// import type { Config } from 'tailwindcss';
import scrollbar from 'tailwind-scrollbar';

module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      screens: {
        'xl-plus': '1440px',
        '3xl': '2560px',
      },
      fontFamily: {
        poppins: ['var(--font-poppins)'],
        sans: ['var(--font-poppins)', 'ui-sans-serif', 'system-ui'],
        mono: ['var(--font-geist-mono)', 'ui-monospace', 'SFMono-Regular'],
      },
    },
  },
  plugins: [scrollbar],
};

// export default config;
