/**
 * Event Type Definitions
 * These types define the structure of event data in the application
 */

// Event status enum for type safety
export type EventStatus = 'Active' | 'Completed' | 'Cancelled' | 'Pending' | 'Synced' | 'SyncFailed' | 'New';

// Event type enum
export type EventType = 'Court Notice' | 'Discovery' | 'Client meeting' | 'Court Appearance' | 'Deposition' | 'Conference';

// Court notice type enum
export type CourtNoticeType = 'Jury Trial' | 'Motion Hearing' | 'Status Conference' | 'Settlement Conference' | 'Trial';

// Workflow Run interface
export interface WorkflowRun {
  id?: string;
  name?: string;
  templateName?: string; // New Court Notice, Court Notice Follow-up
  url: string;
}

// API Response Event interface (matches backend structure)
export interface ApiEvent {
  id: string;
  event_id: string;
  subject: string;
  client_name: string;
  matter: string;
  court_notice_type: string;
  start_time: string;
  end_time: string;
  calendar_status: string;
  location: string;
  attorneys: string;
  workflow_run: string;
  sync_status: string;
  start_date: string;
  end_date: string;
  date: string;
  case_number: string;
  phone_details: string;
  meeting_link: string;
  description: string;
  all_day: boolean;
  is_completed: boolean;
  is_cancelled: boolean;
  optional_attendees: string;
  required_attendees: string;
  client_attendance: string;
  workflow_execution_id: string;
  taskexecution: string;
  last_activity: string;
  courtNoticeActions: string;
  _id: string;
  templateName: string;
}

// Frontend Event interface (normalized for UI)
export interface Event {
  id: string;
  subject: string; // e.g., "Jury Trial; Joe Peter"
  contactName: string; // Full name of the individual
  contactId: string; // Reference to contact/client
  matter: string; // Type of matter name e.g., "DWI"
  courtNoticeType: string; // Event type
  eventType: EventType;
  start: string; // Date and time in MM/DD/YY HH:MM AM/PM format
  end: string; // Date and time in MM/DD/YY HH:MM AM/PM format
  location: string; // Court name or meeting type
  status: EventStatus; // Calendar Status
  attorney: string; // Primary attorney
  attorneys?: string[]; // All attorneys, comma-separated
  workflowRuns: WorkflowRun[]; // Related workflow runs
  syncStatus: 'Synced' | 'Manual' | 'SyncFailed' | 'New'; // Sync status
  matterId?: string; // Optional reference to matter
  description?: string;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
  caseNumber?: string;
  lastActivity?: string;
  courtNoticeActions?: string;
}

// API Request interface
export interface CalendarEventsRequest {
  page: number;
  limit: number;
  userId: number;
  view?: string;
  type: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
}

// API Response interface
export interface CalendarEventsApiResponse {
  statusCode: number;
  message: string;
  data: {
    total: number;
    page: number;
    limit: number;
    fields: ApiEvent[];
    column: Array<{
      fieldName: string;
      isSorting: boolean;
      isFilter: boolean;
      isShowing: boolean;
    }>;
  };
}

// Event list query parameters
export interface EventListParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: EventStatus;
  eventType?: EventType;
  attorney?: string;
  contactId?: string;
  matterId?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Event API response interface
export interface EventListResponse {
  data: Event[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Event form data interface
export interface EventFormData {
  eventType: EventType;
  courtNoticeType?: CourtNoticeType;
  attorney: string;
  start: string;
  end: string;
  location: string;
  status: EventStatus;
  contactId: string;
  matterId?: string;
  description?: string;
  notes?: string;
}

// Event filter interface
export interface EventFilters {
  status: EventStatus[];
  eventType: EventType[];
  attorney: string[];
  dateRange?: {
    start: string;
    end: string;
  };
}

// Event actions interface for bulk operations
export interface EventAction {
  type: 'delete' | 'update_status' | 'reschedule' | 'export';
  eventIds: string[];
  data?: {
    status?: EventStatus;
    newDate?: string;
  };
} 