import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ClioState {
  shouldShowClioMessages: boolean;
}

const initialState: ClioState = {
  shouldShowClioMessages: false,
};

export const clioSlice = createSlice({
  name: 'clio',
  initialState,
  reducers: {
    setShouldShowClioMessages: (state, action: PayloadAction<boolean>) => {
      state.shouldShowClioMessages = action.payload;
    },
    hideClioMessages: (state) => {
      state.shouldShowClioMessages = false;
    },
    showClioMessages: (state) => {
      state.shouldShowClioMessages = true;
    },
  },
});

export const { setShouldShowClioMessages, hideClioMessages, showClioMessages } = clioSlice.actions;
export default clioSlice.reducer;