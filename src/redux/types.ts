// Type definitions for Redux state
export interface CounterState {
  value: number;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
}

export interface SidebarState {
  collapsed: boolean;
}

export interface UserState {
  name: string;
  avatar: string;
  isLoggedIn: boolean;
  isMFAEnabled: boolean;
  isMFAVerified: boolean;
}

export interface ClioState {
  shouldShowClioMessages: boolean;
}

export interface RootState {
  counter: CounterState;
  sidebar: SidebarState;
  user: UserState;
  clio: ClioState;
}
