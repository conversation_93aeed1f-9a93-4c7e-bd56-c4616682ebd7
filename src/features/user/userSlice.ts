import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UserState } from '@/redux/types';

const initialState: UserState = {
  name: 'Guest User',
  avatar: '/avatars/default.png',
  isLoggedIn: false,
  isMFAEnabled: false,
  isMFAVerified: false,
};

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<Partial<UserState>>) => {
      return { ...state, ...action.payload };
    },
    logoutUser: state => {
      state.isLoggedIn = false;
      state.name = 'Guest User';
      state.isMFAEnabled = false;
      state.isMFAVerified = false;
    },
    setMFAEnabled: (state, action: PayloadAction<boolean>) => {
      state.isMFAEnabled = action.payload;
    },
    setMFAVerified: (state, action: PayloadAction<boolean>) => {
      state.isMFAVerified = action.payload;
    },
    resetMFA: state => {
      state.isMFAEnabled = false;
      state.isMFAVerified = false;
    },
  },
});

export const { setUser, logoutUser, setMFAEnabled, setMFAVerified, resetMFA } = userSlice.actions;
export default userSlice.reducer;
