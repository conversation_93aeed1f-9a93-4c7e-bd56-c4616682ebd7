import { baseURL } from '@/services/api/config';
import apiClient from './api/config';
export interface ColumnConfig {
  FieldName: string;
  searchable: boolean;
  sortable: boolean;
  visible: boolean;
}

export interface FilterConfig {
  fieldName: string;
  filter: string; // "equal", "contain", "before", etc.
  value: string[];
}

export interface UserView {
  id: string;
  name: string;
  userId: number;
  type: string; // enum: [my_work_flow, completed, archive, new_court_notice, follow_up]
  filters: Record<string, unknown>;
  columns: ColumnConfig[];
  filter: FilterConfig[];
  is_active: boolean;
  canUpdate?: boolean; // Flag to indicate if view can be updated directly
  createdAt: string;
  updatedAt: string;
}

export interface SaveUserViewRequest {
  name: string;
  type: string; // enum: [my_work_flow, completed, archive, new_court_notice, follow_up]
  userId: number;
  filters?: Record<string, unknown>;
  columns?: ColumnConfig[];
  filter?: FilterConfig[];
}

export interface UpdateUserViewRequest {
  viewId: string;
  name?: string;
  type?: string;
  userId?: number;
  filters?: Record<string, unknown>;
  columns?: ColumnConfig[];
  filter?: FilterConfig[];
}

export interface RenameUserViewRequest {
  viewId: string;
  name: string;
}

export interface DeleteUserViewRequest {
  viewId: string;
}

export interface GetUserViewsResponse {
  success: boolean;
  message: string;
  views: UserView[];
  total: number;
  page: number;
  limit: number;
}

export interface UserViewResponse {
  data: {
    success: boolean;
    message: string;
    view: UserView;
  };
  statusCode: number;
  message: string;
}

export interface DeleteUserViewResponse {
  data: {
    success: boolean;
    message: string;
  };
  statusCode: number;
  message: string;
}

/**
 * User View Service Class
 * Handles all user view related API calls
 */
export class UserViewService {
  private static baseUrl = `${baseURL}/workflow`;

  /**
   * Save a new user view
   */
  static async saveUserView(data: SaveUserViewRequest): Promise<UserViewResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/save-view`, JSON.stringify(data));
      if (response.data.statusCode !== 201) {
        throw new Error(`Failed to save user view: ${response.status}`);
      }
      return await response.data;
    } catch (error) {
      console.error('Error saving user view:', error);
      throw error;
    }
  }

  /**
   * Get all user views for the current user
   */
  static async getUserViews(page = 1, limit = 50): Promise<GetUserViewsResponse> {
    try {
      // Add cache busting parameter
      const cacheBuster = Date.now();
      const response = await apiClient.get(
        `${this.baseUrl}/my-views?page=${page}&limit=${limit}&_t=${cacheBuster}`
      );
      return await response.data.data;
    } catch (error) {
      console.error('Error getting user views:', error);
      throw error;
    }
  }

  /**
   * Update an existing user view
   */
  static async updateUserView(data: UpdateUserViewRequest): Promise<UserViewResponse> {
    try {
      const response = await apiClient.put(`${this.baseUrl}/update-view`, JSON.stringify(data));
      return await response.data;
    } catch (error) {
      console.error('Error updating user view:', error);
      throw error;
    }
  }

  /**
   * Rename a user view
   */
  static async renameUserView(data: RenameUserViewRequest): Promise<UserViewResponse> {
    try {
      const response = await apiClient.put(`${this.baseUrl}/rename-view`, JSON.stringify(data));
      return await response.data;
    } catch (error) {
      console.error('Error renaming user view:', error);
      throw error;
    }
  }

  /**
   * Delete a user view
   */
  static async deleteUserView(data: DeleteUserViewRequest): Promise<DeleteUserViewResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/delete-view`, JSON.stringify(data));
      return await response.data;
    } catch (error) {
      console.error('Error deleting user view:', error);
      throw error;
    }
  }

  /**
   * Get a specific user view by ID
   */
  static async getUserViewById(viewId: string): Promise<UserViewResponse> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/view/${viewId}`);
      return await response.data;
    } catch (error) {
      console.error('Error getting user view:', error);
      throw error;
    }
  }
}

export default UserViewService;
