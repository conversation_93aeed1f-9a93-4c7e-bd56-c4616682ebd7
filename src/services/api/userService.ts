import apiClient from './config';
import { AxiosResponse } from 'axios';

/**
 * User interface for assignee API response
 */
export interface AssigneeUser {
  _id: string;
  value: string;
  text: string;
  is_active: boolean;
}

/**
 * User list API response interface
 */
export interface UserListResponse {
  data: {
    users: AssigneeUser[];
  };
  statusCode: number;
  message: string;
}

/**
 * User API service
 * Handles all user-related API operations
 */
const userService = {
  /**
   * Get user list for assignee dropdown
   * @param search - Search term for filtering users
   * @param userGroupId - User group ID
   * @returns Promise with user list data
   */
  getUserList: async (search: string = '', userGroupId?: string): Promise<UserListResponse> => {
    try {
      const params = new URLSearchParams();
      params.append('search', search);
      if (userGroupId) {
        params.append('user_group_id', userGroupId);
      }

      const response: AxiosResponse<UserListResponse> = await apiClient.get(
        `/workflow/user-list?${params.toString()}`
      );

      return response.data;
    } catch (error) {
      console.error('User List API Error:', error);
      throw error;
    }
  },

  /**
   * Find user by ID in the assignee list
   * @param userId - User ID to find
   * @param userGroupId - Optional user group ID
   * @returns Promise with user data or null if not found
   */
  findUserById: async (userId: string, userGroupId?: string): Promise<AssigneeUser | null> => {
    try {
      const response = await userService.getUserList('', userGroupId);
      const users = response.data.users;

      console.log('🚀 All users from assignee API:', users);
      console.log('🚀 Looking for user ID:', userId);

      // Find user by matching _id with userId
      const foundUser = users.find(user => user._id === userId);
      console.log('🚀 Found user by exact ID match:', foundUser);

      if (foundUser) {
        return foundUser;
      }

      // If exact match not found, try to find by converting types
      const userIdAsString = String(userId);
      const userIdAsNumber = Number(userId);

      const foundByString = users.find(user => user._id === userIdAsString);
      const foundByNumber = users.find(user => user._id === String(userIdAsNumber));

      console.log('🚀 Search attempts:', {
        originalUserId: userId,
        userIdAsString,
        userIdAsNumber,
        foundByString,
        foundByNumber,
      });

      return foundByString || foundByNumber || null;
    } catch (error) {
      console.error('Error finding user by ID:', error);
      return null;
    }
  },
};

export default userService;
