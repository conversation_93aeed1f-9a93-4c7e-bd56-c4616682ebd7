import { apiGet, apiPost } from './apiUtils';
import apiClient from './config';
import { WorkflowResponse } from '@/types/workflow';

/**
 * Workflow API service
 * Handles all workflow-related API operations
 */
const workflowService = {
  /**
   * Get workflows assigned to the current user
   * @param page - Page number for pagination
   * @param limit - Number of items per page
   * @returns Promise with workflow data
   */
  getMyWorkflows: async (page: number = 1, limit: number = 10): Promise<WorkflowResponse> => {
    const response = await apiGet<WorkflowResponse>(
      `/workflow/my-workflow?page=${page}&limit=${limit}`
    );
    return response.data;
  },

  workFlowData: async (
    page: number = 1,
    limit: number = 10,
    userId?: number,
    view?: string,
    type?: string,
    sort_by?: string,
    sort_order?: string,
    filters?: Array<{
      fieldName: string;
      filter: string;
      value: string[];
    }>
  ): Promise<WorkflowResponse> => {
    const payload: Record<string, unknown> = {
      page,
      limit,
      userId,
      view,
      type,
      sort_by,
      sort_order,
    };


    // Helper function to check if field is a matter field
    const isMatterField = (fieldName: string) => {
      const f = (fieldName || '').toLowerCase();
      return f === 'matter' || f === 'matter_name' || f === 'matters';
    };

    // Add filters to payload if provided and extract matter names
    if (filters && filters.length > 0) {
      const processedFilters = filters.map(filter => {
        if (isMatterField(filter.fieldName) && filter.value && filter.value.length > 0) {
          return {
            ...filter,
            value: filter.value, // Keep full values (client|matter) for precise filtering
          };
        }
        return filter;
      });
      payload.filter = processedFilters;
    }

    const response = await apiPost<WorkflowResponse>(`/workflow/court-notice-list`, payload);

    console.log(response, 'RES_DDDD');

    return response.data;
  },
  /**
   * Get a specific workflow by ID
   * @param id - Workflow ID
   * @returns Promise with workflow data
   */
  getWorkflowById: async (id: string) => {
    const response = await apiGet(`/workflow/${id}`);
    return response.data;
  },

  /**
   * Get unique names from work_flow_runner field for dropdown filtering
   * @param type - Workflow type (default: 'new_court_notice')
   * @param userId - User ID (default: 1)
   * @returns Promise with unique names array
   */
  getUniqueNames: async (
    type: string = 'new_court_notice',
    userId: number = 1
  ): Promise<string[]> => {
    const payload = {
      type,
      userId,
    };

    try {
      const response = await apiClient.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/unique-names`,
        JSON.stringify(payload)
      );

      const responseData = await response.data;

      if (responseData.data?.data?.data && Array.isArray(responseData.data.data.data)) {
        return responseData.data.data.data;
      }

      console.warn('Invalid response format for unique names:', responseData);
      return [];
    } catch (error) {
      console.error('Error in getUniqueNames:', error);
      throw error;
    }
  },

  /**
   * Get unique attorney names from attorney field for dropdown filtering
   * @param type - Workflow type (default: 'new_court_notice')
   * @param userId - User ID (default: 1)
   * @returns Promise with unique attorney names array
   */
  getUniqueAttorneys: async (
    type: string = 'new_court_notice',
    userId: number = 1
  ): Promise<string[]> => {
    const payload = {
      type,
      userId,
    };

    try {
      const response = await apiClient.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/unique-attorneys`,
        JSON.stringify(payload)
      );

      const responseData = await response.data;

      if (responseData.data?.data?.data && Array.isArray(responseData.data.data.data)) {
        return responseData.data.data.data;
      }

      console.warn('Invalid response format for unique attorneys:', responseData);
      return [];
    } catch (error) {
      console.error('Error in getUniqueAttorneys:', error);
      throw error;
    }
  },

  /**
   * Get unique client names from work_flow_runner field for dropdown filtering
   * @param type - Workflow type (default: 'new_court_notice')
   * @param userId - User ID (default: 1)
   * @returns Promise with unique client names array
   */
  getUniqueClients: async (
    type: string = 'new_court_notice',
    userId: number = 1
  ): Promise<string[]> => {
    const payload = {
      type,
      userId,
    };

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/unique-clients`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();

      if (responseData.data?.data?.data && Array.isArray(responseData.data.data.data)) {
        return responseData.data.data.data;
      }

      console.warn('Invalid response format for unique clients:', responseData);
      return [];
    } catch (error) {
      console.error('Error in getUniqueClients:', error);
      throw error;
    }
  },
};

export default workflowService;
