import apiClient from './config';
import { AxiosResponse } from 'axios';
import {
  CourtNoticeListResponse,
  CourtNoticeListParams,
  convertStatusArrayToApiFormat,
} from '@/types/courtNotice';

// Cache for storing API responses
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const cache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Request deduplication map
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const pendingRequests = new Map<string, Promise<any>>();

// AbortController map for request cancellation
const abortControllers = new Map<string, AbortController>();

/**
 * Generate cache key from payload
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const generateCacheKey = (payload: any): string => {
  return JSON.stringify(payload);
};

/**
 * Check if cache entry is still valid
 */
const isCacheValid = (timestamp: number): boolean => {
  return Date.now() - timestamp < CACHE_DURATION;
};

/**
 * Court Notice API service
 * Handles all court notice related API operations with caching and deduplication
 */
const courtNoticeService = {
  /**
   * Get court notice list
   * @param params - Payload for the POST request (userId will be automatically added from localStorage)
   * @returns Promise with court notice data
   */
  getCourtNoticeList: async (
    params: Omit<CourtNoticeListParams, 'userId'>,
    signal?: AbortSignal
  ): Promise<CourtNoticeListResponse | null> => {
    try {
      // Get userId from localStorage
      const user = localStorage.getItem('user');
      const userData = user ? JSON.parse(user) : null;
      const userId = userData?.id;

      if (!userId) {
        throw new Error('User ID not found in localStorage');
      }

      // Create POST payload
      const payload: CourtNoticeListParams = {
        ...params,
        userId,
      };

      // Convert sortBy to sort_by and sortOrder to sort_order for API compatibility
      if (params.sortBy) {
        payload.sort_by = params.sortBy;
        delete payload.sortBy;
      }

      if (params.sortOrder) {
        payload.sort_order = params.sortOrder;
        delete payload.sortOrder;
      }


      // Helper function to check if field is a matter field
      const isMatterField = (fieldName: string) => {
        const f = (fieldName || '').toLowerCase();
        return f === 'matter' || f === 'matter_name' || f === 'matters';
      };

      // Convert status values to API format and extract matter names in filters
      if (payload.filter && payload.filter.length > 0) {
        payload.filter = payload.filter.map(filter => {
          if (
            filter.fieldName?.toLowerCase() === 'status' &&
            filter.value &&
            filter.value.length > 0
          ) {
            return {
              ...filter,
              value: convertStatusArrayToApiFormat(filter.value),
            };
          } else if (isMatterField(filter.fieldName) && filter.value && filter.value.length > 0) {
            return {
              ...filter,
              value: filter.value, // Keep full values (client|matter) for precise filtering
            };
          }
          return filter;
        });
      }

      // Generate cache key
      const cacheKey = generateCacheKey(payload);

      // Check cache first
      const cachedEntry = cache.get(cacheKey);
      if (cachedEntry && isCacheValid(cachedEntry.timestamp)) {
        console.log('Returning cached court notice data');
        return cachedEntry.data;
      }

      // Check if there's already a pending request for this payload
      if (pendingRequests.has(cacheKey)) {
        console.log('Waiting for pending request');
        return await pendingRequests.get(cacheKey);
      }

      // Create new request with cancellation support
      const requestPromise = (async () => {
        let controller: AbortController | undefined;
        let combinedController: AbortController | undefined;

        try {
          // Create abort controller for this request
          controller = new AbortController();
          abortControllers.set(cacheKey, controller);

          // Combine with external signal if provided
          const combinedSignal = signal
            ? (() => {
                combinedController = new AbortController();

                // Add abort listeners
                const handleAbort = () => {
                  if (combinedController && !combinedController.signal.aborted) {
                    combinedController.abort();
                  }
                };

                signal.addEventListener('abort', handleAbort);
                controller.signal.addEventListener('abort', handleAbort);

                return combinedController.signal;
              })()
            : controller.signal;

          const response: AxiosResponse<CourtNoticeListResponse> = await apiClient.post(
            '/workflow/court-notice-list',
            payload,
            { signal: combinedSignal }
          );

          const responseData = response.data;

          // Cache the response
          cache.set(cacheKey, {
            data: responseData,
            timestamp: Date.now(),
          });

          console.log(response, 'RES_DDDDCCCCCCCCCC');
          return responseData;
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
          // Check if this is a cancellation error and handle it gracefully
          if (
            error.name === 'CanceledError' ||
            error.name === 'AbortError' ||
            error.message === 'canceled'
          ) {
            console.log('Request was cancelled:', cacheKey);
            // Don't throw cancellation errors, just return null or empty response
            return null;
          }
          throw error;
        } finally {
          // Clean up controllers and pending requests
          if (controller) {
            abortControllers.delete(cacheKey);
          }
          pendingRequests.delete(cacheKey);
        }
      })();

      // Store the pending request
      pendingRequests.set(cacheKey, requestPromise);

      return await requestPromise;
    } catch (error) {
      console.error('CourtNotice API Error:', error);
      throw error;
    }
  },

  /**
   * Get court notice details by ID
   * @param id - Court notice ID
   * @returns Promise with court notice details
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getCourtNoticeById: async (id: string): Promise<any> => {
    const response = await apiClient.get(`/workflow/court-notice/${id}`);
    return response.data;
  },

  /**
   * Get field options for filters (optimized for TableActions)
   * @returns Promise with field names array
   */
  getFieldOptions: async (): Promise<string[]> => {
    try {
      const response = await courtNoticeService.getCourtNoticeList({
        type: 'new_court_notice',
        page: 1,
        limit: 1,
      });

      const first = response?.data?.fields?.[0] || {};
      const keys: string[] = Object.keys(first);

      if (keys.length) {
        // Sort for stable UI
        return keys.sort((a, b) => a.localeCompare(b));
      }

      return [];
    } catch (error) {
      console.warn('Failed to fetch field options:', error);
      return [];
    }
  },

  /**
   * Get distinct values for a specific field (optimized for TableActions)
   * @param field - Field name to get distinct values for
   * @returns Promise with distinct values array
   */
  getDistinctValues: async (field: string): Promise<string[]> => {
    try {
      const response = await courtNoticeService.getCourtNoticeList({
        type: 'new_court_notice',
        page: 1,
        limit: 50,
      });

      const rows: unknown[] = response?.data?.fields || [];
      const set = new Set<string>();

      for (const r of rows) {
        if (typeof r === 'object' && r !== null && field in r) {
          const v = (r as Record<string, unknown>)[field];
          if (v == null) continue;

          if (typeof v === 'string') {
            v.split(',')
              .map(s => s.trim())
              .filter(Boolean)
              .forEach(s => set.add(s));
          } else if (typeof v === 'boolean' || typeof v === 'number') {
            set.add(String(v));
          }
        }
      }

      return Array.from(set).sort((a, b) => a.localeCompare(b));
    } catch (error) {
      console.warn('Failed to fetch distinct values:', error);
      return [];
    }
  },

  /**
   * Cancel a specific request by cache key
   * @param cacheKey - The cache key of the request to cancel
   */
  cancelRequest: (cacheKey: string): void => {
    const controller = abortControllers.get(cacheKey);
    if (controller) {
      controller.abort();
      abortControllers.delete(cacheKey);
      pendingRequests.delete(cacheKey);
    }
  },

  /**
   * Cancel all pending requests
   */
  cancelAllRequests: (): void => {
    abortControllers.forEach(controller => controller.abort());
    abortControllers.clear();
    pendingRequests.clear();
  },

  /**
   * Clear cache (useful for testing or when data needs to be refreshed)
   */
  clearCache: (): void => {
    cache.clear();
    pendingRequests.clear();
    abortControllers.clear();
  },
};

export default courtNoticeService;
