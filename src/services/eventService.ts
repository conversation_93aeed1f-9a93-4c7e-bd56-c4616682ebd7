/**
 * Event Service
 * Handles all API operations related to events
 */

import {
  Event,
  EventListParams,
  EventListResponse,
  WorkflowRun,
  ApiEvent,
  CalendarEventsRequest,
  CalendarEventsApiResponse,
} from '@/types/event';
import apiClient from './api/config';
// import { secureAuthToken } from '@/utils'; // Currently unused

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;
const API_ENDPOINTS = {
  CALENDAR_EVENTS: '/workflow/calendar-events-list',
};

// Utility function to map API event to frontend Event interface
const mapApiEventToEvent = (apiEvent: ApiEvent): Event => {
  // Extract client name from client_name field (format: "CLIENT NAME | Full Details")
  const clientName = apiEvent.client_name.split(' | ')[0] || apiEvent.client_name;

  // Parse attorneys list
  const attorneysList = apiEvent.attorneys ? apiEvent.attorneys.split(', ').filter(Boolean) : [];
  const primaryAttorney = attorneysList.length > 0 ? `${attorneysList[0]}` : '';

  // Get user timezone from localStorage
  const getUserTimezone = (): string => {
    if (typeof window !== 'undefined') {
      return (
        localStorage.getItem('userTimezone') || Intl.DateTimeFormat().resolvedOptions().timeZone
      );
    }
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  };

  // Format date and time with timezone conversion for display
  const formatDateTime = (date: string, time: string): string => {
    if (!date || !time) return '';
    try {
      const [year, month, day] = date.split('-');
      const [hours, minutes] = time.split(':');

      // Create UTC date object (API sends UTC times)
      const utcDateObj = new Date(
        Date.UTC(
          parseInt(year),
          parseInt(month) - 1,
          parseInt(day),
          parseInt(hours),
          parseInt(minutes)
        )
      );

      // Convert to user's timezone for display
      const userTimezone = getUserTimezone();
      return utcDateObj.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
        timeZone: userTimezone,
      });
    } catch {
      return `${date} ${time}`;
    }
  };

  // Create workflow runs from workflow_run field
  const workflowRuns: WorkflowRun[] = [];
  if (apiEvent.workflow_run || apiEvent.templateName) {
    // For now, create a single workflow run entry
    workflowRuns.push({
      id: apiEvent.id + '_workflow',
      name: apiEvent.workflow_run,
      templateName: apiEvent.court_notice_type || 'New Court Notice',
      url: `/workflowrun?taskId=${apiEvent.taskexecution}&work_flow_id=${apiEvent.workflow_execution_id}`,
    });
  }

  // Map sync status
  const syncStatus =
    apiEvent.sync_status === 'Synced'
      ? 'Synced'
      : apiEvent.sync_status === 'SyncFailed'
        ? 'SyncFailed'
        : apiEvent.sync_status === 'New'
          ? 'New'
          : 'Manual';

  // Map calendar status to event status based on current time vs end time (using UTC for accurate comparison)
  const getEventStatus = (): 'Active' | 'Completed' => {
    try {
      // Parse end date and time
      const [endYear, endMonth, endDay] = apiEvent.end_date.split('-');
      const [endHours, endMinutes] = apiEvent.end_time.split(':');

      // Create end datetime object in UTC (API sends UTC times)
      const endDateTimeUTC = new Date(
        Date.UTC(
          parseInt(endYear),
          parseInt(endMonth) - 1,
          parseInt(endDay),
          parseInt(endHours),
          parseInt(endMinutes)
        )
      );

      // Get current datetime in UTC
      const currentDateTimeUTC = new Date();

      // Debug logging can be added here if needed for development

      // Compare current UTC time with end UTC time
      if (currentDateTimeUTC >= endDateTimeUTC) {
        return 'Completed';
      } else {
        return 'Active';
      }
    } catch (error) {
      console.error('Error parsing event datetime:', error);
      // Fallback to original logic if parsing fails
      return apiEvent.is_completed ? 'Completed' : 'Active';
    }
  };

  const status = getEventStatus();

  return {
    id: apiEvent.id,
    subject: apiEvent.subject,
    contactName: clientName,
    contactId: apiEvent.id, // Using the same id for now
    matter: apiEvent.matter,
    courtNoticeType: apiEvent.court_notice_type || '',
    eventType: 'Court Notice',
    start: formatDateTime(apiEvent.start_date, apiEvent.start_time),
    end: formatDateTime(apiEvent.end_date, apiEvent.end_time),
    location: apiEvent.location || '',
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    status: status as any,
    attorney: primaryAttorney,
    attorneys: attorneysList,
    workflowRuns,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    syncStatus: syncStatus as any,
    description: apiEvent.description,
    caseNumber: apiEvent.case_number,
    lastActivity: apiEvent.last_activity,
    courtNoticeActions: apiEvent.courtNoticeActions,
  };
};

/**
 * Event Service Class
 */
export class EventService {
  /**
   * Get events for a specific contact/client
   */
  static async getEventsByContact(contactId: string): Promise<Event[]> {
    // Mock data for demo - in production, this would be an API call
    const mockEvents: Record<string, Event[]> = {
      '1': [
        // Amanda Burke
        {
          id: '1',
          subject: 'Jury Trial; Joe Peter',
          contactName: 'Amanda Burke',
          contactId: '1',
          matter: 'DWI',
          courtNoticeType: 'Jury Trial',
          eventType: 'Court Notice',
          start: '05/21/24 01:30 PM',
          end: '05/21/24 02:30 PM',
          location: 'Zoom',
          status: 'Completed',
          attorney: 'Debra Lee (Primary Attorney)',
          attorneys: ['Debra Lee', 'John Smith'],
          workflowRuns: [
            {
              id: 'wf1',
              name: 'Amanda Burke',
              templateName: 'New Court Notice',
              url: '/workflowrun?taskId=123&work_flow_id=456',
            },
            {
              id: 'wf2',
              name: 'Amanda Burke',
              templateName: 'Court Notice Follow-up',
              url: '/workflowrun?taskId=124&work_flow_id=457',
            },
          ],
          syncStatus: 'Synced',
          matterId: '1',
        },
        {
          id: '2',
          subject: 'Discovery Meeting; Personal Injury Case',
          contactName: 'Amanda Burke',
          contactId: '1',
          matter: 'Personal Injury',
          courtNoticeType: 'Trial',
          eventType: 'Discovery',
          start: '05/28/24 03:30 PM',
          end: '05/28/24 05:00 PM',
          location: 'Hackensack Court',
          status: 'Active',
          attorney: 'Debra Lee (Primary Attorney)',
          attorneys: ['Debra Lee'],
          workflowRuns: [
            {
              id: 'wf3',
              name: 'Amanda Burke',
              templateName: 'New Court Notice',
              url: '/workflowrun?taskId=125&work_flow_id=458',
            },
          ],
          syncStatus: 'Manual',
          matterId: '1',
        },
        {
          id: '3',
          subject: 'Client Consultation; Family Law',
          contactName: 'Amanda Burke',
          contactId: '1',
          matter: 'Family Law',
          courtNoticeType: 'Settlement Conference',
          eventType: 'Client meeting',
          start: '06/10/24 11:30 AM',
          end: '06/10/24 12:00 PM',
          location: 'Zoom',
          status: 'Completed',
          attorney: 'Debra Lee (Primary Attorney)',
          attorneys: ['Debra Lee', 'Sarah Johnson'],
          workflowRuns: [
            {
              id: 'wf4',
              name: 'Amanda Burke',
              templateName: 'Court Notice Follow-up',
              url: '/workflowrun?taskId=126&work_flow_id=459',
            },
          ],
          syncStatus: 'Synced',
          matterId: '2',
        },
      ],
      '2': [
        // Blake Donovan
        {
          id: '4',
          subject: 'Status Conference; Criminal Defense',
          contactName: 'Blake Donovan',
          contactId: '2',
          matter: 'Criminal Defense',
          courtNoticeType: 'Status Conference',
          eventType: 'Court Notice',
          start: '06/25/24 02:00 PM',
          end: '06/25/24 03:30 PM',
          location: 'Manhattan Court',
          status: 'Active',
          attorney: 'Mark Smith (Primary Attorney)',
          attorneys: ['Mark Smith', 'Lisa Wong'],
          workflowRuns: [
            {
              id: 'wf5',
              name: 'Blake Donovan',
              templateName: 'New Court Notice',
              url: '/workflowrun?taskId=127&work_flow_id=460',
            },
            {
              id: 'wf6',
              name: 'Blake Donovan',
              templateName: 'Court Notice Follow-up',
              url: '/workflowrun?taskId=128&work_flow_id=461',
            },
          ],
          syncStatus: 'Synced',
          matterId: '3',
        },
        {
          id: '5',
          subject: 'Motion Hearing; Business Law',
          contactName: 'Blake Donovan',
          contactId: '2',
          matter: 'Business Law',
          courtNoticeType: 'Motion Hearing',
          eventType: 'Court Notice',
          start: '07/01/24 10:00 AM',
          end: '07/01/24 11:30 AM',
          location: 'Quebec Superior Court',
          status: 'Active',
          attorney: 'Mark Smith (Primary Attorney)',
          attorneys: ['Mark Smith'],
          workflowRuns: [
            {
              id: 'wf7',
              name: 'Blake Donovan',
              templateName: 'New Court Notice',
              url: '/workflowrun?taskId=129&work_flow_id=462',
            },
          ],
          syncStatus: 'Manual',
          matterId: '3',
        },
      ],
      '5': [
        // John Donte
        {
          id: '6',
          subject: 'Settlement Conference; Real Estate',
          contactName: 'John Donte',
          contactId: '5',
          matter: 'Real Estate',
          courtNoticeType: 'Settlement Conference',
          eventType: 'Court Notice',
          start: '03/15/22 04:00 PM',
          end: '03/15/22 05:30 PM',
          location: 'Bogan Court',
          status: 'Completed',
          attorney: 'K. Melendez (Primary Attorney)',
          attorneys: ['K. Melendez', 'Robert Davis'],
          workflowRuns: [
            {
              id: 'wf8',
              name: 'John Donte',
              templateName: 'Court Notice Follow-up',
              url: '/workflowrun?taskId=130&work_flow_id=463',
            },
          ],
          syncStatus: 'Synced',
          matterId: '4',
        },
      ],
    };

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));

    return mockEvents[contactId] || [];
  }

  /**
   * Get a single event by ID
   */
  static async getEvent(id: string): Promise<Event | null> {
    // For demo purposes - in production, this would be an API call
    const allEvents = await Promise.all([
      this.getEventsByContact('1'),
      this.getEventsByContact('2'),
      this.getEventsByContact('5'),
    ]);

    const flatEvents = allEvents.flat();
    return flatEvents.find(event => event.id === id) || null;
  }

  /**
   * Get all events with pagination and filters from real API
   */
  static async getEvents(params: EventListParams = {}): Promise<EventListResponse> {
    try {
      const requestPayload: CalendarEventsRequest = {
        page: params.page || 1,
        limit: params.limit || 20,
        userId: 1, // TODO: Get from auth context
        view: 'View_1',
        type: 'calendar_events',
        sort_by: params.sortBy || 'start_date',
        sort_order: params.sortOrder || 'asc',
        ...(params.search && { search: params.search }),
      };

      const response = await apiClient.post(
        `${API_BASE_URL}${API_ENDPOINTS.CALENDAR_EVENTS}`,
        JSON.stringify(requestPayload)
      );

      // Handle the extra wrapper added by TransformInterceptor
      const apiResponse: CalendarEventsApiResponse = response.data.data || response.data;

      if (apiResponse.statusCode !== 201) {
        throw new Error(apiResponse.message || 'API request failed');
      }

      // Map API events to frontend Event interface using the updated interface
      const events = apiResponse.data.fields.map(mapApiEventToEvent);

      return {
        data: events,
        total: apiResponse.data.total,
        page: apiResponse.data.page,
        limit: apiResponse.data.limit,
        totalPages: Math.ceil(apiResponse.data.total / apiResponse.data.limit),
      };
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      // Fallback to mock data in case of API failure
      return this.getMockEvents(params);
    }
  }

  /**
   * Fallback mock data method
   */
  private static async getMockEvents(params: EventListParams = {}): Promise<EventListResponse> {
    const allEvents = await Promise.all([
      this.getEventsByContact('1'),
      this.getEventsByContact('2'),
      this.getEventsByContact('5'),
    ]);

    const flatEvents = allEvents.flat();

    return {
      data: flatEvents,
      total: flatEvents.length,
      page: params.page || 1,
      limit: params.limit || 20,
      totalPages: Math.ceil(flatEvents.length / (params.limit || 20)),
    };
  }

  /**
   * Create a new event
   */
  static async createEvent(eventData: Partial<Event>): Promise<Event> {
    // Mock implementation - in production, this would be an API call
    const newEvent: Event = {
      id: Date.now().toString(),
      subject: '',
      contactName: '',
      contactId: '',
      matter: '',
      courtNoticeType: 'Jury Trial',
      eventType: 'Court Notice',
      start: '',
      end: '',
      location: '',
      status: 'Active',
      attorney: '',
      workflowRuns: [],
      syncStatus: 'Manual',
      ...eventData,
    } as Event;

    await new Promise(resolve => setTimeout(resolve, 200));
    return newEvent;
  }

  /**
   * Update an existing event
   */
  static async updateEvent(id: string, eventData: Partial<Event>): Promise<Event> {
    // Mock implementation - in production, this would be an API call
    const existingEvent = await this.getEvent(id);
    if (!existingEvent) {
      throw new Error('Event not found');
    }

    return {
      ...existingEvent,
      ...eventData,
    };
  }

  /**
   * Delete an event
   */
  static async deleteEvent(): Promise<void> {
    // Mock implementation - in production, this would be an API call
    await new Promise(resolve => setTimeout(resolve, 200));
  }
}

// Export individual methods for convenience
export const { getEventsByContact, getEvent, getEvents, createEvent, updateEvent, deleteEvent } =
  EventService;
