import { Workflow, WorkflowApiResponse } from '@/types/workflow';
import { ContactWorkflow, WorkflowStatus, ContactWorkflowResponse } from '@/types/workflow';
import apiClient from './api/config';
/**
 * Workflow Service
 * Handles all API operations related to workflows
 */

/**
 * Workflow Service Class
 */
export class WorkflowService {
  /**
   * Fetch workflows
   * @param workflowId Optional workflow ID to filter results
   * @returns Promise with workflow data
   */
  async getWorkflows(workflowId?: string): Promise<Workflow[]> {
    try {
      // Build API URL - use the workflowId from the parameter if available
      const apiUrl = workflowId ? `/api/workflows?work_flow_id=${workflowId}` : '/api/workflows';

      const response = await apiClient.get(apiUrl);

      const data: WorkflowApiResponse = await response.data;

      if (data.statusCode === 200 && data.data.workflows.length > 0) {
        return data.data.workflows;
      } else {
        throw new Error('No workflow data available');
      }
    } catch (error) {
      console.error('Error fetching workflow data:', error);
      throw error;
    }
  }

  /**
   * Save workflow task data
   * @param workflowId Workflow ID
   * @param taskId Task ID
   * @param formData Form data to save
   * @returns Promise with save result
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async saveTaskData(
    workflowId: string,
    taskId: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    formData: any
  ) {
    try {
      // Prepare the data payload
      const payload = {
        workflowId,
        taskId,
        formData,
      };

      // Make the API call
      const response = await apiClient.post('/api/workflows/save-task', JSON.stringify(payload));

      return await response.data;
    } catch (error) {
      console.error('Error saving task data:', error);
      throw error;
    }
  }

  /**
   * Update task status
   * @param workflowId Workflow ID
   * @param taskId Task ID
   * @param status New status
   * @returns Promise with update result
   */
  async updateTaskStatus(
    workflowId: string,
    taskId: string,
    status: 'active' | 'pending' | 'completed' | 'skipped' | ''
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ): Promise<any> {
    try {
      const payload = { workflowId, taskId, status };

      const response = await apiClient.put('/api/workflows/update-status', JSON.stringify(payload));

      return await response.data;
    } catch (error) {
      console.error('Error updating task status:', error);
      throw error;
    }
  }

  /**
   * Get workflows for a specific contact
   */
  static async getWorkflowsByContact(contactId: string): Promise<ContactWorkflow[]> {
    // Mock data for demo - in production, this would be an API call
    const mockWorkflows: Record<string, ContactWorkflow[]> = {
      '1': [
        // Amanda Burke
        {
          id: '1',
          workflowRun: 'Joe Smith & Amanda Burke',
          template: 'New Court Notice',
          status: 'Open',
          openDate: '4/15/2024',
          dueDate: '4/15/2024',
          contactId: '1',
          assignee: 'Amanda Burke',
          description: 'New court notice workflow for Amanda Burke case',
        },
        {
          id: '2',
          workflowRun: 'Amanda Burke',
          template: 'Court Notice Followup',
          status: 'Closed',
          openDate: '4/15/2024',
          closedDate: '4/15/2024',
          dueDate: '4/15/2024',
          contactId: '1',
          assignee: 'Amanda Burke',
          description: 'Follow up workflow for court notice processing',
        },
      ],
      '2': [
        // Blake Donovan
        {
          id: '3',
          workflowRun: 'Blake Donovan',
          template: 'Corporate Compliance',
          status: 'On Track',
          openDate: '4/10/2024',
          dueDate: '4/30/2024',
          contactId: '2',
          assignee: 'Mark Smith',
          description: 'Corporate compliance workflow for Blake Donovan',
        },
      ],
      '5': [
        // John Donte
        {
          id: '4',
          workflowRun: 'John Donte',
          template: 'Case Completion',
          status: 'Completed',
          openDate: '2/15/2022',
          closedDate: '3/15/2022',
          dueDate: '3/15/2022',
          contactId: '5',
          assignee: 'K. Melendez',
          description: 'Case completion workflow for John Donte matter',
        },
      ],
    };

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));

    return mockWorkflows[contactId] || [];
  }

  /**
   * Get a single workflow by ID
   */
  static async getWorkflow(id: string): Promise<ContactWorkflow | null> {
    // For demo purposes - in production, this would be an API call
    const allWorkflows = await Promise.all([
      this.getWorkflowsByContact('1'),
      this.getWorkflowsByContact('2'),
      this.getWorkflowsByContact('5'),
    ]);

    const flatWorkflows = allWorkflows.flat();
    return flatWorkflows.find(workflow => workflow.id === id) || null;
  }

  /**
   * Get all workflows with pagination and filters
   */
  static async getWorkflows(
    params: {
      page?: number;
      limit?: number;
      contactId?: string;
      status?: WorkflowStatus;
    } = {}
  ): Promise<ContactWorkflowResponse> {
    // For demo purposes - in production, this would be an API call
    const allWorkflows = await Promise.all([
      this.getWorkflowsByContact('1'),
      this.getWorkflowsByContact('2'),
      this.getWorkflowsByContact('5'),
    ]);

    let filteredWorkflows = allWorkflows.flat();

    // Apply filters
    if (params.contactId) {
      filteredWorkflows = filteredWorkflows.filter(w => w.contactId === params.contactId);
    }

    if (params.status) {
      filteredWorkflows = filteredWorkflows.filter(w => w.status === params.status);
    }

    return {
      data: filteredWorkflows,
      total: filteredWorkflows.length,
      page: params.page || 1,
      limit: params.limit || 50,
    };
  }

  /**
   * Create a new workflow
   */
  static async createWorkflow(workflowData: Partial<ContactWorkflow>): Promise<ContactWorkflow> {
    // Mock implementation - in production, this would be an API call
    const newWorkflow: ContactWorkflow = {
      id: Date.now().toString(),
      workflowRun: '',
      template: '',
      status: 'Open',
      openDate: new Date().toLocaleDateString(),
      dueDate: new Date().toLocaleDateString(),
      contactId: '',
      ...workflowData,
    } as ContactWorkflow;

    await new Promise(resolve => setTimeout(resolve, 200));
    return newWorkflow;
  }

  /**
   * Update an existing workflow
   */
  static async updateWorkflow(
    id: string,
    workflowData: Partial<ContactWorkflow>
  ): Promise<ContactWorkflow> {
    // Mock implementation - in production, this would be an API call
    const existingWorkflow = await this.getWorkflow(id);
    if (!existingWorkflow) {
      throw new Error('Workflow not found');
    }

    const updatedWorkflow = { ...existingWorkflow, ...workflowData };
    await new Promise(resolve => setTimeout(resolve, 200));
    return updatedWorkflow;
  }

  /**
   * Delete a workflow
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  static async deleteWorkflow(id: string): Promise<void> {
    // Mock implementation - in production, this would be an API call
    await new Promise(resolve => setTimeout(resolve, 200));
  }
}

// Export individual methods for convenience
export const {
  getWorkflowsByContact,
  getWorkflow,
  getWorkflows,
  createWorkflow,
  updateWorkflow,
  deleteWorkflow,
} = WorkflowService;

export default new WorkflowService();
