/**
 * Contact Service
 * Handles all API operations related to contacts
 */

import { Contact, ContactListParams, ContactListResponse, ContactFormData } from '@/types/contact';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

// Generic API response type
interface ApiResponse<T> {
  statusCode: number;
  message: string;
  data: T;
}

// API configuration
const apiConfig = {
  headers: {
    'Content-Type': 'application/json',
  },
};

/**
 * Get authorization headers (implement based on your auth system)
 */
const getAuthHeaders = (): Record<string, string> => {
  // In a real application, get the token from localStorage, cookies, or context
  const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
  return token ? { Authorization: `Bearer ${token}` } : {};
};

/**
 * Generic API request wrapper
 */
const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> => {
  const url = `${BASE_URL}${endpoint}`;
  const config: RequestInit = {
    ...options,
    headers: {
      ...apiConfig.headers,
      ...getAuthHeaders(),
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
};

/**
 * Contact Service Class
 */
export class ContactService {
  /**
   * Get list of contacts with pagination and filters
   */
  static async getContacts(params: ContactListParams = {}): Promise<ContactListResponse> {
    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = `/contacts?${queryParams.toString()}`;
    const response = await apiRequest<ContactListResponse>(endpoint);
    return response.data;
  }

  /**
   * Get a single contact by ID
   */
  static async getContact(id: string): Promise<Contact> {
    // For demo purposes, use mock data instead of API
    // In production, use the commented API code

    // Mock data matching the structure from useContacts hook

    const mockContacts: Contact[] = [];

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const contact = mockContacts.find(c => c.id === id);
    if (!contact) {
      throw new Error('Contact not found');
    }

    return contact;

    // Production API code (commented out for demo):
    // const response = await apiRequest<Contact>(`/contacts/${id}`);
    // return response.data;
  }

  /**
   * Create a new contact
   */
  static async createContact(contactData: ContactFormData): Promise<Contact> {
    const response = await apiRequest<Contact>('/contacts', {
      method: 'POST',
      body: JSON.stringify(contactData),
    });
    return response.data;
  }

  /**
   * Update an existing contact
   */
  static async updateContact(id: string, contactData: Partial<ContactFormData>): Promise<Contact> {
    const response = await apiRequest<Contact>(`/contacts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(contactData),
    });
    return response.data;
  }

  /**
   * Delete a contact
   */
  static async deleteContact(id: string): Promise<void> {
    await apiRequest<void>(`/contacts/${id}`, {
      method: 'DELETE',
    });
  }

  /**
   * Bulk update contacts
   */
  static async bulkUpdateContacts(
    contactIds: string[],
    updateData: Partial<ContactFormData>
  ): Promise<Contact[]> {
    const response = await apiRequest<Contact[]>('/contacts/bulk-update', {
      method: 'PUT',
      body: JSON.stringify({
        contactIds,
        updateData,
      }),
    });
    return response.data;
  }

  /**
   * Bulk delete contacts
   */
  static async bulkDeleteContacts(contactIds: string[]): Promise<void> {
    await apiRequest<void>('/contacts/bulk-delete', {
      method: 'DELETE',
      body: JSON.stringify({ contactIds }),
    });
  }

  /**
   * Search contacts
   */
  static async searchContacts(query: string): Promise<Contact[]> {
    const response = await apiRequest<Contact[]>(`/contacts/search?q=${encodeURIComponent(query)}`);
    return response.data;
  }

  /**
   * Get attorneys list for dropdown
   */
  static async getAttorneys(): Promise<string[]> {
    const response = await apiRequest<string[]>('/contacts/attorneys');
    return response.data;
  }

  /**
   * Export contacts to CSV/Excel
   */
  static async exportContacts(format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    const authHeaders = getAuthHeaders();
    const headers: Record<string, string> = { ...authHeaders };

    const response = await fetch(`${BASE_URL}/contacts/export?format=${format}`, {
      headers,
    });

    if (!response.ok) {
      throw new Error('Export failed');
    }

    return response.blob();
  }

  /**
   * Import contacts from CSV/Excel
   */
  static async importContacts(file: File): Promise<{ success: number; errors: string[] }> {
    const formData = new FormData();
    formData.append('file', file);

    const authHeaders = getAuthHeaders();
    const headers: Record<string, string> = { ...authHeaders };

    const response = await fetch(`${BASE_URL}/contacts/import`, {
      method: 'POST',
      headers,
      body: formData,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Import failed');
    }

    return data.data;
  }
}

// Export individual methods for convenience
export const {
  getContacts,
  getContact,
  createContact,
  updateContact,
  deleteContact,
  bulkUpdateContacts,
  bulkDeleteContacts,
  searchContacts,
  getAttorneys,
  exportContacts,
  importContacts,
} = ContactService;
