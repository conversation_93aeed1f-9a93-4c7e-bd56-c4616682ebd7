import { useAppDispatch, useAppSelector } from './index';
import { setMFAEnabled, setMFAVerified, resetMFA } from '@/features/user/userSlice';

/**
 * Custom hook for managing MFA state
 */
export const useMFA = () => {
  const dispatch = useAppDispatch();
  const { isMFAEnabled, isMFAVerified } = useAppSelector(state => state.user);

  const enableMFA = () => {
    dispatch(setMFAEnabled(true));
  };

  const disableMFA = () => {
    dispatch(setMFAEnabled(false));
  };

  const verifyMFA = () => {
    dispatch(setMFAVerified(true));
  };

  const unverifyMFA = () => {
    dispatch(setMFAVerified(false));
  };

  const resetMFAState = () => {
    dispatch(resetMFA());
  };

  return {
    isMFAEnabled,
    isMFAVerified,
    enableMFA,
    disableMFA,
    verifyMFA,
    unverifyMFA,
    resetMFAState,
  };
};
