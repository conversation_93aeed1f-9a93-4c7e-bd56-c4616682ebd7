import { useState, useEffect, useRef, useCallback } from 'react';
import { courtNoticeService } from '@/services/api';
import { CourtNoticeItem, CourtNoticeListParams } from '@/types/courtNotice';

interface UseCourtNoticeListParams extends Omit<CourtNoticeListParams, 'userId'> {
  enabled?: boolean; // Whether to auto-fetch or not
}

interface UseCourtNoticeListReturn {
  data: CourtNoticeItem[];
  loading: boolean;
  error: string | null;
  total: number;
  refetch: () => Promise<void>;
  cancel: () => void;
}

// Global request tracking to prevent duplicate calls across components
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const globalRequestTracker = new Map<string, Promise<any>>();
const globalAbortControllers = new Map<string, AbortController>();

/**
 * Master-level custom hook for court notice list data fetching
 * Implements proper request deduplication, caching, and React best practices
 */
export const useCourtNoticeList = (params: UseCourtNoticeListParams): UseCourtNoticeListReturn => {
  const [data, setData] = useState<CourtNoticeItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);

  // Use ref to track if component is mounted
  const isMountedRef = useRef(true);

  // Generate a stable key for this request
  const requestKey = JSON.stringify({
    ...params,
    enabled: undefined, // Don't include enabled in the key
  });

  // Cancel function
  const cancel = useCallback(() => {
    const controller = globalAbortControllers.get(requestKey);
    if (controller) {
      controller.abort();
      globalAbortControllers.delete(requestKey);
      globalRequestTracker.delete(requestKey);
    }
  }, [requestKey]);

  // Main fetch function
  const fetchData = useCallback(async () => {
    if (!params.enabled && params.enabled !== undefined) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Check if there's already a pending request for this exact params
      if (globalRequestTracker.has(requestKey)) {
        console.log('🔄 Reusing existing request for:', requestKey);
        const existingRequest = globalRequestTracker.get(requestKey);
        const result = await existingRequest;

        // Only update state if component is still mounted
        if (isMountedRef.current) {
          setData(result.data?.fields || []);
          setTotal(result.data?.total || 0);
        }
        return;
      }

      // Create new abort controller for this request
      const controller = new AbortController();
      globalAbortControllers.set(requestKey, controller);

      // Create the request promise
      const requestPromise = courtNoticeService.getCourtNoticeList(
        {
          ...params,
          // enabled: undefined, // Remove enabled from actual API call
        },
        controller.signal
      );

      // Store the promise globally to prevent duplicates
      globalRequestTracker.set(requestKey, requestPromise);

      console.log('🚀 Making new API request for:', requestKey);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const response: any = await requestPromise;

      // Only update state if component is still mounted and this is the latest request
      if (isMountedRef.current) {
        if (response?.statusCode === 200) {
          setData(response.data?.fields || []);
          setTotal(response.data?.total || 0);
        } else {
          setError('Failed to fetch court notice data');
        }
      }
    } catch (err) {
      // Only set error if component is still mounted
      if (isMountedRef.current) {
        // Handle cancellation gracefully
        if (
          err &&
          typeof err === 'object' &&
          (('name' in err && err.name === 'CanceledError') ||
            ('code' in err && err.code === 'ERR_CANCELED') ||
            ('name' in err && err.name === 'AbortError'))
        ) {
          console.log('⏹️ Request was canceled');
          return;
        }

        console.error('❌ API Error:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching data');
      }
    } finally {
      // Clean up global tracking
      globalRequestTracker.delete(requestKey);
      globalAbortControllers.delete(requestKey);

      // Only update loading state if component is still mounted
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [params, requestKey]);

  // Auto-fetch effect
  useEffect(() => {
    if (params.enabled !== false) {
      fetchData();
    }

    // Cleanup function
    return () => {
      cancel();
    };
  }, [fetchData, cancel, params.enabled]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      cancel();
    };
  }, [cancel]);

  return {
    data,
    loading,
    error,
    total,
    refetch: fetchData,
    cancel,
  };
};
