import React, { useState, useEffect, useRef } from 'react';
import { FormField, FormSection } from '../WorkflowField';
import CourtNotice, { ClientMatter, EventType, Matter } from '../CourtNotice';
// import { v4 as uuidv4 } from 'uuid';
import CourtNoticeAddModal from '../modals/CourtNoticeAddModal';
import ReusableAlertModal from '@/components/ui/ReusableAlertModal';
import apiClient from '@/services/api/config';

interface CourtNoticeFieldProps {
  field: FormField;
  onInputChange: (fieldId: string, value: unknown) => void;
  onBlur?: (fieldId: string, value: unknown) => void;
  task_id?: string; // Added for task ID
  section?: FormSection; // Added for section data
  isDisabled?: boolean;
  isChildWorkflow?: boolean;
  apiOptions?: Record<string, unknown>;
  isUpdateMyCase?: boolean;
  work_flow_execution_id?: string;
  isTaskReviewed?: boolean;
  user_group_id?: string;

}

const CourtNoticeField: React.FC<CourtNoticeFieldProps> = ({
  field,
  onInputChange,
  task_id,
  section,
  isDisabled,
  isChildWorkflow,
  apiOptions,
  isUpdateMyCase,
  work_flow_execution_id,
  isTaskReviewed,
  user_group_id,
}) => {
  const [clients, setClients] = useState<ClientMatter[]>([]);
  const [events, setEvents] = useState<Record<string, EventType[]>>({});
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [currentMatterId, setCurrentMatterId] = useState<string>('');
  const [currentClient, setCurrentClient] = useState<ClientMatter | null>(null);
  // Add state for alert modal
  const [alertModalOpen, setAlertModalOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);

  // Create a proper default matter object
  const defaultMatter: Matter = {
    _id: '',
    name: '',
    client_id: '',
    is_active: false,
    client_name: '',
    text: '',
    id: '',
    ex_county_of_arrest: '',
    case_number: '',
  };

  const [selectedMatter, setSelectedMatter] = useState<Matter>(defaultMatter);
  // Ref to track initialization
  const dataInitialized = useRef(false);

  // Extract workflow_id from URL
  // const getWorkflowIdFromUrl = () => {
  //   const { work_flow_id } = router.query;
  //   return work_flow_id ? String(work_flow_id) : '';
  // };

  // Extract court notice data from field value if available

  useEffect(() => {
    if (
      !dataInitialized.current &&
      field.value &&
      Array.isArray(field.value) &&
      field.value.length > 0
    ) {
      const dataItem = field.value[0] as {
        value?: { clients?: ClientMatter[]; events?: Record<string, EventType[]> };
      };
      if (dataItem && dataItem.value) {
        const courtNoticeData = dataItem.value;
        if (courtNoticeData.clients) {
          setClients(courtNoticeData.clients);
        }

        if (courtNoticeData.events) {
          setEvents(courtNoticeData.events);
        }

        dataInitialized.current = true;
      }
    }
  }, [field]);

  // Watch for changes in field.value when isUpdateMyCase changes
  // This ensures the component updates when the form fields are updated after Update My Case
  useEffect(() => {
    if (isUpdateMyCase && field.value && Array.isArray(field.value) && field.value.length > 0) {
      const dataItem = field.value[0] as {
        value?: { clients?: ClientMatter[]; events?: Record<string, EventType[]> };
      };
      if (dataItem && dataItem.value) {
        const courtNoticeData = dataItem.value;
        console.log(
          '🚀 ~ CourtNoticeField ~ Updated court notice data after Update My Case:',
          courtNoticeData
        );

        if (courtNoticeData.clients) {
          setClients(courtNoticeData.clients);
        }

        if (courtNoticeData.events) {
          setEvents(courtNoticeData.events);
        }
      }
    }
  }, [field.value, isUpdateMyCase]);

  // Additional effect to watch for any changes in field.value
  // This ensures the component stays in sync with the parent form data
  useEffect(() => {
    if (field.value && Array.isArray(field.value) && field.value.length > 0) {
      const dataItem = field.value[0] as {
        value?: { clients?: ClientMatter[]; events?: Record<string, EventType[]> };
      };
      if (dataItem && dataItem.value) {
        const courtNoticeData = dataItem.value;

        // Only update if the data has actually changed to avoid infinite loops
        const currentClientsStr = JSON.stringify(clients);
        const currentEventsStr = JSON.stringify(events);
        const newClientsStr = JSON.stringify(courtNoticeData.clients || []);
        const newEventsStr = JSON.stringify(courtNoticeData.events || {});

        if (currentClientsStr !== newClientsStr || currentEventsStr !== newEventsStr) {
          console.log('🚀 ~ CourtNoticeField ~ Field value changed, updating local state');

          if (courtNoticeData.clients) {
            setClients(courtNoticeData.clients);
          }

          if (courtNoticeData.events) {
            setEvents(courtNoticeData.events);
          }
        }
      }
    }
  }, [field.value]);

  // Handle saving court notice data
  const handleSave = async (
    matterId: string,
    updatedEvents: EventType[],
    updatedEvent?: EventType
  ) => {
    // Update local state
    setEvents(prev => ({
      ...prev,
      [matterId]: updatedEvents,
    }));

    // Create the complete data object to save
    const courtNoticeData = {
      clients: clients,
      events: {
        ...events,
        [matterId]: updatedEvents,
      },
    };

    // Call onInputChange to update the form field
    onInputChange(field._id, [{ value: courtNoticeData }]);

    // Call onBlur if provided to trigger save to API
    // if (onBlur) {
    //   onBlur(field._id, [{ value: courtNoticeData }]);
    // }

    // Make the workflow-update API call if we have a task_id and updatedEvent
    if (task_id && updatedEvent) {
      // Find client identifier for the matter - check multiple possible ID fields
      const client = clients.find(client =>
        client.matters.some(
          matter =>
            matter.id === matterId || matter._id === matterId || matter.matter_id === matterId
        )
      );

      console.log('=========1st=client============', client);
      const clientIdentifier = client?.name || 'Client';

      // Get the actual matter_id for API call - check multiple possible ID fields
      let actualMatterId = matterId; // Default fallback
      if (client && client.matters && client.matters.length > 0) {
        const matter = client.matters.find(
          matter =>
            matter.id === matterId || matter._id === matterId || matter.matter_id === matterId
        );
        if (matter) {
          actualMatterId = matter.matter_id || matter._id || matterId;
        }
      }

      // Extract renamed files data from the file objects
      const renamedFilesData = (updatedEvent.files || [])
        .filter(file => file.isNameChanged && file.originalName && file.newName)
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .map((file: any) => ({
          fileId: file.uniqueId || file.id || '',
          originalName: file.originalName!,
          newName: file.newName!,
        }));
      
      console.log('📤 Renamed files data extracted from file objects:', renamedFilesData);

      // Handle appointment cancellation logic
      let appointmentDates = {};
      if (updatedEvent.appointmentAction === 'Cancel' && updatedEvent.appointmentToReschedule) {
        try {
          console.log('🔍 Fetching appointment details for cancellation:', updatedEvent.appointmentToReschedule);
          
          // Call the appointments API to get appointment details
          const appointmentsResponse = await apiClient.get(
            `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/appointments?workflow_id=${work_flow_execution_id}&client_matter_id=${actualMatterId}`
          );
          
          if (appointmentsResponse.data && appointmentsResponse.data.data) {
            // Find the appointment to reschedule by ID
            const appointmentToCancel = appointmentsResponse.data.data.find(
              (appointment: { id?: string; event_id?: string; startDate?: string; endDate?: string }) => 
                appointment.id === updatedEvent.appointmentToReschedule || 
                appointment.event_id === updatedEvent.appointmentToReschedule
            );
            
            if (appointmentToCancel) {
              console.log('📅 Found appointment to cancel:', appointmentToCancel);
              appointmentDates = {
                startDate: appointmentToCancel.startDate,
                endDate: appointmentToCancel.endDate,
              };
              console.log('📅 Extracted dates for cancellation:', appointmentDates);
            } else {
              console.warn('⚠️ Appointment to reschedule not found:', updatedEvent.appointmentToReschedule);
            }
          }
        } catch (error) {
          console.error('❌ Error fetching appointment details:', error);
        }
      }

      // Prepare the API payload
      const apiPayload = {
        workflow_id: work_flow_execution_id,
        task_id: task_id,
        is_completed: false,
        isChildWorkflow: isChildWorkflow,
        forms: [],
        event: {
          derivedFieldId: section?.id || 'court-notice-section',
          clientIdentifier: clientIdentifier,
          newClient: client?.newClient,
          matterId: matterId,
          eventId: updatedEvent.id,
          updateData: {
            ...updatedEvent,
            // Include appointment dates only if it's a cancellation
            ...(updatedEvent.appointmentAction === 'Cancel' ? appointmentDates : {}),
          },
          clientMatterId: actualMatterId,
          // renamedFiles: renamedFilesData, // Include renamed files data
        },
      };
      console.log('🚀 ~ handleSave ~ apiPayload:', apiPayload);
      // console.log('📤 API Payload includes renamed files:', apiPayload.event.renamedFiles?.length || 0, 'files');
      console.log('📤 File objects in updateData.files:', updatedEvent.files?.map(f => ({
        name: f.name,
        uniqueId: f.uniqueId,
        isNameChanged: f.isNameChanged,
        originalName: f.originalName,
        newName: f.newName
      })));

      // Make the API call
      await apiClient
        .put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`, apiPayload)
        .then(response => {
          console.log('Successfully updated court notice event:', response.data);
        })
        .catch(error => {
          console.error('Error updating court notice event:', error);
        });
    }
  };

  // Handle adding a new event for a specific matter
  const handleAddEvent = (matterId: string) => {
    const client = clients.find(c => c.matters.some(m => m.id === matterId));

    if (client) {
      // Find the specific matter
      const selectedMatter = client.matters.find(m => m.id === matterId);
      if (selectedMatter) {
        // Set current client with only the selected matter to ensure
        // it's the one that shows up in the dropdown
        setSelectedMatter(selectedMatter);
        const clientWithSelectedMatter = {
          ...client,
          matters: [selectedMatter],
        };

        // Set current client and matter for the modal
        setCurrentClient(clientWithSelectedMatter);
        setCurrentMatterId(matterId);

        // Open the add modal
        setAddModalOpen(true);
      }
    }
  };

  // Handle form submission from the add modal
  const handleAddNewEvent = (matterId: string, newEvent: EventType) => {
    // Update local state
    const updatedEvents = {
      ...events,
      [currentMatterId]: [...(events[currentMatterId] || []), newEvent],
    };

    setEvents(updatedEvents);

    // Create the complete data object to save
    const courtNoticeData = {
      clients: clients,
      events: updatedEvents,
    };

    // Call onInputChange to update the form field
    onInputChange(field._id, [{ value: courtNoticeData }]);

    // Call onBlur if provided to trigger save to API
    // if (onBlur) {
    //   onBlur(field._id, [{ value: courtNoticeData }]);
    // }

    // Update AdvisorPanel event count if the global function is available
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if (typeof window !== 'undefined' && (window as any).updateAdvisorPanelEventCount) {
      // Use clientName first, fallback to subject if needed
      const eventClientName = newEvent.clientName || newEvent.subject || 'Unknown Client';
      if (eventClientName && eventClientName !== 'Unknown Client') {
        // Increment the count by 1 for new events
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (window as any).updateAdvisorPanelEventCount(eventClientName, 1);
        console.log('🔄 CourtNoticeField: Incremented AdvisorPanel event count for new event:', eventClientName);
      }
    }

    // Make the workflow-update API call if we have a task_id
    if (task_id) {
      // Find client identifier for the matter - check multiple possible ID fields
      const client = clients.find(c =>
        c.matters.some(
          m =>
            m.id === currentMatterId || m._id === currentMatterId || m.matter_id === currentMatterId
        )
      );


      const clientIdentifier = newEvent?.clientName?.split('|')[0] || 'Client';

      // Get the actual matter_id for API call - check multiple possible ID fields
      let actualMatterId = currentMatterId; // Default fallback
      if (client && client.matters && client.matters.length > 0) {
        const matter = client.matters.find(
          matter =>
            matter.id === currentMatterId ||
            matter._id === currentMatterId ||
            matter.matter_id === currentMatterId
        );
        if (matter) {
          actualMatterId = matter.matter_id || matter._id || currentMatterId;
        }
      }

      // Prepare the API payload
      const apiPayload = {
        workflow_id: work_flow_execution_id,
        task_id: task_id,
        is_completed: false,
        forms: [],
        isChildWorkflow: isChildWorkflow,
        event: {
          derivedFieldId: section?.id,
          newClient: client?.newClient,
          clientMatterId: newEvent.selectedMatterId ? newEvent.selectedMatterId : actualMatterId,
          clientIdentifier: clientIdentifier,
          matterId: currentMatterId,
          updateData: newEvent,
        },
      };


      // Make the API call
      apiClient
        .put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`, apiPayload)
        .then(response => {
          console.log('Successfully added new court notice event:', response.data);
        })
        .catch(error => {
          console.error('Error adding new court notice event:', error);
        });
    }
  };

  // Handle deleting an event
  const handleDeleteEvent = (matterId: string, eventId: string) => {
    const updatedEvents = { ...events };

    if (updatedEvents[matterId]) {
      // Find the event to be deleted to get its client name for count update
      const eventToDelete = updatedEvents[matterId].find(event => event.id === eventId);
      
      updatedEvents[matterId] = updatedEvents[matterId].filter(event => event.id !== eventId);

      setEvents(updatedEvents);

      // Create the complete data object to save
      const courtNoticeData = {
        clients: clients,
        events: updatedEvents,
      };

      // Find the client containing this matter - check multiple possible ID fields
      const client = clients.find(client =>
        client.matters.some(
          matter =>
            matter.id === matterId || matter._id === matterId || matter.matter_id === matterId
        )
      );

      const clientIdentifier = client?.name || 'Client';

      // Update AdvisorPanel event count if the global function is available
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if (typeof window !== 'undefined' && (window as any).updateAdvisorPanelEventCount && eventToDelete) {
        // Use clientName first, fallback to subject if needed
        const eventClientName = eventToDelete.clientName || eventToDelete.subject || client?.name || 'Unknown Client';
        if (eventClientName && eventClientName !== 'Unknown Client') {
          // Check if this was the last event for this client
          const remainingEventsCount = updatedEvents[matterId] ? updatedEvents[matterId].length : 0;
          
          if (remainingEventsCount === 0) {
            // Set count to 0 to remove client from notice summary
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (window as any).updateAdvisorPanelEventCount(eventClientName, 0, true); // true indicates absolute set
            console.log('🔄 CourtNoticeField: Set event count to 0 for client (last event deleted):', eventClientName);
          } else {
            // Decrement the count by 1 (using -1)
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (window as any).updateAdvisorPanelEventCount(eventClientName, -1);
            console.log('🔄 CourtNoticeField: Decremented AdvisorPanel event count for:', eventClientName);
          }
        }
      }

      // Get the actual matter_id for API call - check multiple possible ID fields
      let actualMatterId = matterId; // Default fallback
      if (client && client.matters && client.matters.length > 0) {
        const matter = client.matters.find(
          matter =>
            matter.id === matterId || matter._id === matterId || matter.matter_id === matterId
        );
        if (matter) {
          actualMatterId = matter.matter_id || matter._id || matterId;
        }
      }

      // Prepare the API payload
      const apiPayload = {
        workflow_id: work_flow_execution_id,
        task_id: task_id,
        is_completed: false,
        isChildWorkflow: isChildWorkflow,
        forms: [],
        event: {
          derivedFieldId: section?.id || 'court-notice-section',
          clientIdentifier: clientIdentifier,
          matterId: matterId,
          eventId: eventId,
          deleteEvent: true,
          updateData: {},
          clientMatterId: actualMatterId,
        },
      };

      if (actualMatterId) {
        // Make the API call
        apiClient
          .put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`, apiPayload)
          .then(response => {
            console.log('Successfully deleted event:', response.data);
          })
          .catch(error => {
            console.error('Error deleting event:', error);
          });
      } else {
        console.warn(
          'Actual matter ID is undefined, skipping API call for event deletion. Proceeding with local cleanup.'
        );
      }

      // Call onInputChange to update the form field
      onInputChange(field._id, [{ value: courtNoticeData }]);

      // Call onBlur if provided to trigger save to API
      // if (onBlur) {
      //   onBlur(field._id, [{ value: courtNoticeData }]);
      // }
    }
  };

  // Handle deleting a matter
  const handleDeleteMatter = (matterId: string) => {
    // Find the client containing this matter - check multiple possible ID fields
    const client = clients.find(client =>
      client.matters.some(
        matter => matter.matter_id === matterId || matter._id === matterId || matter.id === matterId
      )
    );

    if (!client) {
      setAlertMessage('Cannot find client for this matter. Please refresh the page.');
      setAlertModalOpen(true);
      return;
    }

    // Get clientMatterId - check multiple possible ID fields
    const matter = client.matters.find(
      matter => matter.matter_id === matterId || matter._id === matterId || matter.id === matterId
    );
    if (!matter) {
      return;
    }

    // Use the actual matter_id for the API call, fallback to _id or the provided matterId
    const actualMatterId = matter.matter_id || matter._id || matterId;

    // Update AdvisorPanel to remove this client from notice summary
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if (typeof window !== 'undefined' && (window as any).updateAdvisorPanelEventCount) {
      // Get the client name for the notice summary update
      const clientName = client.name || 'Unknown Client';
      if (clientName && clientName !== 'Unknown Client') {
        // Set count to 0 to remove client from notice summary
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (window as any).updateAdvisorPanelEventCount(clientName, 0, true); // true indicates absolute set
        console.log('🔄 CourtNoticeField: Removed client from notice summary (matter deleted):', clientName);
      }
    }

    // Remove events for this matter from local state
    const updatedEvents = { ...events };
    delete updatedEvents[matterId];
    setEvents(updatedEvents);

    // Prepare the API payload
    const apiPayload = {
      workflow_id: work_flow_execution_id,
      task_id: task_id,
      is_completed: false,
      deleteMatter: true,
      clientMatterId: actualMatterId,
      isChildWorkflow: isChildWorkflow,
      forms: [],
      event: {
        derivedFieldId: section?.id || 'court-notice-section',
        clientIdentifier: client.name,
        matterId: matterId,
        eventId: '',
        deleteEvent: false,
        updateData: {},
        clientMatterId: actualMatterId,
      },
    };

    if (actualMatterId) {
      // Make the API call
      apiClient
        .put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`, apiPayload)
        .then(response => {
          console.log('Successfully deleted matter:', response.data);
        })
        .catch(error => {
          console.error('Error deleting matter:', error);
          setAlertMessage('Failed to delete matter. Please try again.');
          setAlertModalOpen(true);
        });
    }
  };

  // Handle editing an event
  const handleEditEvent = (matterId: string, eventId: string, updatedEvent: EventType) => {
    const updatedEvents = { ...events };

    if (updatedEvents[matterId]) {
      const eventIndex = updatedEvents[matterId].findIndex(event => event.id === eventId);

      if (eventIndex !== -1) {
        updatedEvents[matterId][eventIndex] = updatedEvent;

        setEvents(updatedEvents);

        // Create the complete data object to save
        const courtNoticeData = {
          clients: clients,
          events: updatedEvents,
        };

        // Call onInputChange to update the form field
        onInputChange(field._id, [{ value: courtNoticeData }]);

        // Call onBlur if provided to trigger save to API
        // if (onBlur) {
        //   onBlur(field._id, [{ value: courtNoticeData }]);
        // }
      }
    }
  };

  // Handle client deletion
  const handleDeleteClient = (clientId: string) => {
    // Find the client to be removed
    const clientToRemove = clients.find(c => c.id === clientId);
    if (!clientToRemove) {
      console.error('Client to remove not found:', clientId);
      return;
    }

    // Get the matter IDs associated with this client
    const matterIdsToRemove = new Set(clientToRemove.matters.map(matter => matter.id));

    // Update AdvisorPanel event count when client is deleted
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if (typeof window !== 'undefined' && (window as any).updateAdvisorPanelEventCount) {
      const clientName = clientToRemove.name || 'Unknown Client';
      
      // Set client event count to 0 (absolute set) when client is deleted
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).updateAdvisorPanelEventCount(clientName, 0, true);
      console.log('🔄 CourtNoticeField: Set AdvisorPanel event count to 0 for deleted client:', clientName);
    }

    // Check if client has matters and get the matter_id
    if (clientToRemove.matters && clientToRemove.matters.length > 0) {
      const firstMatter = clientToRemove.matters[0];
      const matterId = firstMatter.matter_id || firstMatter._id;


      if (matterId) {
        handleDeleteMatter(matterId);
      } else {
        console.warn(
          'Matter ID is undefined, skipping API call for matter deletion. Proceeding with local cleanup.'
        );
      }
    }

    // Remove the client from clients array
    const updatedClients = clients.filter(client => client.id !== clientId);
    setClients(updatedClients);

    // Remove events for this client's matters
    const updatedEvents = { ...events };
    Array.from(matterIdsToRemove).forEach(matterId => {
      delete updatedEvents[matterId];
    });
    setEvents(updatedEvents);

    // Update the form field data
    updateFormData(updatedClients, updatedEvents);

    // If we're in a workflow context, also make an API call to delete the client
    if (field._id) {
      const workflowId = field._id.split('-')[0];
      apiClient
        .delete(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/court-notice/client`, {
          data: {
            field_id: field._id,
            workflow_id: workflowId,
            client_id: clientId,
          },
        })
        .then(response => {
          console.log('Successfully deleted client:', response.data);
        })
        .catch(error => {
          console.error('Error deleting client:', error);
          setAlertMessage(
            'Failed to delete client. The UI has been updated but the server may not reflect changes.'
          );
          setAlertModalOpen(true);
        });
    }
  };

  // Handle adding a new contact/client
  const handleAddContact = async (status: boolean) => {
    try {
      // Generate a new client ID
      const newClientId = `client-${Date.now()}-${clients.length + 1}`;

      // Create a new client with no matters initially
      // This allows users to search and select matters through the UI
      const newClient: ClientMatter = {
        id: newClientId,
        name: `Client ${clients.length + 1}`, // Dynamic naming based on client count
        newClient: status,
        matters: [], // Start with empty matters array - user will select through search
      };

      // Update local state
      setClients(prev => [...prev, newClient]);
      // No need to set events since there are no matters yet

      // Create the complete data object to save
      const courtNoticeData = {
        clients: [...clients, newClient],
        events: events, // Keep existing events unchanged
      };

      // Call onInputChange to update the form field
      onInputChange(field._id, [{ value: courtNoticeData }]);

      // If we're in a workflow context, also make an API call
      if (field._id) {
        const workflowId = field._id.split('-')[0];
        await apiClient.post(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/court-notice/client`, {
          field_id: field._id,
          workflow_id: workflowId,
          client: newClient,
        });
      }
    } catch (error) {
      console.error('Error adding new contact:', error);
      // Show error alert using the modal instead of alert()
      setAlertMessage('Failed to add new contact. Please try again.');
      setAlertModalOpen(true);
    }
  };

  // Handle client updates (when matters are added/modified)
  const handleClientsUpdate = (updatedClients: ClientMatter[]) => {
    setClients(updatedClients);

    // Update the form field data
    updateFormData(updatedClients, events);
  };

  // Function to update the form field data
  const updateFormData = (
    updatedClients: ClientMatter[],
    updatedEvents: Record<string, EventType[]>
  ) => {
    const courtNoticeData = {
      clients: updatedClients,
      events: updatedEvents,
    };
    onInputChange(field._id, [{ value: courtNoticeData }]);
  };
  return (
    <>
      <CourtNotice
        clients={clients}
        events={events}
        setClients={setClients}
        setEvents={setEvents}
        onSave={handleSave}
        onAddEvent={handleAddEvent}
        onDeleteEvent={handleDeleteEvent}
        onDeleteMatter={handleDeleteMatter}
        onEditEvent={handleEditEvent}
        onAddContact={isChildWorkflow ? undefined : handleAddContact}
        onDeleteClient={handleDeleteClient}
        onClientsUpdate={handleClientsUpdate}
        label={field.label || 'Review the court notice and ensure all the dates are correct.'}
        fieldId={field._id}
        isChildWorkflow={isChildWorkflow}
        // If field is part of a workflow, extract workflow ID from the field ID
        workflowId={work_flow_execution_id}
        isDisabled={isDisabled}
        options={apiOptions}
        isUpdateMyCase={isUpdateMyCase}
        isTaskReviewed={isTaskReviewed}
        user_group_id={user_group_id}
      />

      {/* Add Modal */}
      <CourtNoticeAddModal
        isOpen={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        clientId={currentClient?.id || ''}
        client={currentClient}
        onAdd={handleAddNewEvent}
        options={apiOptions}
        workflow_id={work_flow_execution_id}
        isChildWorkflow={isChildWorkflow}
        titleOfEvent={'Add a new event'}
        clientName={currentClient?.matters[0].client_name || ''}
        selectedMatter={selectedMatter}
        user_group_id={user_group_id}
      />

      {/* Alert Modal */}
      <ReusableAlertModal
        isOpen={alertModalOpen}
        message={alertMessage}
        onOk={() => setAlertModalOpen(false)}
      />

      {/* Confirm Modal */}
      <ReusableAlertModal
        isOpen={confirmModalOpen}
        message="Are you sure you want to delete this item?"
        title="Confirm Deletion"
        onOk={() => {
          // deleteItem();
          setConfirmModalOpen(false);
        }}
        onCancel={() => setConfirmModalOpen(false)}
        okText="Delete"
        cancelText="Cancel"
      />
    </>
  );
};

export default CourtNoticeField;
