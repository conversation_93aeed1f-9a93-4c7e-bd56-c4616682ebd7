import React, { useState, useCallback, useEffect } from 'react';
import Link, { LinkProps } from 'next/link';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';
import { MoreHorizontal, Edit, Trash2 } from 'lucide-react';
import Image from 'next/image';
import { usePathname, useSearchParams, useRouter } from 'next/navigation';
import { UserViewService, UserView } from '@/services/userViewService';
import RenameViewModal from '@/components/common/RenameViewModal';
import DeleteViewModal from '@/components/common/DeleteViewModal';
import { toast } from 'react-hot-toast';

// Types
interface MenuItemProps {
  href: LinkProps['href'];
  icon?: string;
  children: React.ReactNode;
  className?: string;
  indent?: boolean;
  variant?: 'default' | 'primary';
}

interface CollapsibleSectionProps {
  title: string;
  isCollapsed: boolean;
  onToggle: () => void;
  children: React.ReactNode;
  icon?: string;
  variant?: 'header' | 'folder';
}

interface ViewMenuProps {
  view: UserView;
  onRename: (view: UserView) => void;
  onDelete: (view: UserView) => void;
  isOpen: boolean;
  onToggle: () => void;
}

interface WorkflowSidebarProps {
  className?: string;
}

interface CollapsedSections {
  companyEvents: boolean;
  companyViews: boolean;
  legalOperations: boolean;
  allWorkflows: boolean;
  myViews: boolean;
}

// Constants
const STORAGE_KEY = 'workflow-sidebar-state';
const DEFAULT_COLLAPSED_STATE: CollapsedSections = {
  companyEvents: false,
  companyViews: true,
  legalOperations: true,
  allWorkflows: false,
  myViews: false,
};

// Utility functions for localStorage
const getStoredState = (): CollapsedSections => {
  if (typeof window === 'undefined') {
    return DEFAULT_COLLAPSED_STATE;
  }

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      // Ensure all required keys exist
      return {
        companyEvents: parsed.companyEvents ?? DEFAULT_COLLAPSED_STATE.companyEvents,
        companyViews: parsed.companyViews ?? DEFAULT_COLLAPSED_STATE.companyViews,
        legalOperations: parsed.legalOperations ?? DEFAULT_COLLAPSED_STATE.legalOperations,
        allWorkflows: parsed.allWorkflows ?? DEFAULT_COLLAPSED_STATE.allWorkflows,
        myViews: parsed.myViews ?? DEFAULT_COLLAPSED_STATE.myViews,
      };
    }
  } catch (error) {
    console.warn('Failed to parse stored sidebar state:', error);
  }

  return DEFAULT_COLLAPSED_STATE;
};

const setStoredState = (state: CollapsedSections): void => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
  } catch (error) {
    console.warn('Failed to store sidebar state:', error);
  }
};

// Reusable MenuItem Component
const MenuItem: React.FC<MenuItemProps> = ({
  href,
  icon,
  children,
  className = '',
  indent = false,
  variant = 'default',
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isActive = React.useMemo(() => {
    if (typeof href === 'string') {
      return pathname === href;
    }

    const targetPath = href.pathname?.toString() ?? '';
    if (pathname !== targetPath) return false;

    const query = (href.query ?? {}) as Record<string, string | string[]>;
    const queryKeys = Object.keys(query);

    if (queryKeys.length === 0) return true;

    if (!searchParams) return false;

    for (const key of queryKeys) {
      const expected = query[key];
      const current = searchParams.get(key);
      if (Array.isArray(expected)) {
        if (!expected.includes(current ?? '')) return false;
      } else {
        if ((current ?? '') !== String(expected)) return false;
      }
    }
    return true;
  }, [pathname, href, searchParams]);

  const getItemClasses = () => {
    const baseClasses = 'flex items-center gap-2 text-sm mb-2';
    const indentClasses = indent ? 'pl-2' : '';
    const variantClasses = variant === 'primary' ? 'px-2 py-2' : 'p-2';
    const activeClasses = isActive
      ? 'bg-[#F3F5F9] text-[#3F73F6] rounded-[12px]'
      : 'text-[#5F6F84] hover:bg-[#F3F5F9] hover:rounded-[12px]';

    return `${baseClasses} ${variantClasses} ${indentClasses} ${activeClasses} ${className}`;
  };

  const getSpanClasses = () => {
    const baseClasses = 'w-full font-medium';
    const paddingClasses = variant === 'primary' ? '' : '';
    // const activeClasses = isActive
    //   ? 'bg-[#F3F5F9] text-[#3F73F6] rounded-[12px]'
    //   : 'text-[#5F6F84] hover:bg-gray-50 hover:rounded-[12px]';

    return `${baseClasses} ${paddingClasses}`;
  };

  // Prevent event propagation to avoid triggering parent handlers
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  return (
    <Link href={href} className={getItemClasses()} onClick={handleClick}>
      {icon && <Image src={icon} alt="icon" width={16} height={16} />}
      <span className={getSpanClasses()}>{children}</span>
    </Link>
  );
};

// Reusable Collapsible Section Component
const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  isCollapsed,
  onToggle,
  children,
  icon,
  variant = 'header',
}) => {
  const getButtonClasses = () => {
    const baseClasses = 'w-full flex items-center justify-between text-sm rounded-[12px]';

    if (variant === 'header') {
      return `${baseClasses} px-2 font-medium text-[#5F6F84]`;
    }

    return `${baseClasses} gap-2 p-2 text-[#5F6F84] hover:bg-[#F3F5F9]${isCollapsed ? 'text-[#2A2E34]' : ''
      }`;
  };

  const getTitleClasses = () => {
    if (variant === 'header') {
      return `font-medium text-[14px] ${isCollapsed ? '' : 'text-[#2A2E34]'}`;
    }

    return `${isCollapsed ? '' : 'text-[#2A2E34]'} font-medium`;
  };

  const renderIcon = () => {
    if (!icon) return null;

    const iconSrc = isCollapsed ? '/folder.svg' : '/folder-dark.svg';
    return <Image src={iconSrc} alt={title.toLowerCase()} width={16} height={16} />;
  };

  const renderChevron = () => {
    return isCollapsed ? <FiChevronDown size={16} /> : <FiChevronUp size={16} />;
  };

  // Ensure only the button click triggers the toggle
  const handleToggle = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      onToggle();
    },
    [onToggle]
  );

  // Prevent clicks on the content area from propagating
  const handleContentClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  return (
    <div onClick={handleContentClick}>
      <button className={`${getButtonClasses()} ${variant === 'folder' ? 'mb-2' : 'mb-4'}`} onClick={handleToggle} type="button">
        {variant === 'folder' ? (
          <div className="flex items-center gap-2">
            {renderIcon()}
            <span className={getTitleClasses()}>{title}</span>
          </div>
        ) : (
          <span className={getTitleClasses()}>{title}</span>
        )}
        {renderChevron()}
      </button>

      {!isCollapsed && <div>{children}</div>}
    </div>
  );
};

// ViewMenu Component
const ViewMenu: React.FC<ViewMenuProps> = ({ view, onRename, onDelete, isOpen, onToggle }) => {
  const buttonRef = React.useRef<HTMLButtonElement>(null);
  const [menuPosition, setMenuPosition] = React.useState({ top: 0, left: 0 });

  React.useEffect(() => {
    if (isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setMenuPosition({
        top: rect.bottom + 4, // 4px below the button
        left: rect.left, // Align with left edge of button
      });
    }
  }, [isOpen]);

  return (
    <div className="relative flex-shrink-0">
      <button
        ref={buttonRef}
        onClick={e => {
          e.preventDefault();
          e.stopPropagation();
          onToggle();
        }}
        className="p-1 rounded-[4px] hover:bg-gray-200 transition-colors flex items-center justify-center"
      >
        <MoreHorizontal size={14} className="text-[#5F6F84]" />
      </button>

      {isOpen && (
        <>
          {/* Backdrop to close menu */}
          <div className="fixed inset-0 z-[50]" onClick={onToggle} />
          {/* Menu */}
          <div
            className="fixed bg-white border border-[#DCE2EB] rounded-[12px] shadow-lg z-[9999]"
            style={{
              width: '200px',
              height: '92px',
              paddingTop: '8px',
              paddingBottom: '8px',
              gap: '4px',
              top: `${menuPosition.top}px`,
              left: `${menuPosition.left}px`,
            }}
          >
            <button
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                onRename(view);
                onToggle();
              }}
              className="flex items-center gap-3 w-full px-4 py-2 text-sm text-[#5F6F84] hover:bg-[#F8F9FA] transition-colors"
            >
              <Edit size={16} className="text-[#5F6F84]" />
              Rename
            </button>
            <button
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                onDelete(view);
                onToggle();
              }}
              className="flex items-center gap-3 w-full px-4 py-2 text-sm text-[#DC3545] hover:bg-[#FFF5F5] transition-colors"
            >
              <Trash2 size={16} className="text-[#DC3545]" />
              Delete
            </button>
          </div>
        </>
      )}
    </div>
  );
};

// Main Sidebar Component
const WorkflowSidebar: React.FC<WorkflowSidebarProps> = ({ className = '' }) => {
  const pathname = usePathname();
  const router = useRouter();

  // State management with localStorage persistence
  const [collapsedSections, setCollapsedSections] =
    useState<CollapsedSections>(DEFAULT_COLLAPSED_STATE);

  // State for user views
  const [userViews, setUserViews] = useState<UserView[]>([]);
  const [loadingViews, setLoadingViews] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // State for view menu and modals
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const [renameModalOpen, setRenameModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedView, setSelectedView] = useState<UserView | null>(null);
  const [isRenaming, setIsRenaming] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Initialize state from localStorage on client mount
  useEffect(() => {
    setIsClient(true);
    const storedState = getStoredState();
    setCollapsedSections(storedState);
  }, []);

  // Function to fetch user views
  const fetchUserViews = async () => {
    try {
      setLoadingViews(true);
      console.log('Fetching user views...');
      // Add cache busting parameter
      const response = await UserViewService.getUserViews();
      console.log('User views response:', response);
      if (response.success) {
        console.log('Setting user views:', response.views);
        setUserViews(response.views || []);
      } else {
        console.log('Response data structure:', response);
        setUserViews([]);
      }
    } catch (error) {
      console.error('Error fetching user views:', error);
      setUserViews([]);
    } finally {
      setLoadingViews(false);
    }
  };

  // Fetch user views on component mount
  useEffect(() => {
    fetchUserViews();
  }, []); // Run only once on mount

  // Expose refresh function globally
  useEffect(() => {
    (window as unknown as { refreshSidebarViews?: () => void }).refreshSidebarViews =
      fetchUserViews;
    return () => {
      delete (window as unknown as { refreshSidebarViews?: () => void }).refreshSidebarViews;
    };
  }, []);

  // Update localStorage whenever state changes
  useEffect(() => {
    if (isClient) {
      setStoredState(collapsedSections);
    }
  }, [collapsedSections, isClient]);

  // Handlers with proper isolation and persistence
  const handleCompanyEventsToggle = useCallback(() => {
    setCollapsedSections(prev => ({
      ...prev,
      companyEvents: !prev.companyEvents,
    }));
  }, []);

  const handleCompanyViewsToggle = useCallback(() => {
    setCollapsedSections(prev => ({
      ...prev,
      companyViews: !prev.companyViews,
    }));
  }, []);

  const handleLegalOperationsToggle = useCallback(() => {
    setCollapsedSections(prev => ({
      ...prev,
      legalOperations: !prev.legalOperations,
    }));
  }, []);

  const handleAllWorkflowsToggle = useCallback(() => {
    setCollapsedSections(prev => ({
      ...prev,
      allWorkflows: !prev.allWorkflows,
    }));
  }, []);

  const handleMyViewsToggle = useCallback(() => {
    setCollapsedSections(prev => ({
      ...prev,
      myViews: !prev.myViews,
    }));
  }, []);

  // Handlers for view menu actions
  const handleRename = useCallback((view: UserView) => {
    console.log('Rename clicked for view:', view.name);
    setSelectedView(view);
    setRenameModalOpen(true);
    setOpenMenuId(null);
  }, []);

  const handleDelete = useCallback((view: UserView) => {
    console.log('Delete clicked for view:', view.name);
    setSelectedView(view);
    setDeleteModalOpen(true);
    setOpenMenuId(null);
  }, []);

  const handleRenameSubmit = async (newName: string) => {
    if (!selectedView) return;

    try {
      setIsRenaming(true);
      const response = await UserViewService.renameUserView({
        viewId: selectedView.id,
        name: newName,
      });

      if (response.data.success) {
        setUserViews(views =>
          views.map(v => (v.id === selectedView.id ? { ...v, name: newName } : v))
        );

        // Emit event to notify other components about the rename
        const event = new CustomEvent('viewRenamed', {
          detail: { viewId: selectedView.id, newName },
        });
        window.dispatchEvent(event);

        toast.success('View renamed successfully');
        setRenameModalOpen(false);
        setSelectedView(null);
      } else {
        toast.error('Failed to rename view');
      }
    } catch (error) {
      console.error('Error renaming view:', error);
      toast.error('Failed to rename view');
    } finally {
      setIsRenaming(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedView) return;

    try {
      setIsDeleting(true);
      const response = await UserViewService.deleteUserView({
        viewId: selectedView.id,
      });

      if (response.data.success) {
        const updatedViews = userViews.filter(v => v.id !== selectedView.id);
        setUserViews(updatedViews);

        // Check if we're currently on the deleted view's page
        const isOnDeletedViewPage = pathname === `/views/${selectedView.id}`;

        if (isOnDeletedViewPage) {
          // Navigate to the next available view or default page
          if (updatedViews.length > 0) {
            // Navigate to the first remaining saved view
            router.push(`/views/${updatedViews[0].id}`);
          } else {
            // No saved views left, redirect to new-court-notice page
            router.push('/all-work-flow?type=new-court-notice');
          }
        }

        toast.success('View deleted successfully');
        setDeleteModalOpen(false);
        setSelectedView(null);
      } else {
        toast.error('Failed to delete view');
      }
    } catch (error) {
      console.error('Error deleting view:', error);
      toast.error('Failed to delete view');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleModalClose = useCallback(() => {
    setRenameModalOpen(false);
    setDeleteModalOpen(false);
    setSelectedView(null);
  }, []);

  // Menu items data with stable references
  const companyEventsItems = [
    {
      id: 'calendar',
      href: { pathname: '/events' },
      label: 'Calendar',
    },
  ];

  const primaryMenuItems = [
    {
      id: 'workflows',
      href: { pathname: '/all-work-flow', query: { type: 'my-workflow' } },
      icon: '/assets/workflow.svg',
      label: 'My Workflows',
    },
    {
      id: 'tasks',
      href: { pathname: '/all-work-flow', query: { type: 'my-tasks' } },
      icon: '/assets/task.svg',
      label: 'My Tasks',
    },
    {
      id: 'inbound',
      href: { pathname: '/all-work-flow', query: { type: 'inbound' } },
      icon: '/assets/inboud.svg',
      label: 'Inbound',
    },
  ];

  const legalOperationsItems = [
    {
      id: 'new-court-notice',
      href: { pathname: '/all-work-flow', query: { type: 'new-court-notice' } },
      label: 'New Court Notice',
    },
    {
      id: 'court-notice-followup',
      href: { pathname: '/all-work-flow', query: { type: 'court-notice-follow-up' } },
      label: 'Court Notice Follow up',
    },
  ];

  const allWorkflowsItems = [
    {
      id: 'completed',
      href: { pathname: '/all-work-flow', query: { type: 'completed' } },
      label: 'Completed',
    },
    {
      id: 'archived',
      href: { pathname: '/all-work-flow', query: { type: 'archived' } },
      label: 'Archived',
    },
  ];

  // Prevent any clicks on the sidebar container from propagating
  const handleSidebarClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  // Check if we're on the events/calendar page or workflows page
  const isEventsPage = pathname === '/events';
  const isWorkflowsPage = pathname === '/all-work-flow';

  return (
    <div
      className={`h-[calc(100vh-60px)] px-[22px] py-[30px] bg-white border-r border-[#DCE2EB] w-[260px] overflow-y-auto ${className}`}
      style={{
        width: '260px',
        borderRight: '1px solid #DCE2EB',
      }}
      onClick={handleSidebarClick}
    >
      <div className="flex flex-col">
        {/* COMPANY EVENTS Section - Hidden when on workflows page */}
        {!isWorkflowsPage && (
          <div className="mb-[16px]">
            <CollapsibleSection
              title="COMPANY EVENTS"
              isCollapsed={collapsedSections.companyEvents}
              onToggle={handleCompanyEventsToggle}
              variant="header"
            >
              {companyEventsItems.map((item) => (
                <MenuItem key={item.id} href={item.href} variant="primary">
                  {item.label}
                </MenuItem>
              ))}
            </CollapsibleSection>
          </div>
        )}

        {/* COMPANY VIEWS Section - Hidden when on events page */}
        {!isEventsPage && (
          <div>
            <CollapsibleSection
              title="COMPANY VIEWS"
              isCollapsed={collapsedSections.companyViews}
              onToggle={handleCompanyViewsToggle}
              variant="header"
            >
              {/* Primary Menu Items */}
              {primaryMenuItems.map((item) => (
                <MenuItem key={item.id} href={item.href} icon={item.icon} variant="primary">
                  {item.label}
                </MenuItem>
              ))}

              {/* Legal Operations Submenu */}
              <CollapsibleSection
                title="Legal Operations"
                isCollapsed={collapsedSections.legalOperations}
                onToggle={handleLegalOperationsToggle}
                icon="/folder.svg"
                variant="folder"
              >
                {legalOperationsItems.map((item) => (
                  <MenuItem key={item.id} href={item.href} indent className="ml-[24px]">
                    {item.label}
                  </MenuItem>
                ))}
              </CollapsibleSection>

              {/* All Workflows Submenu */}
              <CollapsibleSection
                title="All Workflows"
                isCollapsed={collapsedSections.allWorkflows}
                onToggle={handleAllWorkflowsToggle}
                icon="/folder.svg"
                variant="folder"
              >
                {allWorkflowsItems.map((item) => (
                  <MenuItem key={item.id} href={item.href} indent className="ml-[24px]">
                    {item.label}
                  </MenuItem>
                ))}
              </CollapsibleSection>
            </CollapsibleSection>
          </div>
        )}

        {/* MY EVENTS Section */}
        <div>
          <CollapsibleSection
            title="MY EVENTS"
            isCollapsed={collapsedSections.myViews}
            onToggle={handleMyViewsToggle}
            variant="header"
          >
            {loadingViews ? (
              <div className="px-4 py-2 text-sm text-gray-500">Loading views...</div>
            ) : userViews.length > 0 ? (
              userViews.map(view => {
                const isActive = pathname === `/views/${view.id}`;

                return (
                  <div key={view.id} className="group relative ml-[24px]">
                    <Link
                      href={`/views/${view.id}`}
                      className={`w-full flex items-center gap-2 text-sm mb-[8px] rounded-[12px] transition-colors ${isActive
                        ? 'bg-[#F3F5F9] text-[#3F73F6]'
                        : 'text-[#5F6F84] hover:bg-[#F8F9FA]'
                        }`}
                      style={{
                        height: '36px',
                        padding: '8px',
                        gap: '8px',
                      }}
                    >
                      <span
                        className="truncate flex-1 font-medium"
                        title={view.name.length > 20 ? view.name : undefined}
                        style={{
                          maxWidth: '140px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {view.name}
                      </span>
                      <div
                        className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={e => e.preventDefault()}
                      >
                        <ViewMenu
                          view={view}
                          onRename={handleRename}
                          onDelete={handleDelete}
                          isOpen={openMenuId === view.id}
                          onToggle={() => setOpenMenuId(openMenuId === view.id ? null : view.id)}
                        />
                      </div>
                    </Link>
                  </div>
                );
              })
            ) : (
              <div className="px-4 py-2 text-sm text-gray-500">No saved views</div>
            )}
          </CollapsibleSection>
        </div>
      </div>

      {/* Modals */}
      <RenameViewModal
        isOpen={renameModalOpen}
        onClose={handleModalClose}
        onSave={handleRenameSubmit}
        currentName={selectedView?.name || ''}
        isLoading={isRenaming}
      />

      <DeleteViewModal
        isOpen={deleteModalOpen}
        onClose={handleModalClose}
        onConfirm={handleDeleteConfirm}
        _viewName={selectedView?.name || ''}
        isLoading={isDeleting}
      />
    </div>
  );
};

export default WorkflowSidebar;
