/* eslint-disable no-constant-condition */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, AlertCircle, LoaderCircle, X } from 'lucide-react';
import { workflowStyles, fieldTypes, conditionalDisabled } from '@/styles/workflow';
import Image from 'next/image';
import CourtNoticeField from './fields/CourtNoticeField';
// import { CustomCheckbox } from '@/components/common';
import ReusableAlertModal from '@/components/ui/ReusableAlertModal';
import Loader from '../ui/Loader';
import apiClient from '@/services/api/config';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/types';
import { showClioMessages } from '@/redux/slices/clioSlice';

// Define validation type
type Validation = {
  minLength?: number;
  min?: number;
  max?: number;
  pattern?: string;
  errorMessage?: string;
};

// Define select option type
interface SelectOption {
  value: string;
  text: string;
  id?: string;
}

// Define the interfaces for workflow data types
export interface FormField {
  type: string;
  placeholder?: string;
  id: string;
  _id: string;
  label?: string;
  value?: string | number | boolean | SelectOption[] | Record<string, unknown> | unknown[];
  options?: SelectOption[] | unknown[];
  required?: boolean;
  validation?: Validation;
  form_field_id?: string;
  apiEndpoint?: string; // Add API endpoint for searchable select
}

// Define condition type
type Condition =
  | {
    field: string;
    value: string | boolean | number;
  }
  | {
    type: 'AND' | 'OR';
    conditions: Array<Condition>;
  };

export interface FormSection {
  id?: string;
  _id?: string;
  type: string;
  label: string;
  fields: FormField[];
  condition?: Condition;
  dynamic_fields?: boolean;
  required?: boolean;
}

interface WorkflowFieldProps {
  section: FormSection;
  onInputChange: (fieldId: string, value: unknown) => void;
  onBlur?: (fieldId: string, value: unknown) => void;
  formData: Record<string, unknown>;
  task_id?: string;
  work_flow_id: string;
  isDisabled?: boolean;
  isChildWorkflow?: boolean;
  isTaskReviewed?: boolean;
  apiOptions?: any;
  setFormFields?: {
    (newFields: FormSection[]): void;
    (updater: (prevFields: FormSection[]) => FormSection[]): void;
  };
  formFields?: FormSection[];
  work_flow_execution_id?: string;
  user_group_id?: string;
  assignUserToTask?: (userId: string, isdeSelected?: boolean) => Promise<void>;
  removeAssignToItem?: (userId: string, isdeSelected?: boolean) => Promise<void>;
  setAssignToSelectedValues?: React.Dispatch<React.SetStateAction<string[]>>;
  assignToSelectedValues?: string[]; // Add this prop to receive the current assign to values
}

// Custom TextareaAutosize component that automatically adjusts height based on content
interface TextareaAutosizeProps {
  fieldId: string;
  placeholder?: string;
  className?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  disabled?: boolean;
}

const TextareaAutosize: React.FC<TextareaAutosizeProps> = ({
  fieldId,
  placeholder,
  className,
  value,
  onChange,
  onBlur,
  disabled,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    localStorage.removeItem('SelectedEventMatterId');
    localStorage.removeItem('isEventAdded');
  }, []);

  // Function to adjust the height of the textarea
  const adjustHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto';
      // Set the height to match the content (with a minimum height)
      const minHeight = 120;
      const scrollHeight = Math.max(textarea.scrollHeight, minHeight);
      textarea.style.height = `${scrollHeight}px`;
    }
  };

  // Adjust height on initial render and when value changes
  useEffect(() => {
    adjustHeight();
  }, [value]);

  return (
    <textarea
      ref={textareaRef}
      id={fieldId}
      placeholder={placeholder}
      className={className}
      onChange={onChange}
      onBlur={onBlur}
      value={value}
      disabled={disabled}
      rows={1} // Start with 1 row, will expand automatically
    />
  );
};

const WorkflowField: React.FC<WorkflowFieldProps> = ({
  section,
  onInputChange,
  onBlur,
  formData,
  task_id,
  work_flow_id,
  isDisabled,
  isChildWorkflow,
  apiOptions: workflowApiOptions,
  setFormFields,
  formFields,
  isTaskReviewed,
  work_flow_execution_id,
  user_group_id,
  assignUserToTask,
  removeAssignToItem,
  setAssignToSelectedValues,
  assignToSelectedValues
}) => {
  // State for field validation errors
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  // Force render fix for localStorage issue on dev server
  const [, forceRender] = useState({});

  const ClientMatterId = localStorage.getItem('selectedMatterMyMatterId');

  // State for searchable select
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [searchableSelectOptions, setSearchableSelectOptions] = useState<
    Array<{ value: string; text: string; id?: string }>
  >([]);
  const [isLoading, setIsLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [activeSearchField, setActiveSearchField] = useState<string | null>(null);
  const [isUpdateMyCase, setIsUpdateMyCase] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  // State for confirmation modal
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  
  // RTK state for Clio messages
  const shouldShowClioMessages = useSelector((state: RootState) => state.clio.shouldShowClioMessages);
  const dispatch = useDispatch();
  const [pendingAction, setPendingAction] = useState<{
    fieldId: string;
    value: unknown;
    field: FormField;
    isRadioGroup?: boolean;
    actionType?: string;
  } | null>(null);

  // State for keyboard navigation in dropdowns (moved from renderField)
  const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);

  // State for multiselect
  const [multiselectOptions, setMultiselectOptions] = useState<Array<{ value: string; text: string; id?: string }>>([]);
  const [multiselectIsOpen, setMultiselectIsOpen] = useState(false);
  const [activeMultiselectField, setActiveMultiselectField] = useState<string | null>(null);
  const [multiselectSearchTerm, setMultiselectSearchTerm] = useState('');
  const [multiselectLoading, setIsMultiselectLoading] = useState(false);
  const multiselectDropdownRef = useRef<HTMLDivElement>(null);
  const [multiselectHighlightedIndex, setMultiselectHighlightedIndex] = useState<number>(-1);

  // Reset highlighted index when search term or active field changes
  useEffect(() => {
    if (activeSearchField) {
      setHighlightedIndex(-1);
    }
    if (activeMultiselectField) {
      setMultiselectHighlightedIndex(-1);
    }
  }, [searchTerm, activeSearchField, multiselectSearchTerm, activeMultiselectField]);

  // Focus management for dropdown keyboard navigation
  useEffect(() => {
    if (isOpen && activeSearchField) {
      // Reset to first item when dropdown opens or when search changes
      setHighlightedIndex(0);
    } else if (!isOpen) {
      setHighlightedIndex(-1);
    }

    if (multiselectIsOpen && activeMultiselectField) {
      setMultiselectHighlightedIndex(0);
    } else if (!multiselectIsOpen) {
      setMultiselectHighlightedIndex(-1);
    }
  }, [isOpen, activeSearchField, searchTerm, multiselectIsOpen, activeMultiselectField, multiselectSearchTerm]);

  // Ensure multiselect dropdown is closed on initial render
  useEffect(() => {
    setMultiselectIsOpen(false);
    setActiveMultiselectField(null);
    setMultiselectSearchTerm('');
  }, []);

  // Clear localStorage when switching to a different workflow
  useEffect(() => {
    const currentWorkflowId = work_flow_id;
    const storedWorkflowId = localStorage.getItem('currentWorkflowId');

    // If this is a different workflow, clear only the generic localStorage values
    if (storedWorkflowId && storedWorkflowId !== currentWorkflowId) {
      localStorage.removeItem('SelectedEventMatterId');
      // Don't remove workflow-specific isEventAdded values
    }

    // Store the current workflow ID
    localStorage.setItem('currentWorkflowId', currentWorkflowId);
  }, [work_flow_id]);

  // Force re-render after component mounts to ensure localStorage is read correctly
  useEffect(() => {
    const timer = setTimeout(() => {
      forceRender({}); // This will trigger a re-render
    }, 100);

    return () => clearTimeout(timer);
  }, [work_flow_id]); // Re-run when work_flow_id changes

  // Update focused index when options change but dropdown remains open
  useEffect(() => {
    if (isOpen && activeSearchField) {
      // If we have options and current index is out of bounds, reset to first item
      const currentOptions = searchTerm
        ? searchableSelectOptions.filter(option =>
          option.text.toLowerCase().includes(searchTerm.toLowerCase())
        )
        : searchableSelectOptions;

      if (
        currentOptions.length > 0 &&
        (highlightedIndex === -1 || highlightedIndex >= currentOptions.length)
      ) {
        setHighlightedIndex(0);
      } else if (currentOptions.length === 0) {
        setHighlightedIndex(-1);
      }
    }
  }, [searchableSelectOptions, searchTerm, isOpen, activeSearchField, highlightedIndex]);

  // Check if a field value matches the condition
  const checkConditionMatch = (condition: Condition): boolean => {
    // Handle AND condition type
    if ('type' in condition && condition.type === 'AND') {
      return condition.conditions.every(c => checkConditionMatch(c));
    }

    // Handle OR condition type
    if ('type' in condition && condition.type === 'OR') {
      return condition.conditions.some(c => checkConditionMatch(c));
    }

    // Handle simple field/value condition
    if ('field' in condition && 'value' in condition) {
      return formData[condition.field] === condition.value;
    }

    return false;
  };

  // Check if this section should be rendered based on conditions
  const shouldRenderSection = () => {
    if (!section.condition) return true;
    return checkConditionMatch(section.condition);
  };

  const sectionRequiredIndicator = section.required ? (
    <span className="text-red-500 ml-1">*</span>
  ) : null;

  // Validate a field value

  const validateField = (field: FormField, value: unknown): string => {
    if (!field.validation) return '';

    const validation = field.validation;

    // Check required field
    if (field.required && (!value || value === '')) {
      return validation.errorMessage || `${field.label || 'This field'} is required`;
    }

    // Convert value to string for validation if it's not already
    const stringValue = typeof value === 'string' ? value : String(value || '');

    // Check minimum length
    if (validation.minLength && stringValue.length < validation.minLength) {
      return validation.errorMessage || `Minimum length is ${validation.minLength} characters`;
    }

    // Check minimum value for numbers
    if (validation.min !== undefined && typeof value === 'number' && value < validation.min) {
      return validation.errorMessage || `Minimum value is ${validation.min}`;
    }

    // Check maximum value for numbers
    if (validation.max !== undefined && typeof value === 'number' && value > validation.max) {
      return validation.errorMessage || `Maximum value is ${validation.max}`;
    }

    // Check pattern
    if (validation.pattern && stringValue) {
      const regex = new RegExp(validation.pattern);
      if (!regex.test(stringValue)) {
        return validation.errorMessage || 'Invalid format';
      }
    }

    return '';
  };

  function mergeAndCombineFormDataToFields(
    fieldsArray: FormSection[],
    formData: Record<string, unknown>
  ): FormSection[] {
    // Create a deep copy to avoid mutating the original arrays
    const updatedFields = JSON.parse(JSON.stringify(fieldsArray));

    // Function to update event status to 'Synced'
    function updateEventStatus(events: Record<string, unknown[]>): Record<string, unknown[]> {
      const updatedEvents: Record<string, unknown[]> = {};
      Object.keys(events).forEach(matterKey => {
        if (Array.isArray(events[matterKey])) {
          updatedEvents[matterKey] = events[matterKey].map((event: unknown) => ({
            ...(event as Record<string, unknown>),
            eventStatus: 'Synced',
          }));
        }
      });
      return updatedEvents;
    }

    // Find the court-notice-section in the fields array
    const courtNoticeSection = updatedFields.find(
      (field: FormSection) => field.id === 'court-notice-section'
    );

    if (!courtNoticeSection) {
      console.warn('Court notice section not found in fields array');
      return updatedFields;
    }

    // Get existing data from court notice section first
    let existingData = {
      clients: [] as unknown[],
      events: {} as Record<string, unknown[]>,
    };

    // Check if court notice section already has data
    if (
      courtNoticeSection.fields &&
      courtNoticeSection.fields.length > 0 &&
      courtNoticeSection.fields[0].value &&
      Array.isArray(courtNoticeSection.fields[0].value) &&
      courtNoticeSection.fields[0].value.length > 0 &&
      courtNoticeSection.fields[0].value[0].value
    ) {
      existingData = courtNoticeSection.fields[0].value[0].value;
    }

    // Also check the section-level value property
    if (
      courtNoticeSection.value &&
      Array.isArray(courtNoticeSection.value) &&
      courtNoticeSection.value.length > 0 &&
      courtNoticeSection.value[0].value
    ) {
      const sectionData = courtNoticeSection.value[0].value;
      if (sectionData.clients && sectionData.events) {
        existingData = sectionData;
      }
    }

    // Initialize formDataValue - try to get from formData.undefined first
    let formDataValue = null;

    // Check if formData has the undefined key with data
    if (formData.undefined && Array.isArray(formData.undefined) && formData.undefined.length > 0) {
      const undefinedData = (
        formData.undefined as Array<{
          value: { clients: unknown[]; events: Record<string, unknown[]> };
        }>
      )[0];
      if (
        undefinedData &&
        undefinedData.value &&
        undefinedData.value.clients &&
        undefinedData.value.events
      ) {
        formDataValue = undefinedData.value;
      }
    }

    // If no data from formData.undefined, use existing data from court notice section
    if (!formDataValue && existingData.clients && existingData.events) {
      formDataValue = existingData;
    }

    // If still no data, return the original fields
    if (!formDataValue || !formDataValue.clients || !formDataValue.events) {
      console.warn(
        'No valid form data found in either formData.undefined or existing court notice section'
      );
      // Still update event status for existing data if available
      if (existingData.events && Object.keys(existingData.events).length > 0) {
        const updatedEvents = updateEventStatus(existingData.events);
        const mergedData = {
          clients: existingData.clients,
          events: updatedEvents,
        };

        // Update the court notice section
        if (courtNoticeSection.fields && courtNoticeSection.fields.length > 0) {
          courtNoticeSection.fields[0].value = [{ value: mergedData }];
        }
        courtNoticeSection.value = [{ value: mergedData }];
      }
      return updatedFields;
    }

    // Merge clients (avoid duplicates based on id)
    const mergedClients = [...existingData.clients];
    formDataValue.clients.forEach((newClient: unknown) => {
      const clientObj = newClient as { id: string };
      const existingClientIndex = mergedClients.findIndex(
        (client: unknown) => (client as { id: string }).id === clientObj.id
      );
      if (existingClientIndex === -1) {
        mergedClients.push(newClient);
      } else {
        // Update existing client with new data
        mergedClients[existingClientIndex] = newClient;
      }
    });

    // Use form data events as authoritative source and update their status to 'Synced'
    // This ensures that deleted events are properly removed from the display
    const updatedFormEvents = updateEventStatus(formDataValue.events);

    // Create the merged data
    const mergedData = {
      clients: mergedClients,
      events: updatedFormEvents,
    };

    // Update the court notice section
    if (courtNoticeSection.fields && courtNoticeSection.fields.length > 0) {
      courtNoticeSection.fields[0].value = [
        {
          value: mergedData,
        },
      ];
    } else {
      // Create the fields structure if it doesn't exist
      courtNoticeSection.fields = [
        {
          id: '',
          _id: '',
          type: 'courtNotice',
          label: 'Review the court notice and ensure all the dates are correct.',
          value: [
            {
              value: mergedData,
            },
          ],
        },
      ];
    }

    // Also update the main value property
    courtNoticeSection.value = [
      {
        value: mergedData,
      },
    ];

    return updatedFields;
  }

  // Helper function to safely get form data values
  const getFormDataValue = (fieldId: string, defaultValue: unknown = ''): string => {
    const value = formData[fieldId];
    if (value === null || value === undefined) return String(defaultValue);
    return String(value);
  };

  const getFormDataBoolean = (fieldId: string, defaultValue = false): boolean => {
    const value = formData[fieldId];
    if (value === null || value === undefined) return defaultValue;
    return Boolean(value);
  };

  // Handle input change with validation.put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/archive-work-flow`, apiPayload)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleInputChange = async (
    fieldId: string,
    value: unknown,
    field: FormField,
    isRadioGroup?: boolean
  ) => {
    if (isRadioGroup) {
      try {
        // Prepare the complete payload
        const payload = {
          workflow_id: work_flow_id,
          task_id: task_id,
          is_completed: false,
          forms: [
            {
              form_component_id: field.id,
              value: [
                {
                  id: null,
                  value: value,
                },
              ],
            },
          ],
        };

        // We don't need this logic anymore since we're handling it in the parent component
        // and this code didn't work correctly with the No/false values anyway

        const response = await apiClient.put(
          `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`,
          payload
        );

        if (response.data.statusCode === 200) {
          setSaveSuccess(true);
          setTimeout(() => setSaveSuccess(false), 2000);
        } else {
          throw new Error(response.data.message || 'Failed to save form data');
        }
      } catch (err) {
        setSaveError(err instanceof Error ? err.message : 'Failed to save field');
        console.error('Error saving form data:', err);
      }
    }

    if (field.form_field_id == 'update-mycases') {
      // Store the action details
      setPendingAction({
        fieldId,
        value,
        field,
        isRadioGroup,
      });

      // if (isChildWorkflow) {
      //   handleConfirmModalOk();
      // } else {
      //   setShowConfirmModal(true);
      // }

      return; // Early return to prevent further execution
    }

    // Validate the input
    const errorMessage = validateField(field, value);

    // Update error state
    setFieldErrors(prev => ({
      ...prev,
      [fieldId]: errorMessage,
    }));

    // Pass the value to parent component
    onInputChange(fieldId, value);
  };
  // Handle input blur - for saving on focus out
  const handleBlur = (fieldId: string, value: unknown) => {
    // Trigger validation on blur
    const field = section.fields.find(f => f._id === fieldId);
    if (field) {
      validateField(field, value);
    }
  };

  // Function to fetch options from API endpoint for searchable select
  const fetchSearchableOptions = async (endpoint: string, query: string) => {
    try {
      setIsLoading(true);
      const response = await apiClient.get(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/matter-list`, {
        params: { search: query },
      });

      if (response.data.statusCode === 200 && response.data.data) {
        // Expected format: { value: string, text: string }[]
        // Adapt the actual response format as needed
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const options = response.data.data.map((item: any) => ({
          value: item.id || item._id || item.value,
          text: item.name || item.text || item.label,
        }));
        setSearchableSelectOptions(options);
      } else {
        setSearchableSelectOptions([]);
      }
    } catch (error) {
      console.error('Error fetching searchable options:', error);
      setSearchableSelectOptions([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) && isOpen) {
        setIsOpen(false);
        setActiveSearchField(null);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Function to handle searchable select option selection
  const handleSearchSelectOption = (
    fieldId: string,
    value: string,
    text: string,
    field: FormField
  ) => {
    handleInputChange(fieldId, value, field);
    setIsOpen(false);
    setSearchTerm('');
    setActiveSearchField(null);

    // Make API call when option is selected
    try {
      // Prepare the complete payload
      const payload = {
        workflow_id: work_flow_id,
        task_id: task_id,
        is_completed: false,
        forms: [
          {
            form_component_id: field._id,
            value: [
              {
                id: null,
                value: value,
                text: text, // include the text value for reference
              },
            ],
          },
        ],
      };

      // API call
      apiClient
        .put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`, payload)
        .then(response => {
          if (response.data.statusCode === 200) {
            setSaveSuccess(true);
            setTimeout(() => setSaveSuccess(false), 2000);
          }
        })
        .catch(err => {
          setSaveError(err instanceof Error ? err.message : 'Failed to save field');
          console.error('Error saving form data:', err);
        });
    } catch (err) {
      console.error('Error preparing data for API call:', err);
    }
  };

  const handleConfirmModalOk = async () => {

    if (!pendingAction) return;

    const { fieldId, value, field } = pendingAction;


    if (!isChildWorkflow) {
      setIsUpdateMyCase(true);
    }

    setShowConfirmModal(false);
    setIsRetrying(true);

    if (work_flow_id && task_id) {
      const radioDisablePayload = {
        workflow_id: work_flow_id,
        task_id: task_id,
        is_completed: false,
        forms: [
          {
            form_component_id: 'court_notice_radio_disabled',
            value: [{ id: null, value: true }],
          },
        ],
      };
      await apiClient.put(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`,
        radioDisablePayload
      );
      onInputChange('court_notice_radio_disabled', true);
    }

    let lastError = null;
    let archiveResponse = null;
    let archiveSuccess = false;
    let localRetryGuard = 0;
    let retryCountFromApi;

    while (true) {
      if (localRetryGuard >= 4) break;
      localRetryGuard++;
      try {
        archiveResponse = await executeUpdateMyCaseAction();
        if (archiveResponse && archiveResponse.data.success) {
          archiveSuccess = true;
          setIsRetrying(false);
          break;
        }
        retryCountFromApi =
          archiveResponse && typeof archiveResponse?.data?.mycase_archive_retry_count === 'number'
            ? archiveResponse?.data?.mycase_archive_retry_count
            : 0;
        lastError =
          archiveResponse && archiveResponse.last_error
            ? archiveResponse.last_error
            : 'Failed to update MyCase';
        if (retryCountFromApi === 0) {
          const pendingPayload = {
            workflow_id: work_flow_id,
            task_id: task_id,
            is_completed: false,
            forms: [
              {
                form_component_id: field.id,
                value: [{ id: null, value: 'pending' }],
              },
            ],
          };
          await apiClient.put(
            `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`,
            pendingPayload
          );
          onInputChange(field.id, 'pending');
        } else if (retryCountFromApi === 3) {
          const finalPayload = {
            workflow_id: work_flow_id,
            task_id: task_id,
            is_completed: false,
            forms: [
              {
                form_component_id: field.id,
                value: [{ id: null, value: 'failed' }],
              },
            ],
          };

          await apiClient.put(
            `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`,
            finalPayload
          );
          onInputChange(field.id, 'failed');
          break;
        }

        await new Promise(res => setTimeout(res, 10000));
      } catch (error) {
        lastError = error;
        if (retryCountFromApi === 0) {
          const pendingPayload = {
            workflow_id: work_flow_id,
            task_id: task_id,
            is_completed: false,
            forms: [
              {
                form_component_id: field.id,
                value: [{ id: null, value: 'pending' }],
              },
            ],
          };
          await apiClient.put(
            `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`,
            pendingPayload
          );
          onInputChange(field.id, 'pending');
        } else if (retryCountFromApi === 2) {
          const finalPayload = {
            workflow_id: work_flow_id,
            task_id: task_id,
            is_completed: false,
            forms: [
              {
                form_component_id: field.id,
                value: [{ id: null, value: 'failed' }],
              },
            ],
          };
          await apiClient.put(
            `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`,
            finalPayload
          );
          onInputChange(field.id, 'failed');
          break;
        }
        await new Promise(res => setTimeout(res, 10000));
      }
    }

    if (archiveSuccess) {
      const finalPayload = {
        workflow_id: work_flow_id,
        task_id: task_id,
        is_completed: false,
        forms: [
          {
            form_component_id: field.id,
            value: [{ id: null, value }],
          },
        ],
      };
      await apiClient.put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`, finalPayload);
      const errorMessage = validateField(field, value);
      setFieldErrors(prev => ({ ...prev, [fieldId]: errorMessage }));
      onInputChange(fieldId, value);
      setSaveSuccess(true);
      setTimeout(() => setSaveSuccess(false), 2000);

      setIsRetrying(false);
    } else if (retryCountFromApi === 2) {
      setIsRetrying(false);
      setSaveError(
        lastError instanceof Error ? lastError.message : lastError || 'Failed to update MyCase'
      );
    } else {
      setIsRetrying(false);
      setSaveError(
        lastError instanceof Error ? lastError.message : lastError || 'Failed to update MyCase'
      );
    }
    setPendingAction(null);
  };

  const handleConfirmModalCancel = () => {
    setShowConfirmModal(false);
    setPendingAction(null);
  };

  // Extracted update MyCase logic for better maintainability
  const executeUpdateMyCaseAction = async () => {
    const apiPayload = {
      work_flow_execution_id: isChildWorkflow ? work_flow_id : work_flow_execution_id,
      type: 'updatecase',
      tenant_id: '1',
    };

    const response = await apiClient.put(
      `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/archive-work-flow`,
      apiPayload
    );

    // Set localStorage flag to indicate case update is completed
    localStorage.setItem('caseUpdateCompleted', 'true');

    // Update all events in formData to have eventStatus 'Synced'
    if (formFields) {
      const newResult = mergeAndCombineFormDataToFields(formFields, formData);
      setFormFields?.(newResult);

      // Also update the parent component's formData by calling onInputChange
      // Find the court notice section and extract its data
      const courtNoticeSection = newResult.find(
        (field: FormSection) => field.id === 'court-notice-section'
      );
      if (courtNoticeSection && courtNoticeSection.fields && courtNoticeSection.fields.length > 0) {
        const courtNoticeField = courtNoticeSection.fields[0];
        if (courtNoticeField.value) {
          // Call onInputChange to update the parent's formData
          onInputChange(courtNoticeField._id, courtNoticeField.value);
        }
      }
    } else {
      console.warn('🚀 ~ formFields is not available');
    }

    // Return the response data for retry logic
    return response.data;
  };

  interface CustomCheckboxProps {
    id: string;
    checked: boolean;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    className?: string;
    disabled?: boolean;
    borderColor?: string;
  }

  const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
    id,
    borderColor = '#5F6F84',
    checked,
    onChange,
    className = '',
    disabled = false,
  }) => {
    const border = disabled ? '#C7D1DF' : checked ? '#3F73F6' : borderColor;
    const background = disabled
      ? checked
        ? '#C7D1DF'
        : 'transparent'
      : checked
        ? '#3F73F6'
        : 'transparent';

    return (
      <div className={`relative w-[18px] h-[18px] ${className}`}>
        <input
          type="checkbox"
          id={id}
          className={`
            peer appearance-none w-full h-full rounded-[5px] 
            border-2 focus:outline-none cursor-pointer
            ${disabled ? 'cursor-not-allowed' : ''}
          `}
          onChange={onChange}
          checked={checked}
          disabled={disabled}
          style={{
            borderColor: border,
            backgroundColor: background,
          }}
        />
        {checked && (
          <svg
            className="absolute top-0 left-0 w-full h-full pointer-events-none"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 18 18"
            fill="none"
          >
            <rect
              x="1"
              y="1"
              width="16"
              height="16"
              rx="5"
              stroke={disabled ? '#C7D1DF' : '#3F73F6'}
              strokeWidth="2"
            />
            <path
              d="M5 9L8 12L13 6"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        )}
      </div>
    );
  };

  // Add new state for matter list
  const [matterOptions, setMatterOptions] = useState<SelectOption[]>([]);
  const [isLoadingMatters, setIsLoadingMatters] = useState(false);

  // Function to fetch matter list
  const fetchMatterList = async () => {
    try {
      setIsLoadingMatters(true);
      const response = await apiClient.get(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/matter-list`);

      if (response.data.statusCode === 200 && response.data.data?.matters) {
        const options = response.data.data.matters.map((matter: any) => ({
          value: matter._id,
          text: matter.name,
        }));
        setMatterOptions(options);
        localStorage.setItem('matterList', JSON.stringify(options));
      }
    } catch (error) {
      console.error('Error fetching matter list:', error);
    } finally {
      setIsLoadingMatters(false);
    }
  };

  // Fetch matter list when component mounts
  useEffect(() => {
    const hasChildPriorityField = section.fields.some(
      field => field.form_field_id === 'case_priority_child'
    );
    if (hasChildPriorityField) {
      fetchMatterList();
    }
  }, [section.fields]);

  // Sync assign to selected values with form data when component mounts
  useEffect(() => {
    // Find the "Assign this workflow to" field
    const assignWorkflowField = section.fields.find(
      field => field.label === 'Assign this workflow to'
    );

    if (assignWorkflowField && setAssignToSelectedValues) {
      const fieldId = assignWorkflowField._id;
      const currentValue = getFormDataValue(fieldId);

      // If there's a value in the form data, update the assign to selected values
      if (currentValue && currentValue !== '') {
        // Find the option that matches the current value
        const matchingOption = searchableSelectOptions.find(opt => opt.value === currentValue);
        if (matchingOption) {
          // setAssignToSelectedValues([matchingOption.id || matchingOption.value]);
        } else {
          // If the option is not in searchableSelectOptions yet, we need to fetch it
          // This will be handled by the existing useEffect that preloads selected options
        }
      }
    }
  }, [section.fields, searchableSelectOptions, formData]);

  // Sync form data when assignToSelectedValues changes from parent component
  useEffect(() => {
    // Find the "Assign this workflow to" field
    const assignWorkflowField = section.fields.find(
      field => field.label === 'Assign this workflow to'
    );

    if (assignWorkflowField && assignToSelectedValues && assignToSelectedValues.length > 0) {
      const fieldId = assignWorkflowField._id;
      const currentValue = getFormDataValue(fieldId);
      const newValue = assignToSelectedValues[0]; // Take the first assigned user

      // Only update if the value is different
      if (currentValue !== newValue) {
        // Find the option that matches the new value
        const matchingOption = searchableSelectOptions.find(opt => opt.value === newValue);
        if (matchingOption) {
          // Update the form data
          handleInputChange(fieldId, matchingOption.value, assignWorkflowField);
        }
      }
    }
  }, [assignToSelectedValues, section.fields, searchableSelectOptions]);

  // Function to fetch multiselect options from API
  const fetchMultiselectOptions = async (field: FormField, query: string = '') => {
    try {
      setIsMultiselectLoading(true);

      // Use the API endpoint from the field if available, otherwise use default
      const endpoint = field.apiEndpoint || '/workflow/user-list';

      const response = await apiClient.get(`${process.env.NEXT_PUBLIC_BASE_URL}${endpoint}`, {
        params: { search: query },
      });

      if (response.data.statusCode === 200 && response.data.data) {
        // Handle different response structures
        let users = [];
        if (response.data.data.users) {
          users = response.data.data.users;
        } else if (Array.isArray(response.data.data)) {
          users = response.data.data;
        } else {
          users = [response.data.data];
        }

        const options = users
          .filter((user: any) => user.is_active !== false) // Only active users
          .map((user: any) => ({
            value: user.value || user.id || user._id,
            text: user.text || user.name || user.label,
          }));

        setMultiselectOptions(options);
      } else {
        setMultiselectOptions([]);
      }
    } catch (error) {
      console.error('Error fetching multiselect options:', error);
      setMultiselectOptions([]);
    } finally {
      setIsMultiselectLoading(false);
    }
  };

  // Handle multiselect option selection/deselection
  const handleMultiselectOptionToggle = (fieldId: string, optionValue: string, field: FormField) => {
    const currentValues = getFormDataValue(fieldId);
    let selectedValues: string[] = [];

    // Parse current values
    if (currentValues && currentValues !== '') {
      try {
        selectedValues = JSON.parse(currentValues);
      } catch {
        selectedValues = currentValues.split(',').map(v => v.trim()).filter(v => v);
      }
    }

    // Toggle the selected value
    const newSelectedValues = selectedValues.includes(optionValue)
      ? selectedValues.filter(v => v !== optionValue)
      : [...selectedValues, optionValue];

    // Update the form data
    const newValue = JSON.stringify(newSelectedValues);
    handleInputChange(fieldId, newValue, field);

    // Make API call to save the selection
    try {
      const payload = {
        workflow_id: work_flow_id,
        task_id: task_id,
        is_completed: false,
        forms: [
          {
            form_component_id: field._id,
            value: [
              {
                id: null,
                value: newValue,
              },
            ],
          },
        ],
      };

      apiClient
        .put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`, payload)
        .then(response => {
          if (response.data.statusCode === 200) {
            setSaveSuccess(true);
            setTimeout(() => setSaveSuccess(false), 2000);
          }
        })
        .catch(err => {
          setSaveError(err instanceof Error ? err.message : 'Failed to save field');
          console.error('Error saving form data:', err);
        });
    } catch (err) {
      console.error('Error preparing data for API call:', err);
    }
  };

  // Get selected values for display
  const getSelectedValues = (fieldId: string): string[] => {
    const currentValues = getFormDataValue(fieldId);
    if (!currentValues || currentValues === '') return [];

    try {
      return JSON.parse(currentValues);
    } catch {
      return currentValues.split(',').map(v => v.trim()).filter(v => v);
    }
  };

  // Get display text for selected values
  const getSelectedDisplayText = (fieldId: string): string => {
    const selectedValues = getSelectedValues(fieldId);
    if (selectedValues.length === 0) return '';

    const selectedOptions = multiselectOptions.filter(option =>
      selectedValues.includes(option.value)
    );

    if (selectedOptions.length === 0) return '';
    if (selectedOptions.length === 1) return selectedOptions[0].text;
    if (selectedOptions.length <= 3) {
      return selectedOptions.map(opt => opt.text).join(', ');
    }
    return `${selectedOptions.length} items selected`;
  };

  // Handle click outside to close multiselect dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (multiselectDropdownRef.current && !multiselectDropdownRef.current.contains(event.target as Node) && multiselectIsOpen) {
        setMultiselectIsOpen(false);
        setActiveMultiselectField(null);
        setMultiselectSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [multiselectIsOpen]);

  const generateInitials = (name: string): string => {
    const nameParts = name.trim().split(' ');
    if (nameParts.length >= 2) {
      return (nameParts[0][0] + nameParts[nameParts.length - 1][0]).toUpperCase();
    }
    return name[0]?.toUpperCase() || '';
  };

  useEffect(() => {
    if (!pendingAction) return;

    if (isChildWorkflow) {
      handleConfirmModalOk();
    } else {
      setShowConfirmModal(true);
    }
  }, [pendingAction]);

  // Render a single form field based on its type
  const renderField = (field: FormField, index: number) => {
    // Ensure field has a unique id
    const fieldId = field._id || `field-${index}`;
    const errorMessage = fieldErrors[fieldId] || '';
    const isInvalid = errorMessage !== '';

    // Check if this field should be disabled due to MyCase update
    const isClientMatterField =
      field.label?.toLowerCase().includes('client') ||
      field.label?.toLowerCase().includes('matter') ||
      field.placeholder?.toLowerCase().includes('client') ||
      field.placeholder?.toLowerCase().includes('matter');

    // Also disable the Update MyCases button itself after successful update
    const isUpdateMyCasesButton = field.form_field_id === 'update-mycases';

    // Update isFieldDisabled to include isTaskReviewed logic
    const isFieldDisabled = Boolean(
      isDisabled || (isUpdateMyCase && (isClientMatterField || isUpdateMyCasesButton))
    );

    // Base classes for inputs + error styling + disabled styling
    const baseInputClass = `${workflowStyles.textInput} ${isInvalid ? 'border-red-500 focus:ring-red-500' : ''} ${conditionalDisabled(isFieldDisabled, 'input')}`;

    // Required indicator for labels
    // const requiredIndicator = field.required ? <span className="text-red-500 ml-1">*</span> : null;

    // Error message component
    const errorComponent = isInvalid ? (
      <div className="text-red-500 text-xs mt-1 flex items-center">
        <AlertCircle size={12} className="mr-1" />
        {errorMessage}
      </div>
    ) : null;



    switch (field.type) {
      case fieldTypes.TEXT:
        return (
          <div key={fieldId} className="ml-8">
            <input
              type="text"
              id={fieldId}
              placeholder={field.placeholder}
              className={baseInputClass}
              onChange={e => handleInputChange(fieldId, e.target.value, field)}
              onBlur={e => handleBlur(fieldId, e.target.value)}
              value={getFormDataValue(fieldId)}
              disabled={isFieldDisabled}
            />
            {errorComponent}
          </div>
        );

      case fieldTypes.EMAIL:
        return (
          <div key={fieldId} className="ml-8">
            <input
              type="email"
              id={fieldId}
              placeholder={field.placeholder}
              className={baseInputClass}
              onChange={e => handleInputChange(fieldId, e.target.value, field)}
              onBlur={e => handleBlur(fieldId, e.target.value)}
              value={getFormDataValue(fieldId)}
              disabled={isFieldDisabled}
            />
            {errorComponent}
          </div>
        );

      case fieldTypes.PHONE:
        return (
          <div key={fieldId} className="ml-8">
            <input
              type="tel"
              id={fieldId}
              placeholder={field.placeholder}
              className={baseInputClass}
              onChange={e => handleInputChange(fieldId, e.target.value, field)}
              onBlur={e => handleBlur(fieldId, e.target.value)}
              value={getFormDataValue(fieldId)}
              disabled={isFieldDisabled}
            />
            {errorComponent}
          </div>
        );

      case fieldTypes.TEXTAREA:
        return (
          <div key={fieldId} className="ml-8">
            <TextareaAutosize
              fieldId={fieldId}
              placeholder={field.placeholder}
              className={`${workflowStyles.textareaInput} ${isInvalid ? 'border-red-500 focus:ring-red-500' : ''} ${conditionalDisabled(isFieldDisabled, 'input')} w-full border border-[#DCE2EB] p-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none`}
              value={getFormDataValue(fieldId)}
              onChange={e => {
                // Only update local state on change, don't make API call
                handleInputChange(fieldId, e.target.value, field);
              }}
              onBlur={e => {
                // Make API call when field loses focus
                handleBlur(fieldId, e.target.value);

                // Also make the API call here
                try {
                  // Prepare the complete payload
                  const payload = {
                    workflow_id: work_flow_id,
                    task_id: task_id,
                    is_completed: false,
                    forms: [
                      {
                        form_component_id: field._id,
                        value: [
                          {
                            id: null,
                            value: e.target.value,
                          },
                        ],
                      },
                    ],
                  };

                  // API call when field loses focus
                  apiClient
                    .put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`, payload)
                    .then(response => {
                      if (response.data.statusCode === 200) {
                        setSaveSuccess(true);
                        setTimeout(() => setSaveSuccess(false), 2000);
                      }
                    })
                    .catch(err => {
                      setSaveError(err instanceof Error ? err.message : 'Failed to save field');
                      console.error('Error saving form data:', err);
                    });
                } catch (err) {
                  console.error('Error preparing data for API call:', err);
                }
              }}
              disabled={isFieldDisabled || field?.label === 'Send the following SMS via Kenect:'}
            />
            {errorComponent}
          </div>
        );

      case fieldTypes.CHECKBOX:
        return (
          <div key={fieldId} className={`${workflowStyles.checkboxContainer} ml-8`}>
            <div className="flex items-start gap-2">
              <CustomCheckbox
                id={fieldId}
                checked={getFormDataBoolean(fieldId)}
                disabled={isFieldDisabled}
                borderColor={'#5F6F84'}
                onChange={e => handleInputChange(fieldId, e.target.checked, field, true)}
              />
              <label
                htmlFor={fieldId}
                className="text-[14px] leading-[20px] font-medium text-[#5F6F84] hover:text-[#2A2E34] text-left break-words whitespace-normal"
              >
                {field.label}
              </label>
            </div>
            {errorComponent}
          </div>
        );

      case fieldTypes.SELECT: {
        // Special case: assign-section-01 as disabled input box
        if (field.form_field_id === 'case_priority_child') {
          const selectedOptionText =
            matterOptions.find(opt => opt.value === getFormDataValue(fieldId))?.text || '';

          return (
            <div key={fieldId} className="ml-8">
              {isLoadingMatters ? (
                <div className="flex items-center gap-2">
                  <LoaderCircle className="animate-spin" size={20} />
                  <span>Loading matters...</span>
                </div>
              ) : (
                <input
                  type="text"
                  className={`${workflowStyles.textInput} bg-[#F3F5F9] text-[#A0AEC0] border border-[#DCE2EB] rounded-3xl px-4 py-3 w-full cursor-not-allowed`}
                  value={selectedOptionText}
                  disabled
                  readOnly
                  placeholder={field.placeholder || 'Select option'}
                  style={{ pointerEvents: 'none' }}
                />
              )}
              {errorComponent}
            </div>
          );
        }

        const currentValue = getFormDataValue(fieldId);

        // Auto-assign user on component mount if no value exists
        // eslint-disable-next-line react-hooks/rules-of-hooks
        useEffect(() => {
          if (field.type === fieldTypes.SELECT) {
            const initializeUser = async () => {
              try {
                const response = await apiClient.get(
                  `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/user-list`,
                  {
                    params: { search: "", user_group_id: user_group_id || '' },
                  }
                );

                const users = response?.data?.data?.users || [];
                const autoAssignedUser = users.find((user: any) =>
                  user.is_active && user.value && user.value.includes('(Auto-assigned)')
                );

                // If no current value, auto-assign the auto-assigned user
                if (!currentValue && autoAssignedUser) {
                  handleInputChange(fieldId, autoAssignedUser.value, field);

                  // Make API call to save the auto-selection
                  const payload = {
                    workflow_id: work_flow_id,
                    task_id: task_id,
                    is_completed: false,
                    forms: [
                      {
                        form_component_id: field._id,
                        value: [
                          {
                            id: null,
                            value: autoAssignedUser.value,
                            text: autoAssignedUser.text,
                          },
                        ],
                      },
                    ],
                  };

                  apiClient
                    .put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`, payload)
                    .then(response => {
                      if (response.data.statusCode === 200) {
                        setSaveSuccess(true);
                        setTimeout(() => setSaveSuccess(false), 2000);
                      }
                    })
                    .catch(err => {
                      console.error('Error saving auto-assigned user:', err);
                    });
                }

                // Always ensure the selected user (whether auto-assigned or pre-selected) is available for display
                if (currentValue || autoAssignedUser) {
                  const userToDisplay = currentValue
                    ? users.find((user: any) => user.is_active && user.value === currentValue)
                    : autoAssignedUser;

                  if (userToDisplay) {
                    setSearchableSelectOptions(prev => {
                      const exists = prev.some(opt => opt.value === userToDisplay.value);
                      if (!exists) {
                        return [...prev, {
                          value: userToDisplay.value,
                          text: userToDisplay.text,
                          id: userToDisplay._id || userToDisplay.id
                        }];
                      }
                      return prev;
                    });
                    if (field.label === 'Assign this workflow to' && assignUserToTask && currentValue) {


                      const userId = userToDisplay._id || userToDisplay.id || userToDisplay.value;
                      assignUserToTask(userId, false);
                    }
                  }
                }
              } catch (error) {
                console.error('Error initializing user:', error);
              }
            };

            initializeUser();
          }
        }, []);

        // eslint-disable-next-line react-hooks/rules-of-hooks
        useEffect(() => {
          if (
            field.type === fieldTypes.SELECT &&
            currentValue &&
            !searchableSelectOptions.some(opt => opt.value === currentValue)
          ) {
            const preloadSelectedOption = async () => {
              // For auto-assigned users, we need to get them from the full user list
              try {
                const response = await apiClient.get(
                  `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/user-list`,
                  {
                    params: { search: "", user_group_id: user_group_id || '' },
                  }
                );

                const users = response?.data?.data?.users || [];
                const selected = users.find((user: any) =>
                  user.is_active && user.value === currentValue
                );

                if (selected) {
                  setSearchableSelectOptions(prev => {
                    const exists = prev.some(opt => opt.value === selected.value);
                    return exists ? prev : [...prev, {
                      value: selected.value,
                      text: selected.text,
                      id: selected._id || selected.id
                    }];
                  });
                }
              } catch (error) {
                console.error('Error preloading selected option:', error);
              }
            };

            preloadSelectedOption();
          }
        }, [currentValue]);

        const isCurrentFieldActive = activeSearchField === fieldId;
        const hasSelection = currentValue && currentValue !== '';

        const fetchUserList = async (
          query: string = '',
          user_group_id: string = ''
        ): Promise<SelectOption[]> => {
          try {
            const response = await apiClient.get(
              `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/user-list`,
              {
                params: { 
                  search: query, 
                  user_group_id: user_group_id || '6877420fd4928f6a37ba1b95'
                },
              }
            );

            const users = response?.data?.data?.users || [];

            return users
              .filter(
                (user: any) =>
                  user.is_active &&
                  (!user.value || !user.value.includes('(Auto-assigned)'))
              )
              .map((user: any) => {
                const displayName = user.text || user.name;
                return {
                  label: displayName,
                  value: user.value,
                  text: displayName,
                  id: user._id || user.id,
                  _id: user._id || user.id,
                  is_active: user.is_active,
                  initials: generateInitials ? generateInitials(displayName) : null,
                };
              });
          } catch (error) {
            console.error('Error fetching user list:', error);
            return [];
          }
        };


        // Handle dropdown toggle
        // const toggleDropdown = () => {
        //   if (isFieldDisabled) return;

        //   if (!isOpen) {
        //     fetchUserList();
        //     setActiveSearchField(fieldId);
        //   }
        //   setIsOpen(!isOpen);
        //   setSearchTerm('');
        // };

        const toggleDropdown = async () => {
          if (isFieldDisabled) return;

          if (!isOpen) {
            setIsLoading(true);
            const options: any = await fetchUserList("", user_group_id || '6877420fd4928f6a37ba1b95');
            setSearchableSelectOptions(options);
            setActiveSearchField(fieldId);
            setIsLoading(false);
          }

          setIsOpen(!isOpen);
        };

        // Handle option selection (extracted to avoid repetition)
        const handleOptionSelect = (option: SelectOption) => {
          if (isFieldDisabled) return;

          handleInputChange(fieldId, option.value, field);
          if (field.label === 'Assign this workflow to' && assignUserToTask) {
            const userId = option.id || option.value;
            assignUserToTask(userId, false);
          }

          if (setAssignToSelectedValues && field.label === 'Assign this workflow to') {
            // setAssignToSelectedValues([option.id || option.value]);
          }

          // Make API call to save selection
          try {
            const payload = {
              workflow_id: work_flow_id,
              task_id: task_id,
              is_completed: false,
              forms: [
                {
                  form_component_id: field._id,
                  value: [
                    {
                      id: null,
                      value: option.value,
                      text: option.text,
                    },
                  ],
                },
              ],
            };

            apiClient
              .put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`, payload)
              .then(response => {
                if (response.data.statusCode === 200) {
                  setSaveSuccess(true);
                  setTimeout(() => setSaveSuccess(false), 2000);
                }
              })
              .catch(err => {
                setSaveError(err instanceof Error ? err.message : 'Failed to save field');
                console.error('Error saving form data:', err);
              });
          } catch (err) {
            console.error('Error preparing data for API call:', err);
          }

          setIsOpen(false);
          setActiveSearchField(null);
        };

        // Handle keyboard navigation for dropdown
        const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
          if (!isCurrentFieldActive) {
            // If dropdown is not open, handle opening it
            if (
              e.key === 'Enter' ||
              e.key === ' ' ||
              e.key === 'ArrowDown' ||
              e.key === 'ArrowUp'
            ) {
              e.preventDefault();
              toggleDropdown();
            }
            return;
          }

          const options = filteredOptions;

          switch (e.key) {
            case 'ArrowDown':
              e.preventDefault();
              setHighlightedIndex(prev => {
                const nextIndex = prev < options.length - 1 ? prev + 1 : 0;
                // Scroll highlighted option into view
                setTimeout(() => {
                  const highlightedElement = document.querySelector(
                    `[data-option-index="${nextIndex}"]`
                  );
                  if (highlightedElement) {
                    highlightedElement.scrollIntoView({
                      block: 'nearest',
                      behavior: 'smooth',
                    });
                  }
                }, 0);
                return nextIndex;
              });
              break;

            case 'ArrowUp':
              e.preventDefault();
              setHighlightedIndex(prev => {
                const nextIndex = prev > 0 ? prev - 1 : options.length - 1;
                // Scroll highlighted option into view
                setTimeout(() => {
                  const highlightedElement = document.querySelector(
                    `[data-option-index="${nextIndex}"]`
                  );
                  if (highlightedElement) {
                    highlightedElement.scrollIntoView({
                      block: 'nearest',
                      behavior: 'smooth',
                    });
                  }
                }, 0);
                return nextIndex;
              });
              break;

            case 'Enter':
              e.preventDefault();
              if (highlightedIndex >= 0 && highlightedIndex < options.length) {
                const selectedOption = options[highlightedIndex];
                handleOptionSelect(selectedOption);
              }
              break;

            case 'Escape':
              e.preventDefault();
              setIsOpen(false);
              setActiveSearchField(null);
              setSearchTerm('');
              setHighlightedIndex(-1);
              break;
          }
        };

        // Filter options based on search term
        const filteredOptions = searchTerm
          ? searchableSelectOptions.filter(option =>
            option.text.toLowerCase().includes(searchTerm.toLowerCase())
          )
          : searchableSelectOptions;

        return (
          <div key={fieldId} className="ml-8">
            <div className={`${workflowStyles.selectContainer} relative`} ref={dropdownRef}>
              <input
                type="text"
                className={`${workflowStyles.selectInput} ${conditionalDisabled(isFieldDisabled, 'input')} appearance-none block w-full px-3 py-2 border border-[#DCE2EB] focus:outline-none focus:ring-blue-200 focus:border focus:border-[#3F73F6] focus:ring-2 cursor-pointer ${hasSelection && !isCurrentFieldActive ? 'pr-20' : 'pr-10'}`}
                placeholder={
                  field.label == 'Assign this workflow to' ? 'Select an assignee' : 'Select option'
                }
                value={
                  isCurrentFieldActive
                    ? searchTerm
                    : searchableSelectOptions.find(opt => opt.value == currentValue)?.text || ''
                }
                onChange={e => {
                  const newSearchTerm = e.target.value;
                  setSearchTerm(newSearchTerm);

                  // Check if this is the "Assign this workflow to" field and if it's being cleared via backspace
                  if (field.label === 'Assign this workflow to' && removeAssignToItem && newSearchTerm === '') {
                    const currentValue = getFormDataValue(fieldId);

                    // If the field had a value and now the search term is empty, call removeAssignToItem
                    if (currentValue && currentValue !== '') {
                      const currentSelectedUser = searchableSelectOptions.find(opt => opt.value === currentValue);
                      const userId = currentSelectedUser?.id || currentSelectedUser?.value;

                      if (userId) {
                        removeAssignToItem(userId, false);
                      }

                      // Clear the field value and make API call
                      handleInputChange(fieldId, '', field);

                      // Make API call to save the cleared selection
                      try {
                        const payload = {
                          workflow_id: work_flow_id,
                          task_id: task_id,
                          is_completed: false,
                          forms: [
                            {
                              form_component_id: field._id,
                              value: [
                                {
                                  id: null,
                                  value: '',
                                  text: '',
                                },
                              ],
                            },
                          ],
                        };

                        apiClient
                          .put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`, payload)
                          .then(response => {
                            if (response.data.statusCode === 200) {
                              setSaveSuccess(true);
                              setTimeout(() => setSaveSuccess(false), 2000);
                            }
                          })
                          .catch(err => {
                            setSaveError(err instanceof Error ? err.message : 'Failed to save field');
                            console.error('Error saving form data:', err);
                          });
                      } catch (err) {
                        console.error('Error preparing data for API call:', err);
                      }
                    }
                  }

                  if (!isOpen) {
                    toggleDropdown();
                  }
                  // Debounce search - only search for non-auto-assigned users
                  const timer = setTimeout(() => {
                    fetchUserList(newSearchTerm, user_group_id || '').then(options => {
                      setSearchableSelectOptions(options);
                    });
                  }, 300);
                  return () => clearTimeout(timer);
                }}
                onFocus={async () => {
                  await toggleDropdown();

                  // Set search term to current selected user's name to preserve display
                  const currentValue = getFormDataValue(fieldId);
                  if (currentValue && currentValue !== '') {
                    const selectedUser = searchableSelectOptions.find(opt => opt.value === currentValue);
                    if (selectedUser) {
                      setSearchTerm(selectedUser.text);
                    } else {
                      setSearchTerm('');
                    }
                  } else {
                    setSearchTerm('');
                  }
                }}
                onClick={e => e.stopPropagation()}
                disabled={isFieldDisabled}
                autoComplete="off"
                onKeyDown={handleKeyDown}
                role="combobox"
                aria-haspopup="listbox"
                aria-expanded={isOpen && isCurrentFieldActive}
                aria-activedescendant={
                  isOpen && isCurrentFieldActive && highlightedIndex >= 0
                    ? `${fieldId}-option-${highlightedIndex}`
                    : undefined
                }
              />

              {/* Clear button */}
              {hasSelection && !isCurrentFieldActive && !isFieldDisabled && (
                <button
                  type="button"
                  className="absolute inset-y-0 right-2 flex items-center px-2 text-[#5F6F84] cursor-pointer transition-colors"
                  onClick={e => {
                    e.stopPropagation();

                    // Get the current selected user ID before clearing
                    const currentSelectedUser = searchableSelectOptions.find(opt => opt.value === currentValue);
                    const userId = currentSelectedUser?.id || currentSelectedUser?.value;

                    // Call removeUserFromTask if this is an assign workflow field
                    if (field.label === 'Assign this workflow to' && removeAssignToItem && userId) {
                      removeAssignToItem(userId, false);
                    }

                    handleInputChange(fieldId, '', field);
                    setSearchTerm('');

                    // Make API call to save the cleared selection
                    try {
                      const payload = {
                        workflow_id: work_flow_id,
                        task_id: task_id,
                        is_completed: false,
                        forms: [
                          {
                            form_component_id: field._id,
                            value: [
                              {
                                id: null,
                                value: '',
                                text: '',
                              },
                            ],
                          },
                        ],
                      };

                      apiClient
                        .put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`, payload)
                        .then(response => {
                          if (response.data.statusCode === 200) {
                            setSaveSuccess(true);
                            setTimeout(() => setSaveSuccess(false), 2000);
                          }
                        })
                        .catch(err => {
                          setSaveError(err instanceof Error ? err.message : 'Failed to save field');
                          console.error('Error saving form data:', err);
                        });
                    } catch (err) {
                      console.error('Error preparing data for API call:', err);
                    }
                  }}
                  title="Clear selection"
                >
                  <X size={20} />
                </button>
              )}

              {/* Dropdown arrow */}
              {
                !hasSelection && (
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
                    <ChevronDown
                      size={20}
                      className={`text-[#5F6F84] transition-transform duration-200 ${isOpen && isCurrentFieldActive ? 'rotate-180' : ''}`}
                    />
                  </div>
                )
              }

              {/* Dropdown options */}
              {isOpen && isCurrentFieldActive && (
                <div
                  ref={dropdownRef}
                  className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-hidden"
                  role="listbox"
                >
                  <div className="max-h-[150px] overflow-y-auto">
                    {filteredOptions.length > 0 ? (
                      filteredOptions.map((option, optionIndex: number) => (
                        <div
                          key={`${fieldId}-option-${optionIndex}`}
                          id={`${fieldId}-option-${optionIndex}`}
                          data-option-index={optionIndex}
                          className={`p-3 cursor-pointer transition-colors duration-150 border-gray-100 last:border-b-0 ${currentValue === option.value
                            ? 'bg-[#EBF1FF] text-[#3F73F6] font-medium'
                            : highlightedIndex === optionIndex
                              ? 'bg-[#F1F5F9] text-gray-900'
                              : 'text-gray-700 hover:bg-[#F8F9FC]'
                            } ${conditionalDisabled(isFieldDisabled, 'dropdown')}`}
                          onClick={() => handleOptionSelect(option)}
                          onMouseEnter={() => setHighlightedIndex(optionIndex)}
                          role="option"
                          aria-selected={currentValue === option.value}
                        >
                          <div className="flex items-center space-x-2">
                            <div
                              className="w-[30px] h-[30px] rounded-full bg-[#5F6F84] flex items-center justify-center text-sm text-white"
                            >
                              {option.text
                                ?.split(' ')
                                .map((word) => word[0]?.toUpperCase())
                                .join('')
                                .slice(0, 2)}
                            </div>

                            <span>{option.text}</span>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-gray-500">
                        {searchTerm ? 'No results found' : 'No options available'}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      }

      case fieldTypes.DATE:
        return (
          <div key={fieldId} className="ml-8">
            <input
              type="date"
              id={fieldId}
              placeholder={field.placeholder}
              className={baseInputClass}
              onChange={e => handleInputChange(fieldId, e.target.value, field)}
              onBlur={e => handleBlur(fieldId, e.target.value)}
              value={getFormDataValue(fieldId)}
              disabled={isFieldDisabled}
            />
            {errorComponent}
          </div>
        );

      case fieldTypes.TIME:
        return (
          <div key={fieldId} className="ml-8">
            <input
              type="time"
              id={fieldId}
              placeholder={field.placeholder}
              className={baseInputClass}
              onChange={e => handleInputChange(fieldId, e.target.value, field)}
              onBlur={e => handleBlur(fieldId, e.target.value)}
              value={getFormDataValue(fieldId)}
              disabled={isFieldDisabled}
            />
            {errorComponent}
          </div>
        );

      case fieldTypes.NUMBER:
        return (
          <div key={fieldId} className="ml-8">
            <input
              type="number"
              id={fieldId}
              placeholder={field.placeholder}
              className={baseInputClass}
              onChange={e => {
                const value = e.target.value === '' ? '' : Number(e.target.value);
                handleInputChange(fieldId, value, field);
              }}
              onBlur={e => {
                const value = e.target.value === '' ? '' : Number(e.target.value);
                handleBlur(fieldId, value);
              }}
              value={getFormDataValue(fieldId)}
              min={field.validation?.min}
              max={field.validation?.max}
              disabled={isFieldDisabled}
            />
            {errorComponent}
          </div>
        );

      case fieldTypes.RADIO:
        return (
          <div key={fieldId} className="ml-8">
            <div className="mt-1 space-y-2">
              {field.options &&
                Array.isArray(field.options) &&
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                field.options.map((option: any, optionIndex: number) => (
                  <div key={`${fieldId}-option-${optionIndex}`} className="flex items-start gap-2">
                    <input
                      type="radio"
                      id={`${fieldId}-${optionIndex}`}
                      name={fieldId}
                      value={option.value}
                      checked={getFormDataValue(fieldId) === option.value}
                      onChange={() => handleInputChange(fieldId, option.value, field)}
                      onBlur={() => handleBlur(fieldId, option.value)}
                      className={`h-4 w-4 mt-[2px] shrink-0 border-[#DCE2EB] ${isFieldDisabled ? 'bg-gray-100  opacity-50 cursor-not-allowed text-gray-700' : 'text-blue-600 focus:ring-blue-500'} `}
                      disabled={isFieldDisabled}
                    />
                    <label
                      htmlFor={`${fieldId}-${optionIndex}`}
                      className="block text-sm text-gray-700 leading-[20px] text-left break-words whitespace-normal"
                    >
                      {option.text}
                    </label>
                  </div>
                ))}
            </div>
            {errorComponent}
          </div>
        );

      case fieldTypes.COURT_NOTICE:
        return (
          <div key={fieldId} className="ml-8">
            <CourtNoticeField
              field={field}
              onInputChange={onInputChange}
              onBlur={onBlur}
              task_id={task_id}
              section={section}
              isDisabled={isFieldDisabled}
              isChildWorkflow={isChildWorkflow}
              apiOptions={workflowApiOptions}
              isUpdateMyCase={isUpdateMyCase}
              work_flow_execution_id={work_flow_id}
              isTaskReviewed={isTaskReviewed}
              user_group_id={user_group_id || ''}
            />
            {errorComponent}
          </div>
        );

      case fieldTypes.RADIO_BUTTON_GROUP:
        // Dynamically identify the court notice radio by label
        // eslint-disable-next-line no-case-declarations
        const isCourtNoticeRadio =
          field.type === fieldTypes.RADIO_BUTTON_GROUP &&
          typeof field.label === 'string' &&
          field.label
            .trim()
            .toLowerCase()
            .includes('do you have any events in the calendar in this court notice');
        // eslint-disable-next-line no-case-declarations
        const isResponseRequired = field.form_field_id === 'court_priority';

        // eslint-disable-next-line no-case-declarations
        const isRadioDisabled =
          (isCourtNoticeRadio &&
            (formData['court_notice_radio_disabled'] === true ||
              formData['court_notice_radio_disabled_secondary'] === true)) ||
          getFormDataValue('680776cfe1af8cdccbe00c1a') === 'true';
        return (
          <div key={fieldId} className="ml-8">
            <div className={`mt-2 flex ${isResponseRequired ? 'flex-col' : 'flex-col sm:flex-row'} gap-3`}>
              {field.options &&
                Array.isArray(field.options) &&
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                field.options.map((option: any, optionIndex: number) => {
                  const isSelected = getFormDataValue(fieldId) === option.value;
                  return (
                    <button
                      key={`${fieldId}-option-${optionIndex}`}
                      type="button"
                      className={`flex items-center justify-start space-x-2 px-[16px] py-[9px] font-medium text-[14px] leading-[20px] rounded-[12px] transition-colors duration-200 border w-full sm:w-auto
          ${isRadioDisabled || isFieldDisabled
                          ? 'cursor-not-allowed bg-[#EFF2F7] text-[#A2AFC2] disabled:text-[#5F6F84] border-[#D0D5DD]'
                          : isSelected
                          ? 'bg-[#3F73F6] text-[#FFFFFF] border-[#3F73F6]'
                          : 'bg-white text-[#5F6F84] border-[#DCE2EB] hover:border-[#3F73F6] hover:bg-gray-50 cursor-pointer'
                        }`}
                      onClick={() => handleInputChange(fieldId, option.value, field, true)}
                      disabled={isRadioDisabled || isFieldDisabled}
                    >
                      <Image
                        src={
                          isSelected
                            ? isRadioDisabled || isFieldDisabled
                              ? '/assets/check-circle-gray.svg'
                              : '/assets/check-circle.svg'
                            : '/assets/circle.svg'
                        }
                        alt={isSelected ? 'Check' : 'Circle'}
                        width={20}
                        height={20}
                        className="flex-shrink-0"
                      />
                      <span className="text-sm text-left flex-1">{option.text}</span>
                    </button>
                  );
                })}
            </div>
            <div className="mt-2">{errorComponent}</div>
          </div>
        );

      case fieldTypes.BUTTON: {
        // eslint-disable-next-line no-case-declarations
        const isUpdateMyCasesButton = field.form_field_id === 'update-mycases';
        // Button disabled logic:
        // - Disable during loading (isRetrying)
        // - Disable after success (stored value === 'true')
        // - Enable after failure (stored value === 'failed')
        // - Respect general field disabled state
        const updateState = getFormDataValue(fieldId);
        const isClioButton = section.label === 'If the information above is correct, submit below to update Clio';
        const buttonDisabled = Boolean(
          isRetrying || isFieldDisabled || 
          (isClioButton ? (shouldShowClioMessages && updateState !== 'failed') : (updateState === 'true'))
        );
        const selectedEventMatterId = localStorage.getItem('SelectedEventMatterId');
        const isUpdated = !!matterOptions?.some(option => option.value === selectedEventMatterId);
        const isEventAdded = localStorage.getItem(`isEventAdded_${work_flow_id}`) === 'true';

        // Check if button has been processed (clicked and has a state)
        const hasBeenProcessed =
          getFormDataValue(fieldId) === 'true' ||
          getFormDataValue(fieldId) === 'failed' ||
          getFormDataValue(fieldId) === 'pending';

        // Show button if it meets original conditions OR if it has been processed
        const shouldShowButton =
          (isUpdated && isChildWorkflow) ||
          (!isUpdated && !isChildWorkflow) ||
          (isChildWorkflow && isEventAdded) ||
          hasBeenProcessed;

        return (
          <>
            {shouldShowButton && (
              <div key={fieldId} className="mb-3 ml-8">
                <button
                  className={`w-full h-[40px] py-[10px] rounded-[12px] text-[14px] leading-[20px] transition font-medium
                    ${buttonDisabled
                      ? 'text-[#A2AFC2] border border-[#D0D5DD] bg-[#EFF2F7] cursor-not-allowed'
                      : 'text-[#3F73F6] border border-[#3F73F6] hover:bg-blue-50 cursor-pointer'
                    }
                  `}
                  onClick={() => {
                    console.log('field dhyey 123', field);
                    
                    // Show Clio messages when Update Clio button is clicked
                    dispatch(showClioMessages());
                    
                    setPendingAction({
                      fieldId,
                      value: 'true',
                      field,
                      isRadioGroup: true,
                      actionType: 'UPDATE_MY_CASE',
                    });
                    if (isChildWorkflow) {
                      handleConfirmModalOk();
                    } else {
                      setShowConfirmModal(true);
                    }
                  }}
                  disabled={buttonDisabled}
                >
                  <span className="font-medium flex items-center gap-2 justify-center">
                    {field.label} {isRetrying && <Loader />}
                  </span>
                </button>
                {errorComponent}
              </div>
            )}
          </>
        );
      }

      case fieldTypes.CONFIRMATION:
        return (
          <div key={fieldId} className="mb-3 ml-8">
            <div className="p-3 bg-[#3f73f6] text-white rounded-[12px] border border-blue-200 flex items-center">
              <CustomCheckbox
                id={fieldId}
                checked={getFormDataBoolean(fieldId)}
                disabled={isFieldDisabled}
                borderColor={'#5F6F84'}
                onChange={e => handleInputChange(fieldId, e.target.checked, field)}
                className="mr-2"
              />
              <label htmlFor={fieldId} className="text-[14px] text-[#5f6f84] font-medium">
                {field.label}
              </label>
            </div>
            {errorComponent}
          </div>
        );

      case fieldTypes.ASSIGN:
        return (
          <div key={fieldId} className="mb-3 ml-8">
            <div className="mt-2 relative">
              <select
                id={fieldId}
                className={`${workflowStyles.selectInput} ${isInvalid ? 'border-red-500 focus:ring-red-500' : ''} ${conditionalDisabled(isFieldDisabled, 'input')} appearance-none block w-full px-3 py-2 border border-[#DCE2EB] focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                onChange={e => handleInputChange(fieldId, e.target.value, field)}
                onBlur={e => handleBlur(fieldId, e.target.value)}
                value={getFormDataValue(fieldId)}
                disabled={isFieldDisabled}
              >
                {field.options &&
                  Array.isArray(field.options) &&
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  field.options.map((option: any, optionIndex: number) => (
                    <option key={`${fieldId}-option-${optionIndex}`} value={option.value}>
                      {option.text}
                    </option>
                  ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
                <ChevronDown size={20} className="text-gray-500" />
              </div>
            </div>
            {errorComponent}
          </div>
        );

      case fieldTypes.SEARCHABLE_SELECT: {
        const isCurrentFieldActive = activeSearchField === fieldId;

        return (
          <div key={fieldId} className="mb-3 ml-8">
            <div
              className={`${workflowStyles.selectContainer} relative`}
              ref={isCurrentFieldActive ? dropdownRef : null}
            >
              <input
                type="text"
                className={`${workflowStyles.selectInput} ${isInvalid ? 'border-red-500 focus:ring-red-500' : ''} ${conditionalDisabled(isFieldDisabled, 'input')} appearance-none block w-full px-3 py-2 border border-[#DCE2EB] focus:outline-none focus:ring-blue-500 focus:border-blue-500 cursor-pointer`}
                placeholder={field.placeholder || 'Select an option'}
                value={
                  searchTerm ||
                  (getFormDataValue(fieldId)
                    ? searchableSelectOptions.find(opt => opt.value === getFormDataValue(fieldId))
                      ?.text || ''
                    : '')
                }
                onChange={e => {
                  setSearchTerm(e.target.value);
                  if (!isOpen) {
                    setIsOpen(true);
                    setActiveSearchField(fieldId);
                  }
                  // Fetch options based on search term
                  fetchSearchableOptions('fetchSearchableOptions', e.target.value);
                }}
                onFocus={() => {
                  if (!isFieldDisabled) {
                    setIsOpen(true);
                    setActiveSearchField(fieldId);
                    // Fetch initial options when field gets focus
                    fetchSearchableOptions('fetchSearchableOptions', searchTerm || '');
                  }
                }}
                onClick={e => e.stopPropagation()}
                disabled={isFieldDisabled}
                autoComplete="off"
              />
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
                <ChevronDown size={20} className="text-gray-500" />
              </div>

              {isOpen && isCurrentFieldActive && (
                <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg">
                  <div className="max-h-60 overflow-y-auto">
                    {isLoading ? (
                      <div className="p-4 text-center text-gray-500">Loading...</div>
                    ) : searchableSelectOptions.length > 0 ? (
                      searchableSelectOptions.map((option, i) => (
                        <div
                          key={`${fieldId}-option-${i}`}
                          className={`p-2 ${conditionalDisabled(isFieldDisabled, 'dropdown')} ${getFormDataValue(fieldId) === option.value
                            ? 'bg-blue-50 text-blue-600'
                            : ''
                            }`}
                          onClick={() => {
                            if (isFieldDisabled) return; // Prevent clicks when disabled
                            handleSearchSelectOption(fieldId, option.value, option.text, field);
                          }}
                        >
                          {option.text}
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-gray-500">No results found</div>
                    )}
                  </div>
                </div>
              )}
            </div>
            {errorComponent}
          </div>
        );
      }

      default:
        return null;
    }
  };

  const selectedEventMatterId = localStorage.getItem('SelectedEventMatterId');
  const matterList = localStorage.getItem('matterList');
  const parsedMatterList: Array<{ value: string | null }> = matterList
    ? JSON.parse(matterList)
    : [];
  const isUpdated = !!parsedMatterList.some(
    (option: { value: string | null }) => option.value === selectedEventMatterId
  );

  // Check if this section should be rendered based on conditions
  const shouldRenderThisSection = () => {
    if (!section.condition) {
      // For special messages, check custom visibility logic
      if (section.label === 'If the information above is correct, submit below to update MyCase' ||
          section.label === 'If the information above is correct, submit below to update Clio') {
        
        const isEventAdded = localStorage.getItem(`isEventAdded_${work_flow_id}`) === 'true';
        
        // For MyCase message, check if button should be visible
        if (section.label === 'If the information above is correct, submit below to update MyCase') {
          return section.fields.some(
            field =>
              field.type === 'button' &&
              field.form_field_id === 'update-mycases' &&
              ((isEventAdded && isChildWorkflow) ||
                (!isEventAdded && !isChildWorkflow) ||
                getFormDataValue(field._id) === 'true' ||
                getFormDataValue(field._id) === 'failed' ||
                getFormDataValue(field._id) === 'pending')
          );
        }
        
        // For Clio message, check if changes were made (original logic)
        if (section.label === 'If the information above is correct, submit below to update Clio') {
          return (isEventAdded && isChildWorkflow) || (!isEventAdded && !isChildWorkflow);
        }
      }
      return true;
    }
    return checkConditionMatch(section.condition);
  };

  // Don't render the section if it doesn't meet conditions
  if (!shouldRenderThisSection()) {
    return null;
  }

  // For message sections and confirmation sections, check if they should be shown
  const shouldShowMessage = section.type === 'success_message' || section.type === 'pending_message' || section.type === 'failed_message';
  const shouldShowConfirmation = (section.type === 'checkbox' || section.type === 'radio_button_group') && 
    section.label === 'Please confirm that the events above are updated appropriately';
  
  if ((shouldShowMessage || shouldShowConfirmation) && !shouldShowClioMessages) {
    return null;
  }

  return (
    <div className="flex w-full mt-4">
      <div className="flex-1">
        {section.type === 'instruction' ? (
          <div className="flex items-end">
            <div className="mr-2 self-end">
              <Image src="/assets/ai-robot-new-2.svg" alt="Bot" width={24} height={24} />
            </div>
            <div className="talk-bubble-instruction tri-right-instruction round btm-left">
              <div className="talktext">
                <p>
                  {section.label}
                  {sectionRequiredIndicator}
                </p>
              </div>
            </div>
          </div>
        ) : section.type === 'success_message' ? (
          <div className="flex items-end">
            <div className="mr-2 self-end">
              <Image src="/assets/ai-robot-new-2.svg" alt="Bot" width={24} height={24} />
            </div>
            <div
              className="talk-bubble-green tri-right-green round btm-left"
              style={{ backgroundColor: '#8CF1BD' }}
            >
              <div className="talktext">
                <p style={{ color: '#2A2E34' }}>
                  {section.label}
                  {sectionRequiredIndicator}
                </p>
              </div>
            </div>
          </div>
        ) : section.type === 'pending_message' ? (
          <div className="flex items-end">
            <div className="mr-2 self-end">
              <Image src="/assets/ai-robot-new-2.svg" alt="Bot" width={24} height={24} />
            </div>
            <div
              className="talk-bubble-green tri-right-blue round btm-left"
              style={{ backgroundColor: '#97C7FF' }}
            >
              <div className="talktext">
                <p style={{ color: '#2A2E34' }}>
                  {section.label}
                  {sectionRequiredIndicator}
                </p>
              </div>
            </div>
          </div>
        ) : section.type === 'failed_message' ? (
          <div className="flex items-end">
            <div className="mr-2 self-end">
              <Image src="/assets/ai-robot-new-2.svg" alt="Bot" width={24} height={24} />
            </div>
            <div
              className="talk-bubble-green tri-right-red round btm-left"
              style={{ backgroundColor: '#EF8B8B' }}
            >
              <div className="talktext">
                <p style={{ color: '#2A2E34' }}>
                  {section.label}
                  {sectionRequiredIndicator}
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-end w-full">
            {/* Bot Icon */}
            <div className="mr-2 self-end">
              <Image
                src="/assets/ai-robot-new-2.svg"
                alt="Bot"
                width={24}
                height={24}
                className="!w-6 !h-6 !min-w-[24px] !min-h-[24px]"
              />
            </div>

            {/* Message Bubble */}
            <div className="talk-bubble tri-right round btm-left">
              <div className="talktext">
                {section.label ===
                  'Confirm that the court notice dates above and the court notice files are entered in MyCase appropriately, if not enter manually. Click here to access MyCase.' ||
                  section.label ===
                  'Please update MyCase calendar and upload court notice file manually. Click here to access MyCase.' ||
                  section.label ===
                  'Confirm that the court notice dates above and the court notice files are entered in Clio appropriately, if not enter manually. Click here to access Clio.' ||
                  section.label ===
                  'Please update Clio calendar and upload court notice file manually. Click here to access Clio.' ? (
                  <div className="ml-auto pl-2 flex items-center gap-2">
                    <p className="text-[14px] leading-[20px]">
                      {section.label.includes('Click here to access Clio.')
                        ? section.label.split('Click here to access Clio.')[0]
                        : section.label.split('Click here to access MyCase.')[0]}
                      <a
                        href={
                          section.label.includes('Clio')
                            ? 'https://app.clio.com/nc/#/calendars'
                            : `https://firmprofit.mycase.com/court_cases/${ClientMatterId}/calendar`
                        }
                        className="text-white font-semibold"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {section.label.includes('Clio')
                          ? 'Click here to access Clio.'
                          : 'Click here to access MyCase.'}
                      </a>
                      {sectionRequiredIndicator}
                    </p>
                  </div>
                ) : (
                  <p className="text-[14px] leading-[20px]">
                    {section.label}
                    {sectionRequiredIndicator}
                  </p>
                )}
              </div>
            </div>

            {/* Conditional icons for specific labels */}
            {section.label === 'Send the following SMS via Kenect:' && (
              <div className="ml-auto pl-2 self-center">
                <Image src="/assets/ai (1).svg" alt="Star" width={20} height={20} />
              </div>
            )}

            {section.label === 'Enter notes below if any' && (
              <div className="ml-auto pl-2 flex items-center gap-2">
                <Image src="/assets/ai (1).svg" alt="Star" width={20} height={20} />
                <Image src="/assets/file-05 (1).svg" alt="File" width={20} height={20} />
              </div>
            )}

            {section.label ===
              'Confirm that the court notice dates above and the court notice files are entered in MyCase appropriately, if not enter manually. Click here to access MyCase.' && (
                <div className="ml-auto pl-2 flex items-center gap-2"></div>
              )}
          </div>
        )}

        <style>{`
  .talk-bubble,
  .talk-bubble-instruction {
    margin: 0px 0 6px;
    display: inline-block;
    position: relative;
    width: auto;
    min-width: 200px;
    height: auto;
    border-radius: 12px;
    border-bottom-left-radius: 0px;
  }

  .talk-bubble {
    background-color: #3f73f6;
  }

  .talk-bubble-instruction {
    background-color: #f3f5f9;
  }

  /* Bubble triangle */
  .tri-right.btm-left:after,
  .tri-right-instruction.btm-left:after {
    content: ' ';
    position: absolute;
    width: 0;
    height: 0;
    left: 0px;
    bottom: -6px;
    border: 6px solid;
  }

  .tri-right.btm-left:after {
    border-color: transparent transparent transparent #3f73f6;
  }

  .tri-right-instruction.btm-left:after {
    border-color: transparent transparent transparent #f3f5f9;
  }

  /* Success message triangle override */
  .talk-bubble[style*="background-color: #8cf1bd"] .tri-right.btm-left:after {
    border-color: transparent transparent transparent #8cf1bd;
  }

 .talktext {
          padding: 8px 14px;
          text-align: left;
        }

        .talktext p {
          margin: 0;
          font-size: 14px;
          line-height: 20px;
          font-weight: normal;
          color: #fff;
        }

  // .talktext p {
  //   margin: 0;
  //   font-size: 14px;
  //   font-weight: normal;
  //   color: #fff;
  // }

  .talktext-third p {
    color: #2a2e34;
  }

  .talk-bubble-instruction .talktext p {
    color: #2a2e34;
  }


////////////// GREEN BUBBLE //////////////////

  .talk-bubble-green,
  .talk-bubble-green-instruction {
    margin: 10px 0;
    display: inline-block;
    position: relative;
    width: auto;
    min-width: 200px;
    height: auto;
    border-radius: 12px;
    border-bottom-left-radius: 0px;
  }

  .talk-bubble-green {
    background-color: #8CF1BD;
  }
  .talk-bubble-blue {
    background-color: #97C7FF;
  }
  .talk-bubble-red {
    background-color: #EF8B8B;
  }

  .talk-bubble-green-instruction {
    background-color: #f3f5f9;
  }

  /* Success message triangle override */
/* Success message triangle override */
.talk-bubble-green[style*="background-color: #8cf1bd"] .tri-right-green.btm-left:after {
  border-color: transparent transparent transparent #8cf1bd;
}
.tri-right-green.btm-left:after {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: 0px;
  bottom: -8px;
  border: 10px solid;
  border-color: transparent transparent transparent #8CF1BD; /* Green for success */
}

.tri-right-red.btm-left:after {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: 0px;
  bottom: -8px;
  border: 10px solid;
  border-color: transparent transparent transparent #EF8B8B; /* Red for failed */
}

.tri-right-blue.btm-left:after {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: 0px;
  bottom: -8px;
  border: 10px solid;
  border-color: transparent transparent transparent #97C7FF; /* Blue for pending */
}

  .talk-bubble-green-instruction .talktext p {
    color: #2a2e34;
  }

  .talk-bubble-green-instruction .talktext-third p {
    color: #2a2e34;
  }

  /* Success message triangle override */
  .talk-bubble-green[style*="background-color: #8cf1bd"] .tri-right-green-third.btm-left:after {
    border-color: transparent transparent transparent #8cf1bd;
  }
          
  `}</style>

        <div className={`${workflowStyles.inputsContainer} mt-[12px]`}>
          {section.type === fieldTypes.FULLNAME ? (
            <div className="flex items-end mb-2">
              {/* <div className="mr-2">
                <Image src="/assets/ai-robot-new-2.svg" alt="Bot" width={24} height={24} />
              </div> */}
              <div className={`${workflowStyles.multiPartContainer} grid grid-cols-3 gap-3`}>
                {section.fields
                  .slice(1)
                  .map((field, fieldIndex) => renderField(field, fieldIndex + 1))}
              </div>
            </div>
          ) : section.type === fieldTypes.COURT_NOTICE ? (
            // For court notice sections, just pass the first field
            section.fields.length > 0 ? (
              renderField(section.fields[0], 0)
            ) : null
          ) : (
            section.fields.map((field, fieldIndex) => renderField(field, fieldIndex))
          )}
        </div>
      </div>

      {/* Confirmation Modal for Update MyCase */}
      <ReusableAlertModal
        isOpen={showConfirmModal}
        title="Confirm Update"
        message="Are you sure you want to update MyCase? This action will prevent any further edits to event fields."
        onOk={handleConfirmModalOk}
        onCancel={handleConfirmModalCancel}
        okText="Yes, Update"
        cancelText="Cancel"
      />
    </div>
  );
};

export default WorkflowField;
