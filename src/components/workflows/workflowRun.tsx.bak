import { useRouter } from 'next/router';
import { User, Plus, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, ChevronUp, ChevronDown } from 'lucide-react';
import WorkflowField from './WorkflowField';
// import axios from 'axios';
import Image from 'next/image';
// import workflowsData from '@/data/workflowData';
import { AdvisorPanel } from './index';
import { useCallback, useEffect, useRef, useState } from 'react';
import axios from 'axios';
import CustomCheckbox from '../common/CustomCheckbox';
import DateTimeModal from '../common/DateTimeModal';
// import workflowsData from '@/data/workflowData';

// Validation type
type Validation = {
  minLength?: number;
  min?: number;
  max?: number;
  pattern?: string;
  errorMessage?: string;
};

// Define the interfaces for workflow data types
interface FormField {
  type: string;
  placeholder?: string;
  id: string;
  _id: string;
  label?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  value?: string | boolean | number | Array<{ id: string; value: any; _id?: string }>;
  options?: Array<{ value: string; text: string }>;
  required?: boolean;
  validation?: Validation;
}

// Define NoticeEvent type for the notice summary
interface NoticeEvent {
  name: string;
  count: number;
}

// Define NoticeInfo type
interface NoticeInfo {
  events?: NoticeEvent[];
}

// Define EmailInfo type
interface EmailInfo {
  email_from?: string;
  email_to?: string;
  email_subject?: string;
  email_body?: string;
  document?: Array<{
    name: string;
    size: string;
  }>;
  notice_summary?: NoticeInfo;
}

// Define condition type
type Condition =
  | {
      field: string;
      value: string | boolean | number;
    }
  | {
      type: 'AND' | 'OR';
      conditions: Array<Condition>;
    };

interface FormSection {
  id?: string;
  _id?: string;
  type: string;
  label: string;
  fields: FormField[];
  condition?: Condition;
  dynamic_fields?: boolean;
  dynamic_selected_task?: boolean;
  selected_task?: boolean;
  required?: boolean;
}

interface Task {
  id: string;
  name: string;
  description: string;
  icon: string;
  status: string;
  formFields: FormSection[];
  default_task?: boolean;
  selected_task?: boolean;
  work_flow_id?: string;
  task_visible_status: string;
  condition_task?: {
    id: string;
    value: string | boolean | number;
  };
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
interface Workflow {
  id: string;
  name: string;
  description: string;
  tasks: Task[];
  end_date?: string;
}

// New interface to support all workflow types
interface WorkflowData {
  workflows: Workflow[];
  child_workflow_1?: Workflow[];
  child_workflow_2?: Workflow[];
  options?: {
    courtNoticeTypes: Array<{ value: string; text: string }>;
    appointmentActions: Array<{ value: string; text: string }>;
    counties: Array<{ value: string; text: string; _id?: string; is_active?: boolean }>;
    courtLocations: Array<{ value: string; text: string; _id?: string; is_active?: boolean }>;
    attendees: Array<{ value: string; text: string; _id?: string; is_active?: boolean }>;
    clientAttendanceOptions: Array<{ value: string; text: string }>;
    meetingLocations: Array<{ value: string; text: string }>;
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: Workflow[] | undefined | any; // Added any to accommodate options
}

// New interface to support child workflow context
interface WorkflowContext {
  workflow: Workflow;
  type: 'main' | 'child1' | 'child2' | string;
  index: number;
}

// interface WorkflowDataType {
//   workflows: Workflow[];
// }

// interface ApiResponse {
//   data: WorkflowDataType;
//   statusCode: number;
//   message: string;
// }

// Simple resizing handle component
const ResizeHandle = ({
  onMouseDown,
  orientation = 'vertical',
}: {
  onMouseDown: (e: React.MouseEvent) => void;
  orientation?: 'vertical' | 'horizontal';
}) => {
  return (
    <div
      className={`resize-handle ${
        orientation === 'vertical'
          ? 'cursor-col-resize w-1 hover:w-2 h-full bg-gray-200 hover:bg-blue-400 active:bg-[#3F73F6] transition-all z-10 relative group'
          : 'cursor-row-resize h-1 hover:h-2 w-full bg-gray-200 hover:bg-blue-400 active:bg-[#3F73F6] transition-all z-10 relative group'
      }`}
      onMouseDown={onMouseDown}
    >
      {/* Resize indicator dots */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
        {orientation === 'vertical' ? (
          <div className="flex flex-col items-center space-y-1">
            <div className="w-1 h-1 rounded-full bg-gray-600"></div>
            <div className="w-1 h-1 rounded-full bg-gray-600"></div>
            <div className="w-1 h-1 rounded-full bg-gray-600"></div>
          </div>
        ) : (
          <div className="flex items-center space-x-1">
            <div className="w-1 h-1 rounded-full bg-gray-600"></div>
            <div className="w-1 h-1 rounded-full bg-gray-600"></div>
            <div className="w-1 h-1 rounded-full bg-gray-600"></div>
          </div>
        )}
      </div>
    </div>
  );
};

interface WorkflowRunProps {
  initialTaskId?: string;
}

export default function ContactForm({ initialTaskId }: WorkflowRunProps) {
  const router = useRouter();
  const { work_flow_id, taskId } = router.query;
  const getWorkflowIdFromUrl = () => {
    const { work_flow_id } = router.query;
    return work_flow_id ? String(work_flow_id) : '';
  };

  // Main state for workflow data
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);

  // New state for all workflows
  const [workflowData, setWorkflowData] = useState<WorkflowData | null>(null);
  const [activeWorkflowContext, setActiveWorkflowContext] = useState<WorkflowContext | null>(null);
  const [childWorkflows, setChildWorkflows] = useState<{ [key: string]: Workflow[] }>({});

  // Add state for showing all workflows toggle
  const [showAllWorkflows, setShowAllWorkflows] = useState(false);
  const showAllWorkflowsRef = useRef(false);
  const [isDataLoading, setIsDataLoading] = useState(false);
  const loadingTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const userTriggeredFetch = useRef(false);  // Add this to track if fetch was triggered by user

  // Store initial task and workflow IDs
  const [initialTaskIdState, setInitialTaskIdState] = useState<string | null>(
    initialTaskId || null
  );
  const [initialWorkflowId, setInitialWorkflowId] = useState<string | null>(null);
  const [initialActiveContext, setInitialActiveContext] = useState<WorkflowContext | null>(null);

  // Add new state for options
  const [apiOptions, setApiOptions] = useState<WorkflowData['options'] | null>(null);

  // Form-related state
  const [formFields, setFormFields] = useState<FormSection[]>([]);
  const [formData, setFormData] = useState<Record<string, string | boolean | number>>({});
  const [formHasErrors, setFormHasErrors] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [showFilter, setShowFilter] = useState(false);
  const [expanded] = useState(true);
  const [emailInfo, setEmailInfo] = useState<EmailInfo | null>(null);
  const [noticeSummary, setNoticeSummary] = useState<Record<string, number> | null>(null);
  const [canComplete, setCanComplete] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [courtNoticeEventsCount, setCourtNoticeEventsCount] = useState(0);

  // Resizable panels state
  const [leftPanelWidth, setLeftPanelWidth] = useState(404); // Initial width in pixels
  const [centerPanelWidth, setCenterPanelWidth] = useState(50); // Initial width in percentage
  const [isResizingLeft, setIsResizingLeft] = useState(false);
  const [isResizingCenter, setIsResizingCenter] = useState(false);
  const [showDateModal, setShowDateModal] = useState(false); // Add this line
  const leftPanelRef = useRef<HTMLDivElement>(null);
  const centerPanelRef = useRef<HTMLDivElement>(null);
  const rightPanelRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [reviewTask, setReviewTask] = useState<string[]>([]);
  const filterRef = useRef<HTMLDivElement>(null);

  // const [activeIcon, setActiveIcon] = useState(0);

  const [filters, setFilters] = useState({
    myTasks: true,
    unassigned: true,
    allTasks: false,
  });

  const handleFilterChange = (filter: keyof typeof filters) => {
    setFilters({
      ...filters,
      [filter]: !filters[filter],
    });
  };

  const applyFilters = () => {
    // Logic to apply filters
    setShowFilter(false);
  };

  const sidebarIcons = [
    {
      icon: <Image src="/IconsBar/ai.svg" alt="Calendar" width={25} height={25} />,
      active: true,
    },
    {
      icon: <Image src="/IconsBar/contacts-two-toned.svg" alt="Calendar" width={25} height={25} />,
      active: false,
    },
    {
      icon: (
        <Image src="/IconsBar/office-bag-two-toned.svg" alt="Calendar" width={25} height={25} />
      ),
      active: false,
    },
    {
      icon: <Image src="/IconsBar/file-edit-two-toned.svg" alt="Calendar" width={25} height={25} />,
      active: false,
    },
    {
      icon: <Image src="/IconsBar/communication.svg" alt="Calendar" width={25} height={25} />,
      active: false,
    },
    {
      icon: <Image src="/IconsBar/file-two-toned.svg" alt="Calendar" width={25} height={25} />,
      active: false,
    },
    {
      icon: <Image src="/IconsBar/calendar-two-toned.svg" alt="Calendar" width={25} height={25} />,
      active: false,
    },
    {
      icon: <Image src="/IconsBar/billing-two-toned.svg" alt="Calendar" width={25} height={25} />,
      active: false,
    },
    {
      icon: <Image src="/IconsBar/tool-two-toned.svg" alt="Calendar" width={25} height={25} />,
      active: false,
    },
  ];

  // After line 1066, update the state management to include collapsed state information
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({
    main: true, // Main workflow is expanded by default
  });

  // New function to toggle section expansion
  const toggleSectionExpansion = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey],
    }));
  };

  // Handler for when mouse is pressed on the left resize handle
  const handleLeftResizeStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizingLeft(true);
    console.log(saving, saveError);
  }, []);

  // Handler for when mouse is pressed on the center resize handle
  const handleCenterResizeStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizingCenter(true);
  }, []);

  // Handler for mouse movement during resize
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isResizingLeft && containerRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect();
        const newWidth = Math.max(
          250,
          Math.min(e.clientX - containerRect.left, containerRect.width * 0.5)
        );
        setLeftPanelWidth(newWidth);
      } else if (
        isResizingCenter &&
        containerRef.current &&
        centerPanelRef.current &&
        rightPanelRef.current
      ) {
        const containerRect = containerRef.current.getBoundingClientRect();
        const availableWidth = containerRect.width - leftPanelWidth;
        const mousePositionRelativeToRightSection = e.clientX - containerRect.left - leftPanelWidth;
        const newCenterWidthPercent = Math.max(
          20,
          Math.min((mousePositionRelativeToRightSection / availableWidth) * 100, 80)
        );
        setCenterPanelWidth(newCenterWidthPercent);
      }
    },
    [isResizingLeft, isResizingCenter, leftPanelWidth]
  );

  // Handler for mouse up to stop resizing
  const handleMouseUp = useCallback(() => {
    setIsResizingLeft(false);
    setIsResizingCenter(false);

    // Clean up any resize-related classes and styles
    document.body.classList.remove('resizing-horizontal', 'resizing-vertical');
    document.body.style.userSelect = '';

    // Save the resize preferences in localStorage for persistence
    // if (leftPanelWidth && centerPanelWidth) {
    //   try {
    //     localStorage.setItem('workflowLeftPanelWidth', leftPanelWidth.toString());
    //     localStorage.setItem('workflowCenterPanelWidth', centerPanelWidth.toString());
    //   } catch (e) {
    //     console.log(e);
    //     // Silent fail if localStorage is not available
    //   }
    // }
  }, [leftPanelWidth, centerPanelWidth]);

  // Update initialTaskIdState when prop changes
  useEffect(() => {
    if (initialTaskId) {
      setInitialTaskIdState(initialTaskId);
    }
  }, [initialTaskId]);

  // Load saved panel dimensions on mount
  useEffect(() => {
    try {
      // const savedLeftWidth = localStorage.getItem('workflowLeftPanelWidth');
      // const savedCenterWidth = localStorage.getItem('workflowCenterPanelWidth');
      // if (savedLeftWidth) {
      //   const parsedWidth = parseInt(savedLeftWidth, 10);
      //   if (!isNaN(parsedWidth) && parsedWidth >= 250) {
      //     setLeftPanelWidth(parsedWidth);
      //   }
      // }
      // if (savedCenterWidth) {
      //   const parsedWidth = parseInt(savedCenterWidth, 10);
      //   if (!isNaN(parsedWidth) && parsedWidth >= 20 && parsedWidth <= 80) {
      //     setCenterPanelWidth(parsedWidth);
      //   }
      // }
    } catch (e) {
      console.log(e);
      // Silent fail if localStorage is not available
    }
  }, []);

  // Set up event listeners for resize
  useEffect(() => {
    if (isResizingLeft || isResizingCenter) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      // Add classes to body during resize
      if (isResizingLeft) {
        document.body.classList.add('resizing-horizontal');
      } else if (isResizingCenter) {
        document.body.classList.add('resizing-horizontal');
      }

      // Disable text selection during resize
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Clean up when unmounting
      document.body.classList.remove('resizing-horizontal');
      document.body.style.userSelect = '';
    };
  }, [isResizingLeft, isResizingCenter, handleMouseMove, handleMouseUp]);

  // Add a separate effect to update global CSS
  useEffect(() => {
    // Create a style element for the custom resize cursor
    const styleElement = document.createElement('style');
    styleElement.innerHTML = `
      .resizing-horizontal {
        cursor: col-resize !important;
      }
      .resizing-vertical {
        cursor: row-resize !important;
      }
      .resize-handle {
        position: relative;
        transition: background-color 0.2s;
      }
      .resize-handle:hover {
        background-color: rgba(59, 130, 246, 0.5) !important;
      }
    `;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // Function to fetch workflow data with all child workflows
  const fetchWorkflowData = useCallback(async () => {
    // Save current toggle state before fetching
    const currentToggleState = showAllWorkflowsRef.current;
    
    setLoading(true);
    try {
      // Make the API call to get workflow data
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-render`,
        {
          params: {
            work_flow_id: work_flow_id,
          },
        }
      );

      // Use the API response data
      const allWorkflowData = response?.data?.data as unknown as WorkflowData;
      setWorkflowData(allWorkflowData);

      // Store options from API if available
      if (allWorkflowData.options) {
        setApiOptions(allWorkflowData.options);
        console.log('Options from API:', allWorkflowData.options);
      }

      // Extract child workflows
      const childWorkflowsObj: { [key: string]: Workflow[] } = {};
      Object.keys(allWorkflowData).forEach(key => {
        if (key.startsWith('child_workflow_') && allWorkflowData[key]) {
          childWorkflowsObj[key] = allWorkflowData[key] as Workflow[];
        }
      });
      setChildWorkflows(childWorkflowsObj);

      // Check if we have a task ID in the URL that might be in a child workflow
      const urlTaskId = router.query.taskId as string;
      let foundInChildWorkflow = false;

      if (urlTaskId) {
        // Save initial taskId when first loading
        if (!initialTaskIdState) {
          setInitialTaskIdState(urlTaskId);
          setInitialWorkflowId(getWorkflowIdFromUrl());
        }

        // First check if the task is in any of the child workflows
        for (const key in childWorkflowsObj) {
          const childWorkflows = childWorkflowsObj[key];
          for (let i = 0; i < childWorkflows.length; i++) {
            const childWorkflow = childWorkflows[i];
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const childTask = childWorkflow.tasks.find((t: any) => t.id === urlTaskId);
            if (childTask) {
              // Set the child workflow as active
              const context = {
                workflow: childWorkflow,
                type: key,
                index: i,
              };

              setActiveWorkflowContext(context);

              // Store initial context for toggle functionality
              if (!initialActiveContext) {
                setInitialActiveContext(context);
              }

              // Update selected workflow
              setSelectedWorkflow(childWorkflow);

              foundInChildWorkflow = true;
              break;
            }
          }
          if (foundInChildWorkflow) break;
        }
      }

      // If we didn't find the task in a child workflow, use the main workflow
      if (!foundInChildWorkflow) {
        // Set the main workflow as active by default
        if (allWorkflowData.workflows.length > 0) {
          const context = {
            workflow: allWorkflowData.workflows[0],
            type: 'main',
            index: 0,
          };

          setSelectedWorkflow(allWorkflowData.workflows[0]);
          setActiveWorkflowContext(context);

          // Store initial context for toggle functionality
          if (!initialActiveContext) {
            setInitialActiveContext(context);
          }
        }
      }

      // Store email info in state if available
      if (allWorkflowData.email_info) {
        setEmailInfo(allWorkflowData.email_info as unknown as EmailInfo);
      }

      // Store notice summary in state if available
      if (allWorkflowData.notice_summary) {
        setNoticeSummary(allWorkflowData.notice_summary as unknown as Record<string, number>);
      }
      
      // Restore toggle state after fetching is complete
      if (currentToggleState) {
        setShowAllWorkflows(true);
      }
    } catch (err) {
      console.error('Error fetching workflow data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch workflow data');
      
      // Also restore toggle state in case of error
      if (currentToggleState) {
        setShowAllWorkflows(true);
      }
    } finally {
      setLoading(false);
    }
  }, [
    work_flow_id,
    router.query.taskId,
    initialTaskIdState,
    initialWorkflowId,
    initialActiveContext,
  ]);

  // Fetch workflow data from API when component mounts
  useEffect(() => {
    // Only fetch if we have a work_flow_id or we're on the workflow page
    if (router.isReady && work_flow_id) {
      console.log('Fetching workflow data with ID:', work_flow_id);
      userTriggeredFetch.current = true; // First load should show loader
      fetchWorkflowData();
    } else if (router.isReady) {
      // If no work_flow_id, still try to load for development/testing
      console.log('Fetching workflow data without ID (development mode)');
      userTriggeredFetch.current = true; // First load should show loader
      fetchWorkflowData();
    }
  }, [router.isReady, work_flow_id, fetchWorkflowData]);

  // When workflow changes or task ID from URL changes, select the appropriate task
  useEffect(() => {
    if (selectedWorkflow) {
      // Skip task selection if we already have a selected task that matches the criteria
      // This helps prevent an infinite update loop
      const currentTaskId = selectedTask?.id;

      // Priority 1: Use initialTaskId if provided (from server-side props)
      if (initialTaskIdState && initialTaskIdState !== currentTaskId) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const task = selectedWorkflow.tasks.find((t: any) => t.id === initialTaskIdState);
        if (task) {
          selectTask(task);
          return;
        }
      }

      // Priority 2: Use taskId from query params if available
      if (taskId && typeof taskId === 'string' && taskId !== currentTaskId) {
        // First check if this taskId belongs to a child workflow
        if (workflowData) {
          // Search all child workflows for a matching task first
          for (const key in workflowData) {
            if (key.startsWith('child_workflow_') && workflowData[key]) {
              const childWorkflows = workflowData[key] as Workflow[];
              for (let i = 0; i < childWorkflows.length; i++) {
                const childWorkflow = childWorkflows[i];
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const childTask = childWorkflow.tasks.find((t: any) => t.id === taskId);
                if (childTask) {
                  console.log(`Found task ${taskId} in ${key}, setting as active context`);

                  // Set the child workflow as active FIRST
                  setActiveWorkflowContext({
                    workflow: childWorkflow,
                    type: key,
                    index: i,
                  });

                  // Update selected workflow
                  setSelectedWorkflow(childWorkflow);

                  // Select the task
                  selectTask(childTask);
                  return;
                }
              }
            }
          }
        }

        // Then try to find in current workflow
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const task = selectedWorkflow.tasks.find((t: any) => t.id === taskId);
        if (task) {
          // If this is the main workflow, explicitly set to main
          if (workflowData?.workflows?.includes(selectedWorkflow)) {
            setActiveWorkflowContext({
              workflow: selectedWorkflow,
              type: 'main',
              index: 0,
            });
          }
          selectTask(task);
          return;
        }
      }

      // Priority 3: If this is a child workflow, select the first task
      if (
        activeWorkflowContext?.type.startsWith('child_workflow_') &&
        !currentTaskId &&
        selectedWorkflow.tasks.length > 0
      ) {
        console.log('Selecting first task of child workflow:', selectedWorkflow.tasks[0]);
        selectTask(selectedWorkflow.tasks[0]);
      }

      // Priority 4: Select only the first task initially for main workflow
      if (!currentTaskId && selectedWorkflow.tasks.length > 0) {
        const firstTask = selectedWorkflow.tasks[0];
        selectTask(firstTask);
      }
    }
  }, [selectedWorkflow, taskId, initialTaskIdState, activeWorkflowContext, workflowData]);

  // New useEffect to log reviewTask changes
  useEffect(() => {
    // When reviewTask changes, also see if there are any tasks in child workflows
    // that should be updated
    if (workflowData) {
      Object.keys(workflowData).forEach(key => {
        if (key.startsWith('child_workflow_') && workflowData[key]) {
          const childWorkflows = workflowData[key] as Workflow[];
          childWorkflows.forEach(workflow => {
            workflow.tasks.forEach(task => {
              if (reviewTask.includes(task.id)) {
                // This task should be marked as reviewed/completed
                task.status = 'completed';
              }
            });
          });
        }
      });
    }
  }, [reviewTask, workflowData]);

  // Function to select a task and update URL
  const selectTask = (task: Task) => {
    // If this task is already selected, do nothing to prevent an infinite loop
    if (selectedTask?.id === task.id) {
      return;
    }

    setSelectedTask(task);

    // Make sure we have form fields from the task
    if (task.formFields) {
      setFormFields(task.formFields);
    }

    // Reset validation state when switching tasks
    setFormHasErrors(false);
    setValidationErrors({});

    // Reset canComplete state when changing tasks
    setCanComplete(false);

    // Initialize form data with values from API
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const initialFormData: Record<string, any> = {};

    // Extract all field values from the task's form fields
    if (task.formFields) {
      task.formFields.forEach(section => {
        if (section.fields && section.fields.length > 0) {
          section.fields.forEach(field => {
            if (field.value !== undefined) {
              if (Array.isArray(field.value) && field.value.length > 0) {
                // Extract the value from the first item in the array
                initialFormData[field._id] = field.value[0].value;
              } else {
                // Handle non-array values
                initialFormData[field._id] = field.value;
              }
            }
          });
        }
      });
    }

    // Set the form data with these initial values
    setFormData(initialFormData);

    // Update URL with task ID without triggering a full page reload
    if (router.query.taskId !== task.id) {
      router.push(
        {
          pathname: router.pathname,
          query: { ...router.query, taskId: task.id },
        },
        undefined,
        { shallow: true }
      );
    }
  };

  // console.log("=============formData=============",formData)

  // Function to update task status in the workflow
  const updateTaskStatus = (
    taskId: string,
    status: 'active' | 'pending' | 'completed' | 'skipped'
  ) => {
    if (!selectedWorkflow) return;

    // Create a deep copy of the workflow to update its tasks
    const updatedWorkflow = {
      ...selectedWorkflow,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      tasks: selectedWorkflow.tasks.map((task: any) => {
        if (task.id === taskId) {
          return { ...task, status };
        }
        return task;
      }),
    };

    setSelectedWorkflow(updatedWorkflow);
  };

  // Update the handleTaskClick function to preserve the toggle state
  const handleTaskClick = (workflowType: string, workflowIndex: number, taskIndex: number) => {
    // Prevent clicking during loading/saving operations
    if (loading || saving) {
      return;
    }

    let targetWorkflow: Workflow | undefined;
    let targetTask: Task | undefined;

    if (workflowType === 'main' && workflowData?.workflows) {
      // Handle main workflow
      targetWorkflow = workflowData.workflows[workflowIndex];
      if (targetWorkflow && targetWorkflow.tasks && targetWorkflow.tasks.length > taskIndex) {
        targetTask = targetWorkflow.tasks[taskIndex];
      }
    } else if (workflowData) {
      // Handle child workflow
      const childWorkflows = workflowData[workflowType] as Workflow[] | undefined;
      if (childWorkflows && childWorkflows.length > workflowIndex) {
        targetWorkflow = childWorkflows[workflowIndex];
        if (targetWorkflow && targetWorkflow.tasks && targetWorkflow.tasks.length > taskIndex) {
          targetTask = targetWorkflow.tasks[taskIndex];
        }
      }
    }

    if (targetWorkflow && targetTask) {
      // If we're already on this task, don't re-select it
      if (selectedTask?.id === targetTask.id) {
        return;
      }

      console.log(`Setting activeWorkflowContext to ${workflowType} for task ${targetTask.name}`);

      // Important: Update the active workflow context FIRST before setting selected workflow
      // Update the active workflow context
      setActiveWorkflowContext({
        workflow: targetWorkflow,
        type: workflowType,
        index: workflowIndex,
      });

      // Update the selected workflow
      setSelectedWorkflow(targetWorkflow);

      // Mark the task as active if it's not already completed or skipped
      if (targetTask.status !== 'completed' && targetTask.status !== 'skipped') {
        updateTaskStatus(targetTask.id, 'active');
      }

      // Reset form state when changing tasks
      setFormHasErrors(false);
      setValidationErrors({});

      // Since we're changing tasks manually, reset any save state
      setSaveSuccess(false);
      setSaveError(null);

      // Preserve the toggle state when switching tasks
      const currentToggleState = localStorage.getItem('workflowToggleState') === 'true';
      if (currentToggleState) {
        setShowAllWorkflows(true);
      }
      
      // Select the task - make sure this happens AFTER setting workflow context
      selectTask(targetTask);

      // Update URL to reflect the selected task
      router.push(
        {
          pathname: router.pathname,
          query: { ...router.query, taskId: targetTask.id },
        },
        undefined,
        { shallow: true }
      );
    } else {
      console.error('Could not find target workflow or task', {
        workflowType,
        workflowIndex,
        taskIndex,
      });
    }
  };

  // Check if a condition is met based on the formData
  // eslint-disable-next-line
  const checkConditionMatch = (condition: Condition): boolean => {
    // Handle AND condition type
    if ('type' in condition && condition.type === 'AND') {
      return condition.conditions.every(c => checkConditionMatch(c));
    }

    // Handle OR condition type
    if ('type' in condition && condition.type === 'OR') {
      return condition.conditions.some(c => checkConditionMatch(c));
    }

    // Handle simple field/value condition
    if ('field' in condition && 'value' in condition) {
      return formData[condition.field] === condition.value;
    }

    return false;
  };

  // Get all visible fields based on current formData
  // const getVisibleFields = (): FormField[] => {
  //   if (!selectedTask) return [];

  //   return selectedTask.formFields
  //     .filter(section => !section.condition || checkConditionMatch(section.condition))
  //     .flatMap(section => section.fields);
  // };

  // Validate a specific field
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  // const validateField = (field: FormField, value: any): string => {
  //   // Check required fields
  //   if (field.required && (value === undefined || value === null || value === '')) {
  //     return 'This field is required';
  //   }

  //   // Skip validation if the field is empty and not required
  //   if (!field.required && (value === undefined || value === null || value === '')) {
  //     return '';
  //   }

  //   // Apply validation rules if they exist
  //   if (field.validation) {
  //     const validation = field.validation;

  //     // String length validation
  //     if (
  //       typeof value === 'string' &&
  //       validation.minLength &&
  //       value.length < validation.minLength
  //     ) {
  //       return `Minimum length is ${validation.minLength} characters`;
  //     }

  //     // Numeric range validation
  //     if (typeof value === 'number') {
  //       if (validation.min !== undefined && value < validation.min) {
  //         return `Minimum value is ${validation.min}`;
  //       }
  //       if (validation.max !== undefined && value > validation.max) {
  //         return `Maximum value is ${validation.max}`;
  //       }
  //     }

  //     // Pattern validation
  //     if (typeof value === 'string' && validation.pattern) {
  //       const regex = new RegExp(validation.pattern);
  //       if (!regex.test(value)) {
  //         return validation.errorMessage || 'Invalid format';
  //       }
  //     }
  //   }

  //   return '';
  // };

  // Validate all visible form fields
  // const validateForm = (): boolean => {
  //   const visibleFields = getVisibleFields();
  //   const errors: Record<string, string> = {};
  //   let hasErrors = false;

  //   visibleFields.forEach(field => {
  //     const fieldId = field._id;
  //     const value = formData[fieldId];
  //     const errorMessage = validateField(field, value);

  //     if (errorMessage) {
  //       errors[fieldId] = errorMessage;
  //       hasErrors = true;
  //     }
  //   });

  //   setValidationErrors(errors);
  //   setFormHasErrors(hasErrors);
  //   return !hasErrors;
  // };

  // Add a function to check if all checkboxes are checked
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const checkAllCheckboxesSelected = (data: Record<string, any>) => {
    // Get all form fields that are checkboxes
    const checkboxFields: string[] = [];

    if (selectedTask && selectedTask.formFields) {
      selectedTask.formFields.forEach(section => {
        if (section.fields) {
          section.fields.forEach(field => {
            if (field.type === 'checkbox') {
              checkboxFields.push(field._id);
            }
          });
        }
      });
    }

    // If there are no checkboxes, return true
    if (checkboxFields.length === 0) return true;

    // Check if all checkboxes are checked
    return checkboxFields.every(id => data[id] === true);
  };

  // Modify handleInputChange function to check all checkboxes
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleInputChange = (fieldId: string, value: any) => {
    // Update form data state
    console.log('value dhyey', value);

    // Check if this is a COURT_NOTICE field and update events count
    if (typeof value === 'object' && value !== null && Array.isArray(value.events)) {
      console.log('Court notice events:', value.events);
      setCourtNoticeEventsCount(value.events.length);

      // If events array is empty, disable the button
      if (value.events.length === 0) {
        setCanComplete(false);
      }
    }

    setFormData(prev => {
      const newFormData = {
        ...prev,
        [fieldId]: value,
      };

      // Reset the specific field error on change
      if (validationErrors[fieldId]) {
        setValidationErrors(prevErrors => {
          const updatedErrors = { ...prevErrors };
          delete updatedErrors[fieldId];
          return updatedErrors;
        });
      }

      // If "no" is selected, immediately enable the button
      if (value === 'no') {
        setCanComplete(true);
      }
      // If "yes" is selected, disable the button (validations will be needed)
      else if (value === 'yes') {
        setCanComplete(false);
      }

      // For checkboxes, check if all are selected
      if (typeof value === 'boolean') {
        // Update newFormData with the current checkbox value
        newFormData[fieldId] = value;

        // Check if all checkboxes are checked
        const allChecked = checkAllCheckboxesSelected(newFormData);
        setCanComplete(allChecked);
      }

      return newFormData;
    });

    // Rest of the existing code...
    // Check if this field is used in any task's condition_task
    // Only update task selection when the field has dynamic_selected_task: true
    if (selectedWorkflow && selectedTask) {
      const fieldWithDynamicTask = selectedTask.formFields.find(
        section =>
          section.dynamic_selected_task === true &&
          section.fields.some(field => field._id === fieldId)
      );

      if (fieldWithDynamicTask) {
        const updatedWorkflow = {
          ...selectedWorkflow,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          tasks: selectedWorkflow.tasks.map((task: any) => {
            // Check if this task should be selected based on the condition_task
            const isSelected =
              task.condition_task &&
              task.condition_task.id === fieldId &&
              task.condition_task.value === value;

            return {
              ...task,
              selected_task: isSelected,
            };
          }),
        };
        setSelectedWorkflow(updatedWorkflow);
      }
    }

    // Reset save states when user makes changes
    setSaveSuccess(false);
    setSaveError(null);
  };

  // Function to save a single field to the API
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const saveFieldToAPI = async (payload: any) => {
    try {
      setSaving(true);
      setSaveError(null);

      payload.workflow_id = work_flow_id;

      // if (activeWorkflowContext?.type.startsWith('child_workflow_')) {
      //   // Make the PUT API call
      //   const response = await axios.put(
      //     `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`,
      //     payload
      //   );

      //   if (response.data.statusCode !== 200) {
      //     throw new Error(response.data.message || 'Failed to save field');
      //   }

      //   //Simulate API call delay
      //   await new Promise(resolve => setTimeout(resolve, 300));

      //   //Briefly show success indicator
      //   setSaveSuccess(true);
      //   setTimeout(() => setSaveSuccess(false), 2000);
      // }

      return true;
    } catch (error) {
      console.error('API save error:', error);
      setSaveError(error instanceof Error ? error.message : 'Failed to save field');
      return false;
    } finally {
      setSaving(false);
    }
  };

  // Get the appropriate icon for a task
  // const getTaskIcon = (iconName: string) => {
  //   switch (iconName) {
  //     case 'user':
  //       return <User size={16} className="text-blue-[#3F73F6]" />;
  //     case 'alert-triangle':
  //       return <AlertTriangle size={16} className="text-amber-500" />;
  //     case 'phone':
  //       return <Phone size={16} className="text-green-500" />;
  //     case 'mail':
  //       return <Mail size={16} className="text-indigo-500" />;
  //     default:
  //       return <User size={16} className="text-blue-[#3F73F6]" />;
  //   }
  // };

  // Determine if current task is the last one or not
  const isLastTask = () => {
    if (!selectedWorkflow || !selectedTask) return false;

    // Get the visible tasks (default_task or selected_task)
    const visibleTasks = selectedWorkflow.tasks.filter(
      task => task.default_task === true || task.selected_task === true
    );

    // Check if the current task is the last one in the visible tasks
    const currentIndex = visibleTasks.findIndex(task => task.id === selectedTask.id);
    return currentIndex === visibleTasks.length - 1;
  };

  // Handler for "Skip" or "Complete" or "Next" buttons
  const handleTaskAction = async (action: 'skip' | 'complete' | 'next') => {
    if (!selectedWorkflow || !selectedTask) return;

    // Prevent multiple API calls
    if (saving) return;
    setSaving(true);

    // Store the current workflow context before we make any changes
    const currentWorkflowContext = activeWorkflowContext;
    console.log(`Current workflow context before action: ${currentWorkflowContext?.type}`);

    // Always treat 'next' as 'complete' since we're renaming the button
    let nextTaskId = null;
    if (action === 'next') {
      // Call API to get next task info if needed
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/next-task?task_id=${selectedTask.id}&work_flow_id=${getWorkflowIdFromUrl() || '68076174e1af8cdccbe00bfc'}`
      );

      // Extract next_task_id from the response if available
      if (response?.data?.data?.next_task_id) {
        nextTaskId = response.data.data.next_task_id;
      }
    }

    // For Complete action, validate form fields first
    if (action === 'complete' || action === 'next') {
      // const isValid = validateForm();
      // if (!isValid) {
      //   // If validation fails, don't proceed
      //   setSaving(false);
      //   return;
      // }
      // Check if there's a "Confirmed" checkbox field that needs to be checked
      // const visibleFields = getVisibleFields();
      // const confirmedField = visibleFields.find(
      //   field =>
      //     field.type === 'checkbox' &&
      //     (field.label?.toLowerCase().includes('confirm') ||
      //       field.id.toLowerCase().includes('confirm'))
      // );
      // if (
      //   confirmedField &&
      //   (!formData[confirmedField._id] || formData[confirmedField._id] !== true)
      // ) {
      //   setValidationErrors(prev => ({
      //     ...prev,
      //     [confirmedField._id]: 'You must confirm before proceeding',
      //   }));
      //   setSaving(false);
      //   return; // Don't proceed if the confirmation checkbox isn't checked
      // }
    }

    // Get available tasks (those that are either default or selected)
    const availableTasks = selectedWorkflow.tasks.filter(
      t => t.default_task === true || t.selected_task === true
    );

    // Find the index of the current task in available tasks
    const currentTaskIndex = availableTasks.findIndex(t => t.id === selectedTask.id);
    const isLastTaskInWorkflow = currentTaskIndex === availableTasks.length - 1;

    try {
      // Handle task completion logic
      if (action === 'complete' || action === 'next') {
        // Mark task as completed in your data/API
        updateTaskStatus(selectedTask.id, 'completed');

        // Add the completed task to the reviewTask array
        const updatedReviewTasks = [...reviewTask];
        if (!updatedReviewTasks.includes(selectedTask.id)) {
          updatedReviewTasks.push(selectedTask.id);
          setReviewTask(updatedReviewTasks);

          // Store the updated review tasks in localStorage for persistence
          try {
            // localStorage.setItem('reviewTasks', JSON.stringify(updatedReviewTasks));
          } catch (e) {
            console.error('Failed to save review tasks to localStorage:', e);
          }
        }

        // Check if we're in a child workflow to determine the API payload
        const isChild = currentWorkflowContext?.type.startsWith('child_workflow_');

        if (action === 'complete') {
          // Update workflow status via API with isChild flag if needed
          await axios.put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update-status`, {
            workflow_id: selectedWorkflow?.tasks[0]?.work_flow_id,
            isChild: isChild, // Add isChild flag for API
          });
        }

        // After updating status, re-fetch the workflow data to get fresh child workflows
        const updatedResponse = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-render`,
          {
            params: {
              work_flow_id: work_flow_id,
            },
          }
        );

        // Update workflow data with the refreshed data
        const allWorkflowData = updatedResponse?.data?.data as unknown as WorkflowData;
        setWorkflowData(allWorkflowData);

        // Extract child workflows
        const childWorkflowsObj: { [key: string]: Workflow[] } = {};
        Object.keys(allWorkflowData).forEach(key => {
          if (key.startsWith('child_workflow_') && allWorkflowData[key]) {
            childWorkflowsObj[key] = allWorkflowData[key] as Workflow[];
          }
        });
        setChildWorkflows(childWorkflowsObj);

        // Check if we're in a child workflow
        if (currentWorkflowContext?.type.startsWith('child_workflow_')) {
          const currentChildType = currentWorkflowContext.type;
          const currentChildNumberMatch = currentChildType.match(/child_workflow_(\d+)/);

          if (currentChildNumberMatch && currentChildNumberMatch[1]) {
            const currentChildNumber = parseInt(currentChildNumberMatch[1], 10);
            const nextChildNumber = currentChildNumber + 1;
            const nextChildType = `child_workflow_${nextChildNumber}`;

            // Check if the next child workflow exists
            if (childWorkflowsObj[nextChildType] && childWorkflowsObj[nextChildType].length > 0) {
              const currentIndex = currentWorkflowContext.index;

              // Get the next child workflow directly from the workflows object
              const nextChildWorkflows = childWorkflowsObj[nextChildType];
              if (nextChildWorkflows.length > currentIndex) {
                const nextChildWorkflow = nextChildWorkflows[currentIndex];

                // Create updated workflow with activated first task
                const updatedNextWorkflow = {
                  ...nextChildWorkflow,
                  tasks: nextChildWorkflow.tasks.map((task, index) =>
                    index === 0 ? { ...task, status: 'active' } : task
                  ),
                };

                console.log(
                  `Moving to next child workflow (${nextChildType}):`,
                  updatedNextWorkflow
                );
                console.log(`Setting activeWorkflowContext to ${nextChildType}`);

                // Update context and workflow state first
                setActiveWorkflowContext({
                  workflow: updatedNextWorkflow,
                  type: nextChildType,
                  index: currentIndex,
                });
                setSelectedWorkflow(updatedNextWorkflow);

                // Then select the first task
                const firstTask = updatedNextWorkflow.tasks[0];
                selectTask(firstTask);

                // Update URL immediately
                router.push(
                  {
                    pathname: router.pathname,
                    query: { ...router.query, taskId: firstTask.id },
                  },
                  undefined,
                  { shallow: true }
                );

                setSaving(false);
                return;
              }
            } else {
              console.log(`No next child workflow (${nextChildType}) found`);
              router.push(`/all-work-flow?type=court-notice-follow-up`);
            }
          }
        }

        // If we're not in a child workflow or there's no next child workflow,
        // check if we have a next_task_id from the API
        if (nextTaskId) {
          console.log('Using next_task_id from API:', nextTaskId);

          // First, look for this task in the main workflow
          let nextTask = null;
          if (allWorkflowData.workflows && allWorkflowData.workflows.length > 0) {
            nextTask = allWorkflowData.workflows[0].tasks.find(task => task.id === nextTaskId);

            if (nextTask) {
              console.log('Found next task in main workflow:', nextTask);
              console.log(
                `Setting activeWorkflowContext to main workflow for task ${nextTask.name}`
              );

              // Set main workflow as active
              setActiveWorkflowContext({
                workflow: allWorkflowData.workflows[0],
                type: 'main',
                index: 0,
              });

              // Update selected workflow
              setSelectedWorkflow(allWorkflowData.workflows[0]);

              // Select the task and mark it as active
              updateTaskStatus(nextTask.id, 'active');
              selectTask(nextTask);

              // Update URL to reflect the next task
              router.push(
                {
                  pathname: router.pathname,
                  query: { ...router.query, taskId: nextTask.id },
                },
                undefined,
                { shallow: true }
              );

              // Stop further processing as we've selected the next task from API
              setSaving(false);
              return;
            }
          }

          // If not found in main workflow, check in all child workflows
          if (!nextTask) {
            for (const key in childWorkflowsObj) {
              const childWorkflows = childWorkflowsObj[key];
              for (let i = 0; i < childWorkflows.length; i++) {
                const childWorkflow = childWorkflows[i];
                nextTask = childWorkflow.tasks.find(task => task.id === nextTaskId);

                if (nextTask) {
                  console.log(`Found next task in ${key}:`, nextTask);
                  console.log(`Setting activeWorkflowContext to ${key} for task ${nextTask.name}`);

                  // Set the child workflow as active
                  setActiveWorkflowContext({
                    workflow: childWorkflow,
                    type: key,
                    index: i,
                  });

                  // Update selected workflow
                  setSelectedWorkflow(childWorkflow);

                  // Select the task and mark it as active
                  updateTaskStatus(nextTask.id, 'active');
                  selectTask(nextTask);

                  // Update URL to reflect the child workflow task
                  router.push(
                    {
                      pathname: router.pathname,
                      query: { ...router.query, taskId: nextTask.id },
                    },
                    undefined,
                    { shallow: true }
                  );

                  // Stop further processing as we've selected the next task from API
                  setSaving(false);
                  return;
                }
              }
            }
          }
        }

        // If we don't have a next_task_id or couldn't find it, try the next task in sequence
        if (currentTaskIndex >= 0 && currentTaskIndex < availableTasks.length - 1) {
          // If there's a next task in the current workflow, use that
          const nextTask = availableTasks[currentTaskIndex + 1];
          console.log('Moving to next task in current workflow:', nextTask.name);
          console.log(`Maintaining workflow context: ${currentWorkflowContext?.type}`);

          // Keep the current workflow context
          // No need to update activeWorkflowContext - we're in the same workflow

          // Update the next task to active status before selecting it
          updateTaskStatus(nextTask.id, 'active');
          selectTask(nextTask);
          setSaving(false);
          return;
        }

        // If we get here, check for child workflows only if we've completed all tasks in the main workflow
        if (isLastTaskInWorkflow && currentWorkflowContext?.type === 'main') {
          console.log('Last task completed in main workflow, checking for child workflows');

          // Find the first child workflow (should be child_workflow_1)
          const childWorkflowKeys = Object.keys(childWorkflowsObj).sort();
          if (childWorkflowKeys.length > 0) {
            const firstChildKey = childWorkflowKeys[0];
            const firstChildWorkflows = childWorkflowsObj[firstChildKey];

            if (firstChildWorkflows && firstChildWorkflows.length > 0) {
              const firstChildWorkflow = firstChildWorkflows[0];

              if (
                firstChildWorkflow &&
                firstChildWorkflow.tasks &&
                firstChildWorkflow.tasks.length > 0
              ) {
                // Get the first task in the child workflow
                const firstTask = firstChildWorkflow.tasks[0];

                console.log(`Selecting ${firstChildKey} first task:`, firstTask);
                console.log(
                  `Setting activeWorkflowContext to ${firstChildKey} for task ${firstTask.name}`
                );

                // Set the child workflow as active
                setActiveWorkflowContext({
                  workflow: firstChildWorkflow,
                  type: firstChildKey,
                  index: 0,
                });

                // Update selected workflow
                setSelectedWorkflow(firstChildWorkflow);

                // Select the first task and mark it as active
                updateTaskStatus(firstTask.id, 'active');
                selectTask(firstTask);

                // Update URL to reflect the child workflow task
                router.push(
                  {
                    pathname: router.pathname,
                    query: { ...router.query, taskId: firstTask.id },
                  },
                  undefined,
                  { shallow: true }
                );

                setSaving(false);
                return;
              }
            }
          } else {
            console.log('Last task completed but no child workflows found');
          }
        } else if (isLastTaskInWorkflow) {
          console.log('Last task completed but not in main workflow or no more child workflows');
          // If we're in a child workflow and there's no next task, we might want to go back to main workflow
          // or show a completion message
        }
      } else if (action === 'skip') {
        // Mark task as skipped in your data/API
        console.log('Task skipped:', selectedTask.id);
        updateTaskStatus(selectedTask.id, 'skipped');

        // If there's a next task, select it and mark it as active
        if (currentTaskIndex >= 0 && currentTaskIndex < availableTasks.length - 1) {
          const nextTask = availableTasks[currentTaskIndex + 1];

          console.log(
            `Skipping to next task: ${nextTask.name}, maintaining context: ${currentWorkflowContext?.type}`
          );

          // Update the next task to active status before selecting it
          updateTaskStatus(nextTask.id, 'active');
          selectTask(nextTask);
        }
      }
    } catch (error) {
      console.error('Error in handleTaskAction:', error);
      // Handle errors appropriately
    } finally {
      setSaving(false);
    }
  };

  // Handle "Save" button action
  // const handleSave = async () => {
  //   if (!selectedWorkflow || !selectedTask) return;

  //   // Validate form before saving
  //   const isValid = validateForm();
  //   if (!isValid) {
  //     setSaveError('Please fix the validation errors before saving');
  //     return;
  //   }

  //   setSaving(true);
  //   setSaveError(null);
  //   setSaveSuccess(false);

  //   try {
  //     // Prepare forms array for API payload
  //     const forms = Object.keys(formData).map(fieldId => ({
  //       form_component_id: fieldId,
  //       value: [{ id: null, value: formData[fieldId] }]
  //     }));

  //     // Prepare the complete payload
  //     const payload = {
  //       workflow_id: selectedWorkflow.id,
  //       task_id: selectedTask.id,
  //       forms
  //     };

  //     console.log('Save payload:', payload);

  //     // Make an API call to save the form data
  //     const response = await axios.put('http://localhost:3000/workflow/workflow-update', payload);

  //     if (response.data.statusCode === 200) {
  //       setSaveSuccess(true);
  //       setTimeout(() => setSaveSuccess(false), 2000);
  //     } else {
  //       throw new Error(response.data.message || 'Failed to save form data');
  //     }

  //   } catch (err) {
  //     console.error('Error saving form data:', err);
  //     setSaveError(err instanceof Error ? err.message : 'Failed to save form data');
  //   } finally {
  //     setSaving(false);
  //   }
  // };

  // Function to get task styling based on status
  // const getTaskStyles = (taskIndex: number) => {
  //   if (!selectedWorkflow || !selectedWorkflow.tasks[taskIndex]) {
  //     return {
  //       container: 'bg-white text-gray-600',
  //       numberBg: 'text-gray-600'
  //     };
  //   }

  //   const task = selectedWorkflow.tasks[taskIndex];

  //   // Check if the task is completed
  //   const isCompleted = task.status === 'completed';

  //   // Check if the task is active
  //   const isActive = selectedTask?.id === task.id || task.status === 'active';

  //   if (isCompleted) {
  //     return {
  //       container: 'bg-[#F3F5F9] text-[#2A2E34] line-through',
  //       numberBg: ''
  //     };
  //   }

  //   if (isActive) {
  //     return {
  //       container: 'bg-white text-[#3F73F6]',
  //       numberBg: 'bg-[#3F73F6] text-white'
  //     };
  //   }

  //   return {
  //     container: 'bg-white text-gray-600',
  //     numberBg: 'text-gray-600'
  //   };
  // };

  // New function to handle creating a new task
  const handleCreateTaskForWorkflow = (workflowType: string, workflowIndex: number) => {
    console.log(`Creating a new task for ${workflowType}, index ${workflowIndex}`);

    // For demo purposes, we will just alert
    alert(
      `This would create a new task in the ${workflowType === 'main' ? 'main workflow' : getChildWorkflowTitle(workflowType)}. In a real implementation, this would open a form to define a new task.`
    );
  };

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setShowFilter(false);
      }
    }

    if (showFilter) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showFilter]);

  // Load saved reviewTask from localStorage on component mount
  useEffect(() => {
    try {
      const savedReviewTasks = localStorage.getItem('reviewTasks');
      if (savedReviewTasks) {
        const parsedReviewTasks = JSON.parse(savedReviewTasks);
        if (Array.isArray(parsedReviewTasks)) {
          setReviewTask(parsedReviewTasks);
          console.log('Loaded review tasks from localStorage:', parsedReviewTasks);
        }
      }
    } catch (e) {
      console.error('Failed to load review tasks from localStorage:', e);
    }
  }, []);

  // Add a delayed loading state to reduce flickering
  useEffect(() => {
    if (loading) {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      // Set minimum delay before showing loader to prevent flickering
      loadingTimeoutRef.current = setTimeout(() => {
        setIsDataLoading(true);
      }, 300); // Wait 300ms before showing loading indicator
    } else {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      setIsDataLoading(false);
    }

    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [loading]);

  // Show loading state
  if (isDataLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
    <div ref={containerRef} className="flex w-full h-full overflow-auto bg-[#F3F5F9]">
      {/* Left sidebar - resizable */}
      <div
        ref={leftPanelRef}
        className="border-r border-gray-200 bg-white h-full flex flex-col overflow-hidden"
        style={{ width: `${leftPanelWidth}px`, flexShrink: 0 }}
      >
        {/* Filter button */}
        <div className="p-4 relative">
          <div className={`flex items-center justify-between`}>
            <div className="relative flex items-center">
              <button
                className={`bg-white text-[#3b82f6] font-medium ${showFilter ? 'outline-2 outline-[#3f73f6]' : ' outline outline-gray-200'} px-[16px] flex items-center justify-between gap-4 py-[12px] cursor-pointer rounded-[12px] hover:bg-gray-50 transition text-sm`}
                onClick={() => setShowFilter(!showFilter)}
              >
                Filter
                <div className="flex items-center justify-center w-5 h-5 bg-[#3b82f6] text-white text-xs font-medium rounded-full">
                  2
                </div>
              </button>
            </div>

            <div className="flex items-center">
              <span className="mr-2 text-gray-600 text-sm">All workflows</span>
              <div
                className={`w-10 h-5 ${showAllWorkflows ? 'bg-[#3F73F6]' : 'bg-gray-400'} rounded-full p-0.5 flex items-center cursor-pointer transition-colors duration-300`}
                onClick={handleToggleChange}
              >
                <div
                  className="bg-white w-4 h-4 rounded-full shadow-md transition-transform duration-300"
                  style={{ transform: showAllWorkflows ? 'translateX(20px)' : 'translateX(0)' }}
                ></div>
              </div>
            </div>
          </div>

          {showFilter && (
            <div
              ref={filterRef}
              className="absolute top-16 w-[344px] font-normal left-4 z-10 bg-white border border-gray-200 rounded-lg shadow-lg px-3 gap-[16px] py-[18px]"
            >
              <div className="space-y-3">
                <label className="flex items-center cursor-pointer">
                  <CustomCheckbox
                    id="myTasks"
                    checked={filters.myTasks}
                    onChange={() => handleFilterChange('myTasks')}
                  />
                  <span className="ml-3 text-sm text-[#2a2e34]">My Tasks</span>
                </label>

                <label className="flex items-center cursor-pointer">
                  <CustomCheckbox
                    id="unassigned"
                    checked={filters.unassigned}
                    onChange={() => handleFilterChange('unassigned')}
                  />
                  <span className="ml-3 text-sm text-[#2a2e34]">Unassigned</span>
                </label>

                <label className="flex items-center cursor-pointer">
                  <CustomCheckbox
                    id="allTasks"
                    checked={filters.allTasks}
                    onChange={() => handleFilterChange('allTasks')}
                  />
                  <span className="ml-3 text-sm text-[#2a2e34]">All Tasks</span>
                </label>
              </div>

              <div className="mt-4 flex justify-end">
                <button
                  className="bg-[#3F73F6] text-white px-[16px] py-[12px] rounded-[12px] cursor-pointer text-sm font-medium hover:bg-blue-600 transition"
                  onClick={applyFilters}
                >
                  Apply
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Client information with expand/collapse */}
        {/* <div className="px-4 py-3 border-gray-200">
          <div
            className="flex items-center justify-between cursor-pointer"
            onClick={() => setExpanded(!expanded)}
          >
            <div>
              <h2 className="font-normal text-gray-800">Jeffery Price & Amanda Burke</h2>
              <p className="text-sm font-normal text-[#2A2E34]">New Court notice</p>
            </div>
            {expanded ? (
              <ChevronUp size={18} className="text-[#2A2E34]" />
            ) : (
              <ChevronDown size={18} className="text-[#2A2E34]" />
            )}
          </div>
        </div> */}

        {expanded && (
          <div className="p-4 flex flex-col space-y-3 overflow-y-auto flex-grow">
            {/* Render all workflows and their tasks */}
            {workflowData && (
              <>
                {/* Main workflow section */}
                {workflowData.workflows &&
                  workflowData.workflows.length > 0 &&
                  (() => {
                    // First check if there are any visible tasks after filtering
                    const visibleTasks = workflowData.workflows[0].tasks
                      .filter(task => task.task_visible_status !== 'DRAFT') // Always remove DRAFT tasks
                      .filter(_ => {
                        if (!showAllWorkflows && initialActiveContext) {
                          return initialActiveContext.type === 'main';
                        }
                        return true; // Keep all non-DRAFT tasks if condition doesn't apply
                      });

                    // Only render the section if there are visible tasks
                    return visibleTasks.length > 0 ? (
                      <div className="mb-4">
                        <div
                          className="flex items-center justify-between mb-2 cursor-pointer hover:bg-gray-50 p-2 rounded"
                          onClick={() => toggleSectionExpansion('main')}
                        >
                          <div className="flex flex-col">
                            <h3 className="font-normal leading-[24px] text-[16px] text-[#2A2E34]">
                              {workflowData.workflows[0].name || 'Jeffery Price, SA'}
                            </h3>
                            <p className="text-[12px] font-normal text-[#2A2E34]">
                              New Court notice
                            </p>
                          </div>

                          {expandedSections['main'] ? (
                            <ChevronUp size={16} className="text-gray-500" />
                          ) : (
                            <ChevronDown size={16} className="text-gray-500" />
                          )}
                        </div>

                        {expandedSections['main'] && (
                          <>
                            {visibleTasks.map((task, idx) => {
                              console.log('🚀 ~ {visibleTasks.map ~ task:', task);
                              // Check if this task is in the reviewTask array or completed
                              const isReviewed =
                                reviewTask.includes(task.id) || task.status === 'completed';
                              const isCurrentTask =
                                selectedTask?.id === task.id &&
                                activeWorkflowContext?.type === 'main';

                              return (
                                <div key={task.id} className="relative flex items-center mb-2">
                                  <div
                                    className={`absolute left-0 w-6 h-6 flex items-center justify-center ${isCurrentTask ? 'bg-[#3F73F6] text-white' : isReviewed ? ' text-gray-600' : ' text-gray-600'} rounded-full`}
                                  >
                                    <span className="text-xs">{idx + 1}</span>
                                  </div>
                                  <div className="flex-1 ml-10">
                                    <div
                                      className={`flex items-center justify-between p-3 rounded-[12px] border ${task?.task_visible_status !== 'REVIEWED' ? 'border-[#3F73F6]' : 'border-[#C7D1DF]'} bg-white cursor-pointer`}
                                      onClick={() => handleTaskClick('main', 0, idx)}
                                    >
                                      <div className="flex items-center">
                                        <Image
                                          src={
                                            task?.task_visible_status !== 'REVIEWED'
                                              ? '/assets/squares.svg'
                                              : '/assets/check-square-gray.svg'
                                          }
                                          alt="Task status"
                                          className="mr-2"
                                          width={24}
                                          height={24}
                                        />
                                        <span
                                          className={`font-normal ${
                                            task?.task_visible_status !== 'REVIEWED'
                                              ? 'text-[#3F73F6]'
                                              : 'text-[#A2AFC2] line-through'
                                          }`}
                                        >
                                          {task?.name}
                                        </span>
                                      </div>
                                      <div className="relative bg-[#3f73f65e] w-7 h-7 rounded-full flex items-center justify-center">
                                        <Image
                                          src="/assets/ai-robot-new-2.svg"
                                          alt="AI assistant"
                                          width={25}
                                          height={25}
                                        />
                                        <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                                          3
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                          </>
                        )}

                        <div className="flex-1 pl-10">
                          {expandedSections['main'] && (
                            <div className="flex items-center justify-between border-b border-[#DCE2EB] my-2"></div>
                          )}
                          <button
                            className="w-full flex items-center cursor-pointer border border-[#A2AFC2] rounded-[12px] p-3 hover:bg-[#f3f5f9b9] transition"
                            onClick={() => handleCreateTaskForWorkflow('main', 0)}
                          >
                            <Plus size={16} className="mr-2 text-[#5F6F84]" />
                            <span className="text-[#A2AFC2]">Create a Task</span>
                          </button>
                        </div>
                      </div>
                    ) : null;
                  })()}

                {/* Child workflows - each in its own independently collapsible section */}
                {Object.entries(childWorkflows).map(([key, workflows]) =>
                  workflows.map((childWorkflow, childIndex) => {
                    // If showing all is false, only show child workflow if it's part of the initial context
                    if (
                      !showAllWorkflows &&
                      initialActiveContext &&
                      initialActiveContext.type !== key
                    ) {
                      return null;
                    }

                    // Initialize expandedSections for this child workflow if it doesn't exist
                    const sectionKey = `${key}-${childIndex}`;
                    if (expandedSections[sectionKey] === undefined) {
                      // Automatically update expandedSections state with a default value
                      setExpandedSections(prev => ({
                        ...prev,
                        [sectionKey]: true, // Default to expanded
                      }));
                    }

                    return (
                      <div key={sectionKey} className="mb-4">
                        <div className="px-4 py-3 border-gray-200">
                          <div
                            className="flex items-center justify-between cursor-pointer"
                            onClick={() => toggleSectionExpansion(sectionKey)}
                          >
                            <div>
                              <h2 className="font-normal text-gray-800">
                                {childWorkflow?.tasks[0]?.name}
                              </h2>
                              <p className="text-sm font-normal text-[#2A2E34]">
                                {' '}
                                {getChildWorkflowTitle(key)}
                              </p>
                            </div>
                            {expandedSections[sectionKey] ? (
                              <ChevronUp size={18} className="text-[#2A2E34]" />
                            ) : (
                              <ChevronDown size={18} className="text-[#2A2E34]" />
                            )}
                          </div>
                        </div>

                        {expandedSections[sectionKey] && (
                          <>
                            {childWorkflow.tasks.map((task, taskIndex) => {
                              const isCurrentChildTask =
                                activeWorkflowContext?.type === key &&
                                activeWorkflowContext?.index === childIndex &&
                                selectedTask?.id === task.id;
                              const isReviewed =
                                reviewTask.includes(task.id) || task.status === 'completed';

                              return (
                                <div key={task.id} className="relative flex items-center mb-2">
                                  <div
                                    className={`absolute left-0 w-6 h-6 flex items-center justify-center ${isCurrentChildTask ? 'bg-[#3F73F6] text-white' : isReviewed ? 'text-gray-600' : ' text-gray-600'} rounded-full`}
                                  >
                                    <span className="text-xs">{taskIndex + 1}</span>
                                  </div>
                                  <div className="flex-1 ml-10">
                                    <div
                                      className={`flex items-center justify-between p-3 rounded-[12px] border ${task?.task_visible_status !== 'REVIEWED' ? 'border-[#3F73F6]' : 'border-[#C7D1DF]'} bg-white cursor-pointer`}
                                      onClick={() => handleTaskClick(key, childIndex, taskIndex)}
                                    >
                                      <div className="flex items-center">
                                        <Image
                                          src={
                                            task?.task_visible_status !== 'REVIEWED'
                                              ? '/assets/squares.svg'
                                              : '/assets/check-square-gray.svg'
                                          }
                                          alt="Task status"
                                          className="mr-2"
                                          width={24}
                                          height={24}
                                        />
                                        <span
                                          className={`font-normal ${
                                            task?.task_visible_status !== 'REVIEWED'
                                              ? 'text-[#3F73F6]'
                                              : 'text-[#A2AFC2] line-through'
                                          }`}
                                        >
                                          {'Notify all parties'}
                                        </span>
                                      </div>
                                      <div className="relative bg-[#3f73f65e] w-7 h-7 rounded-full flex items-center justify-center">
                                        <Image
                                          src="/assets/ai-robot-new-2.svg"
                                          alt="AI assistant"
                                          width={25}
                                          height={25}
                                        />
                                        <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                                          3
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                          </>
                        )}

                        <div className="flex-1 pl-10">
                          {expandedSections[sectionKey] && (
                            <div className="flex items-center justify-between border-b border-[#DCE2EB] my-2"></div>
                          )}
                          <button
                            className="w-full flex items-center cursor-pointer border border-[#A2AFC2] rounded-[12px] p-3 hover:bg-[#f3f5f9b9] transition"
                            onClick={() => handleCreateTaskForWorkflow('main', 0)}
                          >
                            <Plus size={16} className="mr-2 text-[#5F6F84]" />
                            <span className="text-[#A2AFC2]">Create a Task</span>
                          </button>
                        </div>
                      </div>
                    );
                  })
                )}
              </>
            )}
          </div>
        )}
      </div>

      {/* Left resize handle */}
      <ResizeHandle onMouseDown={handleLeftResizeStart} />

      {/* Main content area - contains center panel and right panel */}
      <div className="flex-1 flex">
        {/* Center panel - resizable */}
        <div
          ref={centerPanelRef}
          className="flex flex-col bg-white border-r border-gray-200"
          style={{ width: `${centerPanelWidth}%`, flexShrink: 0 }}
        >
          <div
            className="flex-1 p-2 overflow-y-auto 
            [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] 
            [&::-webkit-scrollbar]:w-2 
            [&::-webkit-scrollbar-thumb]:rounded-full 
            [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] 
            [&::-webkit-scrollbar-track]:bg-gray-100"
          >
            <div className="p-4">
              <div className="p-4 border-b border-gray-200 flex justify-between items-center bg-white">
                <div className="flex items-center">
                  <span className="text-[24px] leading-[36px] font-medium text-[#2A2E34]">
                    {selectedTask?.name || selectedWorkflow?.name || ''}
                  </span>
                </div>
                <div className="flex items-center">
                  {/* <span className="text-sm text-[#5F6F84] mr-2">{new Date().toLocaleDateString()}</span> */}
                  <button className="p-[10px] w-[40px] cursor-pointer h-[40px] text-[#5F6F84] border border-[#DCE2EB] rounded-[12px] transition ml-2">
                    <svg
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="#5F6F84"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <circle cx="12" cy="12" r="1" />
                      <circle cx="19" cy="12" r="1" />
                      <circle cx="5" cy="12" r="1" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Form validation error summary */}
              {formHasErrors && Object.keys(validationErrors).length > 0 && (
                <div className="mb-6 p-3 bg-red-50 border border-red-200 rounded-[12px]">
                  <div className="flex items-center text-red-800 mb-2">
                    <AlertTriangle size={16} className="mr-2" />
                    <span className="font-medium">Please fix the following errors:</span>
                  </div>
                  <ul className="text-sm text-red-600 ml-6 list-disc">
                    {Object.values(validationErrors).map((error, i) => (
                      <li key={i}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Render the workflow form if task is selected */}
              {selectedTask && (
                <div className="mb-6">
                  {/* Skip the client header for court notice workflows */}
                  {/* {selectedTask.name !== "Notify all parties" && ( */}
                  <div className="flex justify-between items-center mb-4 bg-white p-4 rounded-[12px]">
                    <div className="flex items-center">
                      <User className="text-[#5F6F84] mr-2" size={22} />
                      <div className="relative bg-[#3f73f65e] w-7 h-7 rounded-full flex items-center justify-center">
                        <Image
                          src="/assets/ai-robot-new-2.svg"
                          alt="AI assistant"
                          width={25}
                          height={25}
                        />
                        <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                          3
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center">
                      {/* <Calendar className="text-[#5F6F84] mr-2" size={18} /> */}
                      <Image
                        src="/assets/calendar1.svg"
                        alt="AI assistant"
                        className="text-[#5F6F84] mr-2"
                        width={18}
                        height={18}
                      />
                      <span
                        className="text-sm text-gray-600 cursor-pointer hover:text-blue-500 transition-colors"
                        onClick={() => setShowDateModal(true)}
                        title="Click to change date and time"
                      >
                        {selectedWorkflow?.end_date || 'Set date and time'}
                      </span>
                    </div>
                  </div>
                  {/* )} */}

                  {/* Form fields - Using the reusable WorkflowField component */}
                  {formFields.map((section: FormSection, idx: number) => {
                    // Determine if this section should be disabled based on workflow context and section id
                    const isChildWorkflowSection =
                      activeWorkflowContext?.type.startsWith('child_workflow_');
                    const isSensitiveSection =
                      section.id === 'assign-section-01' || section.id === 'court-notice-section';

                    // Check if the task is reviewed or in a special state
                    const isTaskReviewed = selectedTask?.task_visible_status === 'REVIEWED';

                    // Determine if section should be disabled
                    // If task is reviewed, all sections are disabled
                    // Otherwise, only disable sensitive sections in child workflows
                    const shouldDisableSection =
                      isTaskReviewed || (isChildWorkflowSection && isSensitiveSection);

                    console.log(
                      `Section ${section.id || section._id}: isChildWorkflow=${isChildWorkflowSection}, isSensitive=${isSensitiveSection}, isDisabled=${shouldDisableSection}`
                    );

                    return (
                      <WorkflowField
                        key={`section-${section.id || section._id || idx}`}
                        section={section}
                        onInputChange={handleInputChange}
                        onBlur={(fieldId, value) =>
                          saveFieldToAPI({
                            workflow_id: selectedWorkflow?.tasks[0]?.work_flow_id,
                            task_id: selectedTask?.id,
                            forms: [
                              {
                                form_component_id: fieldId,
                                value: [{ id: null, value: value }],
                              },
                            ],
                          })
                        }
                        task_id={selectedTask?.id}
                        formData={formData}
                        work_flow_id={selectedWorkflow?.tasks[0]?.work_flow_id || ''}
                        isDisabled={shouldDisableSection}
                        isChildWorkflow={isChildWorkflowSection}
                        apiOptions={apiOptions} // Pass the API options to WorkflowField
                      />
                    );
                  })}

                  {/* Save status messages */}
                  {saveSuccess && (
                    <div className="mt-4 p-3 bg-green-50 text-green-700 rounded-[12px] flex items-center">
                      <CheckCircle size={16} className="mr-2" />
                      Data saved successfully!
                    </div>
                  )}

                  {/* {saveError && (
                    <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-[12px] flex items-center">
                      <AlertTriangle size={16} className="mr-2" />
                      {saveError}
                    </div>
                  )} */}
                </div>
              )}

              <div className="flex justify-between items-center gap-4 pt-4 mt-6 border-t border-gray-200">
                <button
                  className="px-4 cursor-not-allowed py-2 border w-full font-medium border-[#DCE2EB] rounded-[12px] text-[#A2AFC2]"
                  disabled
                >
                  Skip
                </button>
                {isLastTask() ? (
                  <button
                    className="px-4 py-2 cursor-pointer bg-[#3F73F6] w-full text-white rounded-[12px] font-medium hover:bg-blue-700 transition disabled:text-[#A2AFC2] disabled:bg-[#EFF2F7] disabled:cursor-not-allowed"
                    onClick={() => handleTaskAction('complete')}
                    disabled={selectedTask?.task_visible_status === 'REVIEWED' || !canComplete}
                  >
                    Complete
                  </button>
                ) : (
                  <button
                    className="px-4 py-2 cursor-pointer bg-[#3F73F6] w-full text-white rounded-[12px] font-medium hover:bg-blue-700 transition disabled:text-[#A2AFC2] disabled:bg-[#EFF2F7] disabled:cursor-not-allowed"
                    onClick={() => handleTaskAction('next')}
                    disabled={selectedTask?.task_visible_status === 'REVIEWED' || !canComplete}
                  >
                    Complete
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Center resize handle */}
        <ResizeHandle onMouseDown={handleCenterResizeStart} />

        {/* Right panel - sidebar */}
        <AdvisorPanel
          emailInfo={emailInfo || undefined}
          noticeSummary={noticeSummary || undefined}
          panelRef={rightPanelRef}
          style={{ width: `${100 - centerPanelWidth}%` }}
        />

        {/* <div className="flex flex-col bg-white gap-6 px-3 py-4 items-center border border-l border-[#DCE2EB]">
          {sidebarIcons.map((item, index) => (
            <button
              key={index}
              className={` rounded-[12px] ${item.active ? 'bg-[#3F73F6]' : ''}`}
              onClick={() => setActiveIcon(index)}
            >
              {item.icon}
            </button>
          ))}
        </div> */}
        <div className="flex flex-col cursor-pointer bg-white gap-2 items-center border-l py-2 border-[#DCE2EB] w-[64px]">
          {sidebarIcons.map((item, index) => (
            <div
              key={index}
              className="w-full h-[48px] border-b border-[#DCE2EB] flex items-center justify-center p-[7px]"
            >
              <button
                className={`flex items-center  cursor-pointer justify-center w-full h-full ${item.active ? 'bg-[#3F73F6] w-[40px] h-[40px] rounded-[12px]' : ''}`}
                // onClick={() => setActiveIcon(index)}
              >
                <span className="text-xl">{item.icon}</span>
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Date Modal */}
      <DateTimeModal
        isOpen={showDateModal}
        onClose={() => setShowDateModal(false)}
        initialDate={selectedWorkflow?.end_date?.split(' ')[0] || '04/12/25'}
        initialTime={selectedWorkflow?.end_date?.split(' ')[1] || '10:00 AM'}
        workflowId={getWorkflowIdFromUrl()}
        taskId={selectedTask?.id}
        onSave={(date, time) => {
          if (selectedWorkflow) {
            // Create a deep copy of the workflow to update its end_date
            const updatedWorkflow = {
              ...selectedWorkflow,
              end_date: `${date} ${time}`,
            };
            setSelectedWorkflow(updatedWorkflow);
          }
        }}
      />
    </div>
  );
}

// Update only the handleToggleChange function (around line 1860)
const handleToggleChange = () => {
  const newToggleState = !showAllWorkflows;
  setShowAllWorkflows(newToggleState);
  
  // Store toggle state in local storage to persist between task changes
  try {
    localStorage.setItem('workflowToggleState', newToggleState ? 'true' : 'false');
  } catch (e) {
    console.error('Failed to save toggle state:', e);
  }

  if (!newToggleState) { // Toggle OFF - show only initial workflow
    console.log('Toggle OFF - showing only initial workflow');
    if (initialActiveContext && initialTaskIdState) {
      // Restore initial context and task
      setActiveWorkflowContext(initialActiveContext);
      setSelectedWorkflow(initialActiveContext.workflow);

      // Find the task from initial taskId
      const task = initialActiveContext.workflow.tasks.find(t => t.id === initialTaskIdState);
      if (task) {
        selectTask(task);
      }
    }
  } else { // Toggle ON - show all workflows
    console.log('Toggle ON - showing all workflows');
    fetchWorkflowData();
  }
};

// Add this useEffect after the other useEffects (around line 500-700)
// Load saved toggle state from localStorage on component mount
useEffect(() => {
  try {
    const savedToggleState = localStorage.getItem('workflowToggleState');
    if (savedToggleState === 'true') {
      setShowAllWorkflows(true);
    }
  } catch (e) {
    console.error('Failed to load toggle state from localStorage:', e);
  }
}, []);
