/* eslint-disable @typescript-eslint/no-explicit-any */
import { useRouter } from 'next/router';
import { User, Plus, AlertTriangle, CheckCircle, ChevronUp, ChevronDown, X } from 'lucide-react';
import WorkflowField from './WorkflowField';
import Image from 'next/image';
import { AdvisorPanel } from './index';
import DetailsPanel from './sidebar/DetailsPanel';
import { useCallback, useEffect, useRef, useState } from 'react';
import CustomCheckbox from '../common/CustomCheckbox';
import DateTimeModal from '../common/DateTimeModal';
import apiClient from '@/services/api/config';

// Timezone utility functions (moved inside component)
const getUserTimezone = (): string => {
  if (typeof window === 'undefined') return 'UTC';
  return (
    localStorage.getItem('timezone') || Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC'
  );
};

const convertDateToUserTimezone = (date: Date | string, timezone?: string): Date => {
  const userTimezone = timezone || getUserTimezone();
  const inputDate = typeof date === 'string' ? new Date(date) : date;

  // Convert to user's timezone
  const timezonedDate = new Date(inputDate.toLocaleString('en-US', { timeZone: userTimezone }));
  return timezonedDate;
};

const formatDateTimeInUserTimezone = (dateString: string, timezone?: string): string => {
  try {
    if (!dateString || typeof dateString !== 'string') {
      return 'Set date and time';
    }

    const userTimezone = timezone || getUserTimezone();
    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn('Invalid date for timezone formatting:', dateString);
      return 'Set date and time';
    }

    // Convert to user's timezone and format
    const options: Intl.DateTimeFormatOptions = {
      timeZone: userTimezone,
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    };

    const formatter = new Intl.DateTimeFormat('en-US', options);
    return formatter.format(date);
  } catch (e) {
    console.error('Error formatting date with timezone:', e, dateString);
    return 'Set date and time';
  }
};

const parseAndFormatTaskDate = (endDate: string | undefined): string => {
  if (!endDate) {
    return 'Set date and time';
  }

  try {
    // Check if it's a simple local format (from onSave callback) - just return as-is
    if (endDate.match(/^\d{2}\/\d{2}\/\d{4} \d{1,2}:\d{2} [AP]M$/)) {
      return endDate; // Return exactly what user selected
    }

    // Check if it's UTC time from backend (format: "09/09/2025 09:31 AM UTC")
    if (endDate.includes(' UTC')) {
      // Remove " UTC" suffix and parse as UTC time
      const utcTimeString = endDate.replace(' UTC', '');
      const parts = utcTimeString.split(' ');

      if (parts.length >= 2) {
        const datePart = parts[0]; // "09/09/2025"
        const timePart = parts[1]; // "09:31"
        const period = parts[2] || 'AM'; // "AM" or "PM"

        // Parse the date part
        const [month, day, year] = datePart.split('/');
        const fullYear = year; // Already 4-digit year from backend

        // Parse the time part
        const [hours, minutes] = timePart.split(':');
        let hour24 = parseInt(hours);

        // Convert to 24-hour format
        if (period.toUpperCase() === 'PM' && hour24 !== 12) {
          hour24 += 12;
        } else if (period.toUpperCase() === 'AM' && hour24 === 12) {
          hour24 = 0;
        }

        // Create UTC date object
        const utcDate = new Date(Date.UTC(
          parseInt(fullYear),
          parseInt(month) - 1,
          parseInt(day),
          hour24,
          parseInt(minutes)
        ));

        // Convert to user's timezone
        return formatDateTimeInUserTimezone(utcDate.toISOString());
      }
    }

    // Handle other date formats (fallback)
    let dateToFormat: Date;

    // Check if it's already in a format like "04/12/25 10:00 AM"
    if (endDate.includes('/') && endDate.includes(':')) {
      // Split the date and time parts
      const parts = endDate.split(' ');
      if (parts.length >= 2) {
        const datePart = parts[0]; // "04/12/25"
        const timePart = parts[1]; // "10:00"
        const period = parts[2] || 'AM'; // "AM" or "PM"

        // Parse the date part
        const [month, day, year] = datePart.split('/');
        const fullYear = year; // Already 4-digit year from backend

        // Parse the time part
        const [hours, minutes] = timePart.split(':');
        let hour24 = parseInt(hours);

        // Convert to 24-hour format
        if (period.toUpperCase() === 'PM' && hour24 !== 12) {
          hour24 += 12;
        } else if (period.toUpperCase() === 'AM' && hour24 === 12) {
          hour24 = 0;
        }

        // Create date object
        dateToFormat = new Date(
          parseInt(fullYear),
          parseInt(month) - 1,
          parseInt(day),
          hour24,
          parseInt(minutes)
        );
      } else {
        // Fallback to direct parsing
        dateToFormat = new Date(endDate);
      }
    } else {
      // Try to parse as ISO string or other standard format
      dateToFormat = new Date(endDate);
    }

    // Check if the date is valid
    if (isNaN(dateToFormat.getTime())) {
      console.warn('Invalid date for task formatting:', endDate);
      return 'Set date and time';
    }

    // Format in user's timezone
    return formatDateTimeInUserTimezone(dateToFormat.toISOString());
  } catch (e) {
    console.error('Error parsing and formatting task date:', e, endDate);
    return 'Set date and time';
  }
};

// Function to parse task date for DateTimeModal
const parseTaskDateForModal = (
  endDate: string | undefined
): { date: string; time: string; period: string } => {
  if (!endDate) {
    return { date: '04/12/2025', time: '10:00', period: 'AM' };
  }

  try {
    const userTimezone = getUserTimezone();
    let dateToConvert: Date;

    // Check if it's UTC time from backend (format: "09/09/2025 09:31 AM UTC")
    if (endDate.includes(' UTC')) {
      // Remove " UTC" suffix and parse as UTC time
      const utcTimeString = endDate.replace(' UTC', '');
      const parts = utcTimeString.split(' ');

      if (parts.length >= 2) {
        const datePart = parts[0]; // "09/09/2025"
        const timePart = parts[1]; // "09:31"
        const period = parts[2] || 'AM'; // "AM" or "PM"

        // Parse the date part
        const [month, day, year] = datePart.split('/');
        const fullYear = year; // Already 4-digit year from backend

        // Parse the time part
        const [hours, minutes] = timePart.split(':');
        let hour24 = parseInt(hours);

        // Convert to 24-hour format
        if (period.toUpperCase() === 'PM' && hour24 !== 12) {
          hour24 += 12;
        } else if (period.toUpperCase() === 'AM' && hour24 === 12) {
          hour24 = 0;
        }

        // Create UTC date object
        dateToConvert = new Date(Date.UTC(
          parseInt(fullYear),
          parseInt(month) - 1,
          parseInt(day),
          hour24,
          parseInt(minutes)
        ));
      } else {
        dateToConvert = new Date(endDate);
      }
    } else if (endDate.includes('/') && endDate.includes(':')) {
      // Handle other date formats that might come from the API
      // Split the date and time parts
      const parts = endDate.split(' ');
      if (parts.length >= 2) {
        const datePart = parts[0]; // "04/12/25"
        const timePart = parts[1]; // "10:00"
        const period = parts[2] || 'AM'; // "AM" or "PM"

        // Parse the date part
        const [month, day, year] = datePart.split('/');
        const fullYear = year; // Already 4-digit year from backend

        // Parse the time part
        const [hours, minutes] = timePart.split(':');
        let hour24 = parseInt(hours);

        // Convert to 24-hour format
        if (period.toUpperCase() === 'PM' && hour24 !== 12) {
          hour24 += 12;
        } else if (period.toUpperCase() === 'AM' && hour24 === 12) {
          hour24 = 0;
        }

        // Create date object (treat as local time if no UTC indicator)
        dateToConvert = new Date(
          parseInt(fullYear),
          parseInt(month) - 1,
          parseInt(day),
          hour24,
          parseInt(minutes)
        );
      } else {
        dateToConvert = new Date(endDate);
      }
    } else {
      dateToConvert = new Date(endDate);
    }

    // Check if the date is valid
    if (isNaN(dateToConvert.getTime())) {
      console.warn('Invalid date for modal parsing:', endDate);
      return { date: '04/12/2025', time: '10:00', period: 'AM' };
    }

    // Convert to user's timezone
    const userTimezoneDate = convertDateToUserTimezone(dateToConvert, userTimezone);

    // Format date part (MM/DD/YYYY)
    const month = String(userTimezoneDate.getMonth() + 1).padStart(2, '0');
    const day = String(userTimezoneDate.getDate()).padStart(2, '0');
    const year = String(userTimezoneDate.getFullYear());
    const formattedDate = `${month}/${day}/${year}`;
    

    // Format time part
    let hours = userTimezoneDate.getHours();
    const minutes = String(userTimezoneDate.getMinutes()).padStart(2, '0');
    const period = hours >= 12 ? 'PM' : 'AM';

    // Convert to 12-hour format
    if (hours === 0) {
      hours = 12;
    } else if (hours > 12) {
      hours -= 12;
    }

    const formattedTime = `${hours}:${minutes}`;

    return { date: formattedDate, time: formattedTime, period };
  } catch (e) {
    console.error('Error parsing task date for modal:', e, endDate);
    return { date: '04/12/2025', time: '10:00', period: 'AM' };
  }
};

type Validation = {
  minLength?: number;
  min?: number;
  max?: number;
  pattern?: string;
  errorMessage?: string;
};

interface FormField {
  type: string;
  placeholder?: string;
  id: string;
  _id: string;
  label?: string;
  value?: string | number | boolean | SelectOption[] | Record<string, unknown> | unknown[];
  options?: SelectOption[] | unknown[];
  required?: boolean;
  validation?: Validation;
}

interface SelectOption {
  value: string;
  text: string;
  profileImage?: string;
  initials?: string;
}

interface NoticeEvent {
  name: string;
  count: number;
}

interface NoticeInfo {
  events?: NoticeEvent[];
}

interface EmailInfo {
  email_from?: string;
  email_to?: string;
  email_subject?: string;
  email_body?: string;
  document?: Array<{
    name: string;
    size: string;
  }>;
  notice_summary?: NoticeInfo;
}

type Condition =
  | {
    field: string;
    value: string | boolean | number;
  }
  | {
    type: 'AND' | 'OR';
    conditions: Array<Condition>;
  };

interface FormSection {
  id?: string;
  _id?: string;
  type: string;
  label: string;
  fields: FormField[];
  condition?: Condition;
  dynamic_fields?: boolean;
  dynamic_selected_task?: boolean;
  selected_task?: boolean;
  required?: boolean;
}

interface Task {
  id: string;
  name: string;
  description: string;
  icon: string;
  status: string;
  formFields: FormSection[];
  default_task?: boolean;
  selected_task?: boolean;
  work_flow_id?: string;
  task_visible_status: string;
  end_date?: string;
  group_id?: string;
  condition_task?: {
    id: string;
    value: string | boolean | number;
  };
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
interface Workflow {
  id: string;
  name: string;
  description: string;
  tasks: Task[];
  end_date?: string;
  is_archive: boolean;
  archive_at?: string;
  archive_by: string;
  workflow_status: string;
  workflow_execution_id?: string; // Add this field for child workflows
}

interface WorkflowData {
  workflows: Workflow[];
  child_workflow_1?: Workflow[];
  child_workflow_2?: Workflow[];
  client_not_found?: boolean;
  options?: {
    courtNoticeTypes: Array<{ value: string; text: string }>;
    appointmentActions: Array<{ value: string; text: string }>;
    counties: Array<{ value: string; text: string; _id?: string; is_active?: boolean }>;
    courtLocations: Array<{ value: string; text: string; _id?: string; is_active?: boolean }>;
    attendees: Array<{ value: string; text: string; _id?: string; is_active?: boolean }>;
    clientAttendanceOptions: Array<{ value: string; text: string }>;
    meetingLocations: Array<{ value: string; text: string }>;
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: Workflow[] | undefined | any; // Added any to accommodate options
}

// New interface to support child workflow context
interface WorkflowContext {
  //  @typescript-eslint/no-explicit-any
  [x: string]: any;
  workflow: Workflow;
  type: 'main' | 'child1' | 'child2' | string;
  index: number;
}

interface WorkflowRunProps {
  initialTaskId?: string;
}

// Function to get timezone display name
const getTimezoneDisplayName = (): string => {
  try {
    const timezone = getUserTimezone();
    const now = new Date();
    const shortName =
      now
        .toLocaleString('en-US', {
          timeZone: timezone,
          timeZoneName: 'short',
        })
        .split(' ')
        .pop() || '';
    return shortName;
  } catch (e) {
    console.error('Error getting timezone display name:', e);
    return '';
  }
};

export default function ContactForm({ initialTaskId }: WorkflowRunProps) {
  const router = useRouter();
  const { work_flow_id, taskId } = router.query;

  const getWorkflowIdFromUrl = () => {
    const { work_flow_id } = router.query;
    return work_flow_id ? String(work_flow_id) : '';
  };

  // Add state declarations at the top with other state
  const [activePanel, setActivePanel] = useState<'advisor' | 'details'>('advisor');
  const [activeSidebarIcon, setActiveSidebarIcon] = useState(0);
  const [userId, setUserId] = useState<string | null>(null);
  const [hoveredIcon, setHoveredIcon] = useState<number | null>(null);
  const [isRightPanelOverlayOpen, setIsRightPanelOverlayOpen] = useState(false);
  const [dynamicLeftPanelWidth, setDynamicLeftPanelWidth] = useState(758);
  const [dynamicRightPanelWidth, setDynamicRightPanelWidth] = useState(758);

  // Main state for workflow data
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);

  // New state for all workflows
  const [workflowData, setWorkflowData] = useState<WorkflowData | null>(null);
  const [activeWorkflowContext, setActiveWorkflowContext] = useState<WorkflowContext | null>(null);
  const [childWorkflows, setChildWorkflows] = useState<{ [key: string]: Workflow[] }>({});

  // Add state for showing all workflows toggle
  const [showAllWorkflows, setShowAllWorkflows] = useState(() => {
    try {
      // Check if running in browser before using localStorage
      if (typeof window !== 'undefined') {
        const savedToggle = localStorage.getItem('workflowShowAllToggle');
        const initialValue = savedToggle === 'true';

        return initialValue;
      }

      return false;
    } catch (e) {
      console.error('❌ Failed to load toggle state from localStorage:', e);
      return false;
    }
  });

  const getUserFromLocalStorage = () => {
    try {
      if (typeof window !== 'undefined') {
        const userData = localStorage.getItem('user');
        if (userData) {
          return JSON.parse(userData);
        }
      }
      return null;
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
      return null;
    }
  };

  // Initialize user timezone on component mount
  useEffect(() => {
    try {
      if (typeof window !== 'undefined') {
        // Check if timezone is already stored
        const storedTimezone = localStorage.getItem('timezone');

        if (!storedTimezone) {
          // Detect and store user's timezone
          const detectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
          localStorage.setItem('timezone', detectedTimezone);
          console.log('🌍 Timezone detected and stored:', detectedTimezone);
        } else {
          console.log('🌍 Using stored timezone:', storedTimezone);
        }

        // Debug: Show timezone info
        console.log('🌍 Timezone Debug Info:', {
          stored: localStorage.getItem('timezone'),
          detected: Intl.DateTimeFormat().resolvedOptions().timeZone,
          displayName: getTimezoneDisplayName(),
        });
      }
    } catch (e) {
      console.error('❌ Failed to initialize timezone:', e);
    }
  }, []);

  useEffect(() => {
    const userData = getUserFromLocalStorage();
    if (userData) {
      setUserId(userData?.user_id || '');
    }
  }, []);
  console.log(userId, 'User ID from localStorage');
  // Simplified effect to only save to localStorage when toggle changes
  useEffect(() => {
    try {
      if (typeof window !== 'undefined') {
        localStorage.setItem('workflowShowAllToggle', String(showAllWorkflows));
        console.log('✅ Saved toggle state to localStorage:', showAllWorkflows);
      }
    } catch (e) {
      console.error('❌ Failed to save toggle state to localStorage:', e);
    }
  }, [showAllWorkflows]);

  // Store initial task and workflow IDs
  const [initialTaskIdState, setInitialTaskIdState] = useState<string | null>(
    initialTaskId || null
  );
  const [initialWorkflowId, setInitialWorkflowId] = useState<string | null>(null);
  const [initialActiveContext, setInitialActiveContext] = useState<WorkflowContext | null>(null);

  // Add new state for options
  const [apiOptions, setApiOptions] = useState<WorkflowData['options'] | null>(null);

  // Form-related state
  const [formFields, setFormFields] = useState<FormSection[]>([]);
  console.log('🚀 ~ formFields:', formFields);
  const [formData, setFormData] = useState<Record<string, string | boolean | number>>({});
  const [formHasErrors, setFormHasErrors] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [pageDataLoading, setPageDataLoading] = useState(true);
  const [filtersLoading, setFiltersLoading] = useState(false);
  const [isEmptyDueToFilters, setIsEmptyDueToFilters] = useState(false);
  const [initialFilterCheckComplete, setInitialFilterCheckComplete] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [saveError, setSaveError] = useState<string | null>(null);
  const [showFilter, setShowFilter] = useState(false);
  const [expanded] = useState(true);
  const [emailInfo, setEmailInfo] = useState<EmailInfo | null>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [noticeSummary, setNoticeSummary] = useState<Record<string, number | any> | null>(null);
  const [canComplete, setCanComplete] = useState(false);
  const [clientNotFound, setClientNotFound] = useState(false);
  const [initialAssignmentDone, setInitialAssignmentDone] = useState(false);

  // Add state for access control
  // const [userHasAccess, setUserHasAccess] = useState<boolean | null>(null);
  // const [accessCheckLoading, setAccessCheckLoading] = useState(false);
  const [showAccessDeniedModal, setShowAccessDeniedModal] = useState(false);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [courtNoticeEventsCount, setCourtNoticeEventsCount] = useState(0);
  const [showNotification, setShowNotification] = useState(true);

  // Multi-select dropdown state for Assign To
  const [assignToDropdownOpen, setAssignToDropdownOpen] = useState(false);
  const [assignToSearchTerm, setAssignToSearchTerm] = useState('');
  const [assignToOptions, setAssignToOptions] = useState<
    Array<{
      label: string | undefined;
      profile_picture: string | undefined;
      value: string;
      text: string;
      id: string;
      _id?: string;
      is_active?: boolean;
      profileImage?: string | null;
      initials?: string;
      type?: string;
    }>
  >([]);
  const [assignToSelectedValues, setAssignToSelectedValues] = useState<string[]>([]);
  const [assignedUsersData, setAssignedUsersData] = useState<any[]>([]);
  const [taskAssignments, setTaskAssignments] = useState<Record<string, string[]>>({});
  const [assignToLoading, setAssignToLoading] = useState(false);
  const [assignToHighlightedIndex, setAssignToHighlightedIndex] = useState<number>(-1);
  const assignToDropdownRef = useRef<HTMLDivElement>(null);
  const assignToSearchInputRef = useRef<HTMLInputElement>(null);
  const assignToTriggerRef = useRef<HTMLDivElement>(null);

  // Responsive design state and constants
  const [screenSize, setScreenSize] = useState<
    'sm' | 'md' | 'lg' | 'xl' | 'xl-plus' | '2xl' | '3xl' | '4xl' | '5xl'
  >('md');
  const [isLeftPanelCollapsed, setIsLeftPanelCollapsed] = useState(false);
  const [isMobileView, setIsMobileView] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [leftPanelPercent, setLeftPanelPercent] = useState(20);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [rightPanelPercent, setRightPanelPercent] = useState(33);
  // Responsive breakpoints - enhanced for large screens
  const breakpoints = {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    'xl-plus': 1440,
    '2xl': 1536,
    '3xl': 1920, // Added support for very large screens
    '4xl': 2560, // Added support for ultra-wide screens
    '5xl': 3840,
  };

  // Resizable panels state - now responsive with better large screen support
  const [leftPanelWidth, setLeftPanelWidth] = useState(400); // Will be overridden by responsive logic
  const [isResizingLeft, setIsResizingLeft] = useState(false);
  const [isResizingCenter, setIsResizingCenter] = useState(false);
  const [showDateModal, setShowDateModal] = useState(false); // Add this line
  const leftPanelRef = useRef<HTMLDivElement>(null);
  const centerPanelRef = useRef<HTMLDivElement>(null);
  const rightPanelRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [reviewTask, setReviewTask] = useState<string[]>([]);
  const filterRef = useRef<HTMLDivElement>(null);

  // const [activeIcon, setActiveIcon] = useState(0);

  const [filters, setFilters] = useState(() => {
    const defaultFilters = {
      myTasks: true,
      unassigned: true,
      allTasks: false,
      assigned_to_my_group: true,
    };

    try {
      // Check if running in browser before using localStorage
      if (typeof window !== 'undefined') {
        // Get current workflow ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const currentWorkflowId = urlParams.get('work_flow_id');

        // Get stored filters and workflow ID
        const savedFilters = localStorage.getItem('workflowFilters');
        const storedWorkflowId = localStorage.getItem('currentWorkflowId');

        // Check if this is external navigation
        const isFromExternalRoute = sessionStorage.getItem('isFromExternalRoute') === 'true';
        const hasWorkflowNavigation = sessionStorage.getItem('workflowNavigation') === 'true';

        // Detect hard refresh: no session flags AND same workflow ID
        const isHardRefresh = !isFromExternalRoute && !hasWorkflowNavigation &&
          storedWorkflowId === currentWorkflowId;

        // Reset filters if:
        // 1. Coming from external route (sidebar click, new button, etc.)
        // 2. Different workflow ID (most important check)
        // 3. Hard refresh - ALWAYS reset filters on hard refresh
        const shouldResetFilters = isFromExternalRoute || storedWorkflowId !== currentWorkflowId || isHardRefresh;

        if (shouldResetFilters) {
          console.log('🔄 Resetting filters - external navigation, different workflow, or hard refresh:', {
            isFromExternalRoute,
            hasWorkflowNavigation,
            isHardRefresh,
            workflowChanged: storedWorkflowId !== currentWorkflowId,
            oldWorkflow: storedWorkflowId,
            newWorkflow: currentWorkflowId,
            resetReason: isHardRefresh ? 'hard_refresh' :
              isFromExternalRoute ? 'external_route' :
                storedWorkflowId !== currentWorkflowId ? 'workflow_changed' :
                  'unknown'
          });

          // Clear external route flag and set workflow navigation flag
          sessionStorage.removeItem('isFromExternalRoute');
          sessionStorage.setItem('workflowNavigation', 'true');

          // Store current workflow ID and default filters
          if (currentWorkflowId) {
            localStorage.setItem('currentWorkflowId', currentWorkflowId);
            localStorage.setItem('workflowFilters', JSON.stringify(defaultFilters));
          }

          return defaultFilters;
        }

        // Use saved filters if navigating within same workflow
        if (savedFilters && storedWorkflowId === currentWorkflowId && currentWorkflowId) {
          // Ensure workflow navigation flag is set for same workflow navigation
          sessionStorage.setItem('workflowNavigation', 'true');
          sessionStorage.removeItem('isFromExternalRoute');

          const parsedFilters = JSON.parse(savedFilters);
          console.log('🔄 Using saved filters for same workflow navigation:', parsedFilters);
          return { ...defaultFilters, ...parsedFilters };
        }

        // If we're in the same workflow but don't have saved filters, use defaults but set navigation flag
        if (storedWorkflowId === currentWorkflowId && currentWorkflowId) {
          sessionStorage.setItem('workflowNavigation', 'true');
          sessionStorage.removeItem('isFromExternalRoute');
          console.log('🔄 Same workflow detected, using default filters but preserving navigation state');

          // Store default filters for this workflow
          localStorage.setItem('currentWorkflowId', currentWorkflowId);
          localStorage.setItem('workflowFilters', JSON.stringify(defaultFilters));

          return defaultFilters;
        }
      }
    } catch (e) {
      console.error('❌ Failed to load filters from localStorage:', e);
    }

    console.log('🔄 Using default filters:', defaultFilters);
    return defaultFilters;
  });

  // Effect to save filters to localStorage whenever filters change, but only for the same workflow
  // Using useRef to prevent infinite loops by tracking the last saved filters
  const lastSavedFiltersRef = useRef<string>('');
  
  useEffect(() => {
    try {
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        const currentWorkflowId = urlParams.get('work_flow_id');

        // Save filters for current workflow (even if it's a new workflow)
        if (currentWorkflowId) {
          const filtersString = JSON.stringify(filters);
          
          // Only save if filters actually changed to prevent infinite loops
          if (lastSavedFiltersRef.current !== filtersString) {
            localStorage.setItem('currentWorkflowId', currentWorkflowId);
            localStorage.setItem('workflowFilters', filtersString);
            lastSavedFiltersRef.current = filtersString;
            console.log('✅ Auto-saved filters to localStorage for workflow:', currentWorkflowId, filters);
            
            // Ensure we maintain workflow navigation state
            sessionStorage.setItem('workflowNavigation', 'true');
            sessionStorage.removeItem('isFromExternalRoute');
          }
        }
      }
    } catch (e) {
      console.error('❌ Failed to auto-save filters to localStorage:', e);
    }
  }, [filters]);

  // Debug effect to monitor component mount and filter loading
  useEffect(() => {
    console.log('🚀 WorkflowRun component mounted/updated');
    console.log('🔍 Current filter state on mount:', filters);

    // Ensure workflow navigation flag is set if we're in the same workflow
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const currentWorkflowId = urlParams.get('work_flow_id');
      const storedWorkflowId = localStorage.getItem('currentWorkflowId');

      if (currentWorkflowId === storedWorkflowId && currentWorkflowId) {
        // We're in the same workflow, ensure navigation flag is set
        sessionStorage.setItem('workflowNavigation', 'true');
        sessionStorage.removeItem('isFromExternalRoute');
        console.log('🔄 Same workflow detected on mount, setting navigation flags');
      }
    }
  }, []); // Only run on mount

  // Additional effect to handle task ID changes within the same workflow
  useEffect(() => {
    if (typeof window !== 'undefined' && taskId && work_flow_id) {
      const currentWorkflowId = Array.isArray(work_flow_id) ? work_flow_id[0] : work_flow_id;
      const storedWorkflowId = localStorage.getItem('currentWorkflowId');

      // If we're navigating within the same workflow, ensure flags are preserved
      if (currentWorkflowId === storedWorkflowId && currentWorkflowId) {
        sessionStorage.setItem('workflowNavigation', 'true');
        sessionStorage.removeItem('isFromExternalRoute');
        console.log('🔄 Task navigation within same workflow detected, preserving navigation flags');

        // CRITICAL: Save current filter state immediately when task changes within same workflow
        localStorage.setItem('currentWorkflowId', currentWorkflowId);
        localStorage.setItem('workflowFilters', JSON.stringify(filters));
        console.log('🔄 Saved filters on task change within same workflow:', filters);
      }
    }
  }, [taskId, work_flow_id]); // Removed filters to prevent infinite loops - filters are saved via auto-save effect

  // Effect to handle workflow ID changes and reset filters when needed
  useEffect(() => {
    if (typeof window !== 'undefined' && work_flow_id) {
      const currentWorkflowId = Array.isArray(work_flow_id) ? work_flow_id[0] : work_flow_id;
      const storedWorkflowId = localStorage.getItem('currentWorkflowId');

      // If workflow ID changed, reset to defaults
      if (storedWorkflowId !== currentWorkflowId) {
        const defaultFilters = {
          myTasks: true,
          unassigned: true,
          allTasks: false,
          assigned_to_my_group: true,
        };

        console.log('🔄 Workflow ID changed, resetting filters to default:', {
          oldId: storedWorkflowId,
          newId: currentWorkflowId,
          defaultFilters
        });

        // Update localStorage with new workflow ID and default filters
        localStorage.setItem('currentWorkflowId', currentWorkflowId);
        localStorage.setItem('workflowFilters', JSON.stringify(defaultFilters));

        // Update both filter states
        setFilters(defaultFilters);
        setPendingFilters(defaultFilters);
      }
    }
  }, [work_flow_id]); // Run when workflow ID changes

  // State to trigger re-renders when filters are applied
  const [filtersApplied, setFiltersApplied] = useState(false);

  // Separate state for pending filter changes (not yet applied)
  const [pendingFilters, setPendingFilters] = useState(() => {
    const defaultFilters = {
      myTasks: true,
      unassigned: true,
      allTasks: false,
      assigned_to_my_group: true,
    };

    try {
      // Check if running in browser before using localStorage
      if (typeof window !== 'undefined') {
        // Get current workflow ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const currentWorkflowId = urlParams.get('work_flow_id');

        // Get stored filters and workflow ID
        const savedFilters = localStorage.getItem('workflowFilters');
        const storedWorkflowId = localStorage.getItem('currentWorkflowId');

        // Check if this is external navigation (same logic as main filters)
        const isFromExternalRoute = sessionStorage.getItem('isFromExternalRoute') === 'true';
        const hasWorkflowNavigation = sessionStorage.getItem('workflowNavigation') === 'true';

        // Detect hard refresh: no session flags AND same workflow ID
        const isHardRefresh = !isFromExternalRoute && !hasWorkflowNavigation &&
          storedWorkflowId === currentWorkflowId;

        // Reset filters if:
        // 1. Coming from external route (sidebar click, new button, etc.)
        // 2. Different workflow ID (most important check)
        // 3. Hard refresh - ALWAYS reset filters on hard refresh
        const shouldResetFilters = isFromExternalRoute || storedWorkflowId !== currentWorkflowId || isHardRefresh;

        if (shouldResetFilters) {
          console.log('🔄 Using default pending filters - external navigation, different workflow, or hard refresh');
          return defaultFilters;
        }

        // Use saved filters if navigating within same workflow
        if (savedFilters && storedWorkflowId === currentWorkflowId && currentWorkflowId) {
          const parsedFilters = JSON.parse(savedFilters);
          console.log('🔄 Using saved pending filters for same workflow navigation:', parsedFilters);
          return { ...defaultFilters, ...parsedFilters };
        }

        // CRITICAL: If we're in the same workflow (even without saved filters), preserve state
        // This handles reverse navigation when filters haven't been saved yet
        if (storedWorkflowId === currentWorkflowId && currentWorkflowId) {
          console.log('🔄 Same workflow detected - preserving filter state for reverse navigation');
          // Set session flags to ensure we're treated as internal navigation
          sessionStorage.setItem('workflowNavigation', 'true');
          sessionStorage.removeItem('isFromExternalRoute');

          // If we have saved filters, use them, otherwise use defaults
          if (savedFilters) {
            const parsedFilters = JSON.parse(savedFilters);
            return { ...defaultFilters, ...parsedFilters };
          }

          // Use defaults but mark this as same workflow navigation
          localStorage.setItem('workflowFilters', JSON.stringify(defaultFilters));
          return defaultFilters;
        }
      }
    } catch (e) {
      console.error('❌ Failed to load pending filters from localStorage:', e);
    }

    console.log('🔄 Using default pending filters:', defaultFilters);
    return defaultFilters;
  });

  const handleFilterChange = (filter: keyof typeof filters) => {
    console.log(
      '🔍 Filter change requested for:',
      filter,
      'Current state:',
      pendingFilters[filter]
    );
    const newFilters = { ...pendingFilters };

    // Handle "All Tasks" logic - when selected, auto-check other options
    if (filter === 'allTasks' && !pendingFilters.allTasks) {
      newFilters.allTasks = true;
      newFilters.myTasks = true;
      newFilters.unassigned = true;
      newFilters.assigned_to_my_group = true;
      console.log('🔍 All Tasks selected, enabling all filters');
      // Reset empty state when All Tasks is selected
      setIsEmptyDueToFilters(false);
    } else {
      newFilters[filter] = !pendingFilters[filter];
      console.log(
        '🔍 Toggling filter:',
        filter,
        'from',
        pendingFilters[filter],
        'to',
        newFilters[filter]
      );
    }

    console.log('🔍 New pending filter state:', newFilters);
    setPendingFilters(newFilters);
  };

  // Function to check if a task should be visible based on filters and user permissions
  const shouldShowTask = useCallback((task: any): boolean => {
    const currentUserId = getUserFromLocalStorage()?.user_id;

    // Work with task.assigns array directly
    const taskAssigns = task.assigns || [];

    // Helper functions to analyze the assigns array structure
    // Simple check: if currentUserId exists in any assign.user_id, regardless of isdeSelected flag
    const isCurrentUserAssigned = taskAssigns.some(
      (assign: any) => assign.user_id === currentUserId && assign.type === 'user'
    );

    const hasGroupTypeAssignments = taskAssigns.some((assign: any) => assign.type === 'group');

    // Check if task has ANY user type assignments (not just current user)
    const hasUserTypeAssignments = taskAssigns.some((assign: any) => assign.type === 'user');

    // Task is unassigned if it has no user type assignments (can have group assignments)
    const isUnassigned = !hasUserTypeAssignments;

    // Check if task is assigned to user's group (group assignment where current user is member)
    // For group assignments, we check if the current user ID matches the user_id in group assignments

    const isAssignedToMyGroup =
      hasGroupTypeAssignments &&
      taskAssigns.some(
        (assign: any) => assign.type === 'group' && assign.user_id === currentUserId
      );

    console.log(`🔍 FILTERING Task "${task.name}" (ID: ${task.id}):`, {
      currentUserId,
      taskAssigns: taskAssigns,
      isCurrentUserAssigned,
      hasGroupTypeAssignments,
      hasUserTypeAssignments,
      isUnassigned,
      isAssignedToMyGroup,
      activeFilters: filters,
      taskAssignsCount: taskAssigns.length,
    });

    // Simple filter logic based on requirements:

    // 1. All Tasks - show all tasks including other assignee tasks, AI Agent assigned tasks, Group assigned tasks
    if (filters.allTasks) {
      console.log(`✅ Task "${task.name}" shown: All Tasks filter - show all tasks`);
      return true;
    }

    let shouldShow = false;

    // 2. My Tasks (Assigned to Me) - tasks where currentUserId exists in assigns array
    if (filters.myTasks && isCurrentUserAssigned) {
      console.log(`✅ Task "${task.name}" matches My Tasks filter`);
      shouldShow = true;
    }

    // 3. Unassigned - tasks with no user type assignments (can have group assignments)
    if (filters.unassigned && isUnassigned) {
      console.log(`✅ Task "${task.name}" matches Unassigned filter`);
      shouldShow = true;
    }

    // 4. Assigned to My Group - tasks assigned to groups where logged-in user is a member
    if (filters.assigned_to_my_group && isAssignedToMyGroup) {
      console.log(`✅ Task "${task.name}" matches Assigned to My Group filter`);
      shouldShow = true;
    }

    // STRICT FILTERING: Only apply fallbacks for genuine technical issues
    // Remove overly permissive fallbacks that show tasks when they shouldn't match filters
    
    // Only show unassigned tasks if they have NO assignments AND unassigned filter is enabled
    if (!shouldShow && filters.unassigned && taskAssigns.length === 0) {
      console.log(`✅ Task "${task.name}" shown: Truly unassigned task with unassigned filter enabled`);
      shouldShow = true;
    }

    if (shouldShow) {
      console.log(`✅ RESULT: Task "${task.name}" SHOWN - matches selected filters`);
      return true;
    } else {
      console.log(`❌ RESULT: Task "${task.name}" HIDDEN - doesn't match any selected filters`);
      console.log(`   Filter states: myTasks=${filters.myTasks}, unassigned=${filters.unassigned}, allTasks=${filters.allTasks}, assigned_to_my_group=${filters.assigned_to_my_group}`);
      return false;
    }
  }, [filters]);

  // Helper function to check if task should show blue dot indicator
  const shouldShowBlueDot = (task: any): boolean => {
    const currentUserId = getUserFromLocalStorage()?.user_id;
    const taskAssigns = task.assigns || [];

    // Check if current user is assigned to this task
    const isCurrentUserAssigned = taskAssigns.some(
      (assign: any) => assign.user_id === currentUserId && assign.type === 'user'
    );

    // Check if task is not reviewed
    const isNotReviewed = task.task_visible_status !== 'REVIEWED';

    // Show blue dot only if:
    // 1. Current user is assigned to the task
    // 2. Task is not reviewed (is_reviewed === false)
    return isCurrentUserAssigned && isNotReviewed;
  };

  // Helper function to check if any tasks are visible across all workflows after filtering
  const hasVisibleTasks = useCallback((): boolean => {
    if (!workflowData) return false;

    // Check main workflow
    if (workflowData.workflows && workflowData.workflows.length > 0) {
      const shouldShowMainWorkflow = showAllWorkflows || initialActiveContext?.type === 'main';
      
      if (shouldShowMainWorkflow) {
        const visibleMainTasks = workflowData.workflows[0].tasks.filter(
          task => task.task_visible_status !== 'DRAFT' && shouldShowTask(task)
        );
        if (visibleMainTasks.length > 0) return true;
      }
    }

    // Check child workflows
    const childWorkflowKeys = ['child_workflow_1', 'child_workflow_2'];
    for (const key of childWorkflowKeys) {
      const childWorkflows = workflowData[key] as Workflow[];
      if (childWorkflows && childWorkflows.length > 0) {
        for (const childWorkflow of childWorkflows) {
          const shouldShowChildWorkflow = showAllWorkflows || 
            (initialActiveContext?.type === key && initialActiveContext?.workflow?.id === childWorkflow.id);
          
          if (shouldShowChildWorkflow) {
            const visibleChildTasks = childWorkflow.tasks.filter(
              task => task.task_visible_status !== 'DRAFT' && shouldShowTask(task)
            );
            if (visibleChildTasks.length > 0) return true;
          }
        }
      }
    }

    return false;
  }, [workflowData, showAllWorkflows, initialActiveContext, shouldShowTask]);

  const applyFilters = async () => {
    try {
      console.log('🔍 Applying pending filters:', pendingFilters);
      console.log('🔍 Current selected task:', selectedTask?.name);
      console.log('🔍 Current selected workflow:', selectedWorkflow?.name);

      // Show loading during filter application to prevent flickering
      setFiltersLoading(true);
      setIsEmptyDueToFilters(false); // Reset flag when applying new filters

      // Apply the pending filters to the actual filters state
      setFilters(pendingFilters);

      // Save filters to localStorage for persistence
      try {
        if (typeof window !== 'undefined') {
          localStorage.setItem('workflowFilters', JSON.stringify(pendingFilters));
          console.log('✅ Saved filters to localStorage:', pendingFilters);
        }
      } catch (e) {
        console.error('❌ Failed to save filters to localStorage:', e);
      }

      // Check if current selected task still matches the new filters
      if (selectedTask && selectedWorkflow) {
        // Temporarily update filters to check visibility with new filters

        // Create a function that checks visibility with pending filters
        const checkTaskVisibilityWithPendingFilters = (task: any): boolean => {
          const currentUserId = getUserFromLocalStorage()?.user_id;
          const taskAssigns = task.assigns || [];

          const isCurrentUserAssigned = taskAssigns.some(
            (assign: any) => assign.user_id === currentUserId && assign.type === 'user'
          );
          const hasGroupTypeAssignments = taskAssigns.some((assign: any) => assign.type === 'group');
          const hasUserTypeAssignments = taskAssigns.some((assign: any) => assign.type === 'user');
          const isUnassigned = !hasUserTypeAssignments;
          const isAssignedToMyGroup = hasGroupTypeAssignments && taskAssigns.some(
            (assign: any) => assign.type === 'group' && assign.user_id === currentUserId
          );

          if (pendingFilters.allTasks) return true;

          let shouldShow = false;
          if (pendingFilters.myTasks && isCurrentUserAssigned) shouldShow = true;
          if (pendingFilters.unassigned && isUnassigned) shouldShow = true;
          if (pendingFilters.assigned_to_my_group && isAssignedToMyGroup) shouldShow = true;

          return shouldShow;
        };

        const taskStillVisible = selectedTask.task_visible_status !== 'DRAFT' && checkTaskVisibilityWithPendingFilters(selectedTask);

        console.log('🔍 Task still visible after filter change:', taskStillVisible);

        if (!taskStillVisible) {
          console.log('🔍 Current task no longer matches filters, clearing selection');
          setSelectedTask(null);
          setFormFields([]);
          setFormData({});

          // Try to find another task that matches the filters using pending filters
          const visibleTasks = selectedWorkflow.tasks.filter(task => {
            if (task.task_visible_status === 'DRAFT') return false;
            return checkTaskVisibilityWithPendingFilters(task);
          });

          console.log('🔍 Found', visibleTasks.length, 'tasks matching new filters');

          if (visibleTasks.length > 0) {
            console.log('🔍 Selected new task after filter change:', visibleTasks[0].name);
            selectTask(visibleTasks[0]);
          } else {
            console.log('🔍 No tasks match new filters, showing empty state');
          }
        }
      } else if (selectedWorkflow) {
        // Create the same function for this scope
        const checkTaskVisibilityWithPendingFilters = (task: any): boolean => {
          const currentUserId = getUserFromLocalStorage()?.user_id;
          const taskAssigns = task.assigns || [];

          const isCurrentUserAssigned = taskAssigns.some(
            (assign: any) => assign.user_id === currentUserId && assign.type === 'user'
          );
          const hasGroupTypeAssignments = taskAssigns.some((assign: any) => assign.type === 'group');
          const hasUserTypeAssignments = taskAssigns.some((assign: any) => assign.type === 'user');
          const isUnassigned = !hasUserTypeAssignments;
          const isAssignedToMyGroup = hasGroupTypeAssignments && taskAssigns.some(
            (assign: any) => assign.type === 'group' && assign.user_id === currentUserId
          );

          if (pendingFilters.allTasks) return true;

          let shouldShow = false;
          if (pendingFilters.myTasks && isCurrentUserAssigned) shouldShow = true;
          if (pendingFilters.unassigned && isUnassigned) shouldShow = true;
          if (pendingFilters.assigned_to_my_group && isAssignedToMyGroup) shouldShow = true;

          return shouldShow;
        };

        // No task selected, check if we should select one based on new filters
        const visibleTasks = selectedWorkflow.tasks.filter(task => {
          if (task.task_visible_status === 'DRAFT') return false;
          return checkTaskVisibilityWithPendingFilters(task);
        });

        console.log('🔍 No task selected, found', visibleTasks.length, 'tasks matching filters');

        if (visibleTasks.length > 0) {
          console.log('🔍 Selecting first task that matches filters:', visibleTasks[0].name);
          selectTask(visibleTasks[0]);
        }
      }

      // Use the new refreshWorkflowData function for consistency
      await refreshWorkflowData();

      // Force re-render by updating a state or triggering useEffect
      console.log('🔄 Refreshing task display...');
      setFiltersApplied(!filtersApplied);

      // Check if there are any visible tasks after applying filters
      setTimeout(() => {
        const hasAnyVisibleTasks = hasVisibleTasks();
        console.log('🔍 Has visible tasks after filter:', hasAnyVisibleTasks);
        
        // Only show empty state if filters are applied and no tasks are visible
        const hasActiveFilters = !pendingFilters.allTasks && 
          (pendingFilters.myTasks || pendingFilters.unassigned || pendingFilters.assigned_to_my_group);
        
        // If no tasks are visible, clear the selected task and show empty state
        if (!hasAnyVisibleTasks && hasActiveFilters) {
          console.log('🔍 No visible tasks found, clearing selected task and showing empty state');
          setSelectedTask(null);
          setFormFields([]);
          setFormData({});
          setIsEmptyDueToFilters(true);
        } else {
          setIsEmptyDueToFilters(false);
        }
        
        setInitialFilterCheckComplete(true); // Mark filter check as complete
        setFiltersLoading(false);
      }, 100);

      // Close the filter dialog
      setShowFilter(false);

      console.log('✅ Filters applied successfully with state:', pendingFilters);
    } catch (error) {
      console.error('❌ Error applying filters:', error);
      setShowFilter(false);
      setFiltersLoading(false);
    }
  };

  const handleFilterDialogOpen = () => {
    // Reset pending filters to current applied filters when opening dialog
    setPendingFilters(filters);
    setShowFilter(true);
  };

  const handleFilterDialogCancel = () => {
    // Reset pending filters to current applied filters when cancelling
    setPendingFilters(filters);
    setShowFilter(false);
  };

  // Responsive helper functions - enhanced for large screens
  const getScreenSize = (
    width: number
  ): 'sm' | 'md' | 'lg' | 'xl' | 'xl-plus' | '2xl' | '3xl' | '4xl' | '5xl' => {
    if (width < breakpoints.sm) return 'sm';
    if (width < breakpoints.md) return 'md';
    if (width < breakpoints.lg) return 'lg';
    if (width < breakpoints.xl) return 'xl';
    if (width < breakpoints['xl-plus']) return 'xl'; // Under 1440px
    if (width < breakpoints['2xl']) return 'xl-plus'; // 1440px and above
    if (width < breakpoints['3xl']) return '2xl';
    if (width < breakpoints['4xl']) return '3xl';
    if (width < breakpoints['5xl']) return '4xl';
    return '5xl';
  };

  const getResponsiveLeftPanelWidth = (): number => {
    if (typeof window === 'undefined') return 320;

    const screenWidth = window.innerWidth;

    switch (screenSize) {
      case 'sm':
      case 'md':
        return isLeftPanelCollapsed ? 0 : Math.min(280, screenWidth * 0.75);
      case 'lg':
        return isLeftPanelCollapsed ? 60 : Math.min(320, screenWidth * 0.25);
      case 'xl':
        return isLeftPanelCollapsed ? 60 : Math.min(350, screenWidth * 0.5);
      case '2xl':
        // For large screens, make panels more compact
        if (screenWidth >= 1920) {
          return isLeftPanelCollapsed ? 60 : Math.min(380, screenWidth * 0.18) + 55;
        } else {
          return isLeftPanelCollapsed ? 60 : Math.min(360, screenWidth * 0.2);
        }
      default:
        return isLeftPanelCollapsed ? 60 : 320;
    }
  };

  const getSidebarWidth = (): number => {
    switch (screenSize) {
      case 'sm':
      case 'md':
        return 56;
      case 'lg':
        return 64;
      case 'xl':
      case '2xl':
        return 64; // Slightly reduced from 72
      default:
        return 64;
    }
  };

  // Enhanced responsive effect with better large screen support
  useEffect(() => {
    const handleResize = () => {
      if (typeof window !== 'undefined') {
        const width = window.innerWidth;
        const height = window.innerHeight;
        const newScreenSize = getScreenSize(width);
        setScreenSize(newScreenSize);
        setIsMobileView(width < breakpoints.md);

        // Auto-collapse left panel on smaller screens
        if (width < breakpoints.lg && !isLeftPanelCollapsed) {
          setIsLeftPanelCollapsed(true);
        } else if (width >= breakpoints.xl && isLeftPanelCollapsed) {
          // Auto-expand on large screens
          setIsLeftPanelCollapsed(false);
        }

        // Auto-close overlay when screen size changes to 1440px or above
        if (width >= 1440 && isRightPanelOverlayOpen) {
          setIsRightPanelOverlayOpen(false);
        }

        // Update left panel width based on screen size
        setLeftPanelWidth(getResponsiveLeftPanelWidth());

        // Update dynamic left panel width for 2028px-2560px range
        if (width >= 2028 && width < 2560) {
          const dynamicWidth = Math.max(404, 758 - ((2560 - width) * (758 - 404) / (2560 - 2028)));
          setDynamicLeftPanelWidth(dynamicWidth);
        } else if (width >= 2560) {
          setDynamicLeftPanelWidth(758);
        } else if (width >= 1728) {
          setDynamicLeftPanelWidth(404);
        } else if (width >= 1440) {
          // Dynamic width for 1440px-1728px range: 404px to 300px
          const dynamicWidth = Math.max(300, 404 - ((1728 - width) * (404 - 300) / (1728 - 1440)));
          setDynamicLeftPanelWidth(dynamicWidth);
        } else {
          setDynamicLeftPanelWidth(300);
        }

        // Update dynamic right panel width for 2028px-2560px range
        if (width >= 2028 && width < 2560) {
          const dynamicWidth = Math.max(600, 758 - ((2560 - width) * (758 - 600) / (2560 - 2028)));
          setDynamicRightPanelWidth(dynamicWidth);
        } else if (width >= 2560) {
          setDynamicRightPanelWidth(758);
        } else {
          setDynamicRightPanelWidth(600);
        }



        // Handle orientation changes on mobile
        if (width < breakpoints.md && height < 600) {
          // Landscape mode on mobile - optimize layout
          setIsLeftPanelCollapsed(true);
        }

        // For very large screens, ensure panels don't become too wide
        if (width >= 2560) {
          // Ultra-wide screen adjustments
          const maxLeftWidth = Math.min(600, width * 0.2);
          if (leftPanelWidth > maxLeftWidth) {
            setLeftPanelWidth(maxLeftWidth);
          }
        }
      }
    };

    // Set initial values
    handleResize();

    // Use ResizeObserver for better performance if available
    let resizeObserver: ResizeObserver | null = null;

    if (typeof window !== 'undefined' && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver(handleResize);
      resizeObserver.observe(document.body);
    } else if (typeof window !== 'undefined') {
      // Fallback to window resize events
      const w = window as Window; // 👈 Explicitly cast to Window
      w.addEventListener('resize', handleResize);
      w.addEventListener('orientationchange', handleResize);
    }

    return () => {
      if (resizeObserver) {
        resizeObserver.disconnect();
      } else if (typeof window !== 'undefined') {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('orientationchange', handleResize);
      }
    };
  }, [screenSize, isLeftPanelCollapsed, leftPanelWidth]);

  // Update screen size on window resize
  useEffect(() => {
    const handleResize = () => {
      if (typeof window !== 'undefined') {
        const width = window.innerWidth;
        const newScreenSize = getScreenSize(width);
        setScreenSize(newScreenSize);
        setIsMobileView(width < breakpoints.md);

        // Auto-collapse left panel on smaller screens
        if (width < breakpoints.lg && !isLeftPanelCollapsed) {
          setIsLeftPanelCollapsed(true);
        }

        // Auto-close overlay when screen size changes to 1440px or above
        if (width >= 1440 && isRightPanelOverlayOpen) {
          setIsRightPanelOverlayOpen(false);
        }

        // Update left panel width based on screen size
        setLeftPanelWidth(getResponsiveLeftPanelWidth());
      }
    };

    // Set initial values
    handleResize();

    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [screenSize, isLeftPanelCollapsed]);

  const sidebarIcons = [
    {
      icon: '/IconsBar/ai-not-select.svg',
      activeIcon: '/IconsBar/ai-left.svg',
      active: true,
    },
    {
      icon: '/IconsBar/contacts-two-toned.svg',
      activeIcon: '/IconsBar/contacts-two-toned.svg',
      active: false,
    },
    {
      icon: '/IconsBar/office-bag-two-toned.svg',
      activeIcon: '/IconsBar/office-bag-two-toned.svg',
      active: false,
    },
    {
      icon: '/IconsBar/file-edit-two-toned.svg',
      activeIcon: '/IconsBar/file-edit-two-toned.svg',
      active: false,
    },
    {
      icon: '/IconsBar/communication.svg',
      activeIcon: '/IconsBar/communication.svg',
      active: false,
    },
    {
      icon: '/IconsBar/file-two-toned.svg',
      activeIcon: '/IconsBar/file-two-toned.svg',
      active: false,
    },
    {
      icon: '/IconsBar/calendar-two-toned.svg',
      activeIcon: '/IconsBar/calendar-two-toned.svg',
      active: false,
    },
    {
      icon: '/IconsBar/billing-two-toned.svg',
      activeIcon: '/IconsBar/billing-two-toned.svg',
      active: false,
    },
    {
      icon: '/IconsBar/tool-two-toned.svg',
      activeIcon: '/IconsBar/tool-two-toned.svg',
      active: false,
    },
    {
      icon: '/IconsBar/dots-horizontal-two-toned.svg',
      activeIcon: '/IconsBar/dots-horizontal.svg',
      active: false,
    },
  ];

  // After line 1066, update the state management to include collapsed state information
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({
    main: true, // Main workflow is expanded by default
  });

  // Add ref to track if we've initialized expandedSections for child workflows
  const initializedSectionsRef = useRef<Set<string>>(new Set());

  // New function to toggle section expansion
  const toggleSectionExpansion = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey],
    }));
  };

  // Add useEffect to initialize expandedSections for child workflows
  useEffect(() => {
    if (workflowData && childWorkflows) {
      const newExpandedSections: { [key: string]: boolean } = { ...expandedSections };
      let hasChanges = false;

      Object.entries(childWorkflows).forEach(([key, workflows]) => {
        workflows.forEach((childWorkflow, childIndex) => {
          const sectionKey = `${key}-${childIndex}`;
          if (
            !initializedSectionsRef.current.has(sectionKey) &&
            newExpandedSections[sectionKey] === undefined
          ) {
            newExpandedSections[sectionKey] = true; // Default to expanded
            initializedSectionsRef.current.add(sectionKey);
            hasChanges = true;
          }
        });
      });

      if (hasChanges) {
        setExpandedSections(newExpandedSections);
      }
    }
  }, [workflowData, childWorkflows]);

  const filteredAssignToOptions = assignToSearchTerm
    ? assignToOptions.filter(option =>
      option.text.toLowerCase().includes(assignToSearchTerm.toLowerCase())
    )
    : assignToOptions;

  // Handler for mouse movement during resize
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isResizingLeft && containerRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect();
        const maxWidth = containerRect.width - (activePanel === 'advisor' ? 440 : 260) - 64 - 250; // Minimum center panel width
        const newWidth = Math.max(250, Math.min(e.clientX - containerRect.left, maxWidth));
        setLeftPanelWidth(newWidth);
      }
    },
    [isResizingLeft, activePanel]
  );

  // Handler for mouse up to stop resizing
  const handleMouseUp = useCallback(() => {
    setIsResizingLeft(false);
    setIsResizingCenter(false);

    // Clean up any resize-related classes and styles
    document.body.classList.remove('resizing-horizontal', 'resizing-vertical');
    document.body.style.userSelect = '';
  }, []);

  // Update initialTaskIdState when prop changes
  useEffect(() => {
    if (initialTaskId) {
      setInitialTaskIdState(initialTaskId);
    }
  }, [initialTaskId]);

  // Set up event listeners for resize
  useEffect(() => {
    if (isResizingLeft || isResizingCenter) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      // Add classes to body during resize
      if (isResizingLeft) {
        document.body.classList.add('resizing-horizontal');
      } else if (isResizingCenter) {
        document.body.classList.add('resizing-horizontal');
      }

      // Disable text selection during resize
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Clean up when unmounting
      document.body.classList.remove('resizing-horizontal');
      document.body.style.userSelect = '';
    };
  }, [isResizingLeft, isResizingCenter, handleMouseMove, handleMouseUp]);

  // Add a separate effect to update global CSS
  useEffect(() => {
    // Create a style element for the custom resize cursor
    const styleElement = document.createElement('style');
    styleElement.innerHTML = `
      .resizing-horizontal {
        cursor: col-resize !important;
      }
      .resizing-vertical {
        cursor: row-resize !important;
      }
      .resize-handle {
        position: relative;
        transition: background-color 0.2s;
      }
      .resize-handle:hover {
        background-color: rgba(59, 130, 246, 0.5) !important;
      }
    `;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // Function to refresh workflow data after user assignment changes
  const refreshWorkflowData = useCallback(async () => {
    try {
      const currentUser = getUserFromLocalStorage();
      const workflowId = getWorkflowIdFromUrl();

      if (!currentUser?.user_id || !workflowId) {
        console.error('Missing user ID or workflow ID for refresh');
        return;
      }

      console.log('🔄 Refreshing workflow data after assignment change...');

      const response = await apiClient.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-render`,
        {
          params: {
            work_flow_id: workflowId,
            userId: currentUser.user_id,
          },
        }
      );

      if (response?.data?.data) {
        const allWorkflowData = response.data.data as unknown as WorkflowData;

        // Apply formatWorkflowName to the initial workflow name if it exists
        if (allWorkflowData.workflows && allWorkflowData.workflows.length > 0) {
          // Extract client names from the current workflow name for formatting
          const currentName = allWorkflowData.workflows[0].name || '';
          const clients = currentName.split(',').map(name => name.trim()).filter(name => name && !name.includes('& more'));

          // Apply formatting if we have clients
          if (clients.length > 0) {
            const formattedName = formatWorkflowName(clients);
            allWorkflowData.workflows[0].name = formattedName;
          }
        }

        // Update workflow data
        setWorkflowData(allWorkflowData);

        // Store options from API if available
        if (allWorkflowData.options) {
          setApiOptions(allWorkflowData.options);
        }

        // Extract child workflows
        const childWorkflowsObj: { [key: string]: Workflow[] } = {};
        Object.keys(allWorkflowData).forEach(key => {
          if (key.startsWith('child_workflow_') && allWorkflowData[key]) {
            childWorkflowsObj[key] = allWorkflowData[key] as Workflow[];
          }
        });
        setChildWorkflows(childWorkflowsObj);

        // Update the current workflow context with fresh data
        if (activeWorkflowContext) {
          let updatedWorkflow: Workflow | undefined;

          if (activeWorkflowContext.type === 'main' && allWorkflowData.workflows?.length > 0) {
            updatedWorkflow = allWorkflowData.workflows[activeWorkflowContext.index];
          } else if (childWorkflowsObj[activeWorkflowContext.type]) {
            updatedWorkflow =
              childWorkflowsObj[activeWorkflowContext.type][activeWorkflowContext.index];
          }

          if (updatedWorkflow) {
            // Update the selected workflow with fresh data
            setSelectedWorkflow(updatedWorkflow);

            // Update the active workflow context
            setActiveWorkflowContext({
              ...activeWorkflowContext,
              workflow: updatedWorkflow,
            });

            // Update the selected task with fresh data if it exists
            if (selectedTask) {
              const updatedTask = updatedWorkflow.tasks.find(task => task.id === selectedTask.id);
              if (updatedTask) {
                setSelectedTask(updatedTask);

                // Update form fields with fresh data
                if (updatedTask.formFields) {
                  setFormFields(updatedTask.formFields);
                }
              }
            }
          }
        }

        console.log('✅ Workflow data refreshed successfully');
      }
    } catch (error) {
      console.error('❌ Error refreshing workflow data:', error);
    }
  }, [activeWorkflowContext, selectedTask, getWorkflowIdFromUrl, getUserFromLocalStorage]);

  // Function to fetch workflow data with all child workflows
  const fetchWorkflowData = useCallback(async () => {
    setLoading(true);
    setPageDataLoading(true);
    try {
      // Make the API call to get workflow data
      const response = await apiClient.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-render`,
        {
          params: {
            work_flow_id: work_flow_id,
            userId: userId,
          },
        }
      );

      const allWorkflowData = response?.data?.data as unknown as WorkflowData;

      // Apply formatWorkflowName to the initial workflow name if it exists
      if (allWorkflowData.workflows && allWorkflowData.workflows.length > 0) {
        // Extract client names from the current workflow name for formatting
        const currentName = allWorkflowData.workflows[0].name || '';
        const clients = currentName.split(',').map(name => name.trim()).filter(name => name && !name.includes('& more'));

        // Apply formatting if we have clients
        if (clients.length > 0) {
          const formattedName = formatWorkflowName(clients);
          allWorkflowData.workflows[0].name = formattedName;
        }
      }

      setWorkflowData(allWorkflowData);
      
      // Check if we have no workflows at all
      if (!allWorkflowData.workflows || allWorkflowData.workflows.length === 0) {
        setIsEmptyDueToFilters(false);
        setInitialFilterCheckComplete(true); // Mark filter check as complete
      }
      // Check if we have no tasks at all (not due to filters)
      else if (allWorkflowData.workflows && allWorkflowData.workflows.length > 0) {
        const totalTasks = allWorkflowData.workflows[0].tasks?.length || 0;
        if (totalTasks === 0) {
          setIsEmptyDueToFilters(false); // No tasks at all, not due to filters
          setInitialFilterCheckComplete(true); // Mark filter check as complete
        } else {
          // Check if current filters result in no visible tasks
          setTimeout(() => {
            const hasAnyVisibleTasks = hasVisibleTasks();
            const hasActiveFilters = !filters.allTasks && 
              (filters.myTasks || filters.unassigned || filters.assigned_to_my_group);
            
            // If no tasks are visible with current filters, clear selection and show empty state
            if (!hasAnyVisibleTasks && hasActiveFilters) {
              console.log('🔍 Initial load: No visible tasks found, clearing selected task');
              setSelectedTask(null);
              setFormFields([]);
              setFormData({});
              setIsEmptyDueToFilters(true);
            } else {
              setIsEmptyDueToFilters(false);
            }
            setInitialFilterCheckComplete(true); // Mark filter check as complete
          }, 100);
        }
      }

      // Store options from API if available
      if (allWorkflowData.options) {
        setApiOptions(allWorkflowData.options);
      }

      // Extract child workflows
      const childWorkflowsObj: { [key: string]: Workflow[] } = {};
      Object.keys(allWorkflowData).forEach(key => {
        if (key.startsWith('child_workflow_') && allWorkflowData[key]) {
          childWorkflowsObj[key] = allWorkflowData[key] as Workflow[];
        }
      });
      setChildWorkflows(childWorkflowsObj);

      // Check if we have a task ID in the URL that might be in a child workflow
      const urlTaskId = router.query.taskId as string;
      let foundInChildWorkflow = false;

      if (urlTaskId) {
        // Save initial taskId when first loading
        if (!initialTaskIdState) {
          setInitialTaskIdState(urlTaskId);
          setInitialWorkflowId(getWorkflowIdFromUrl());
        }

        // First check if the task is in any of the child workflows
        for (const key in childWorkflowsObj) {
          const childWorkflows = childWorkflowsObj[key];
          for (let i = 0; i < childWorkflows.length; i++) {
            const childWorkflow = childWorkflows[i];
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const childTask = childWorkflow.tasks.find((t: any) => t.id === urlTaskId);
            if (childTask) {
              // Set the child workflow as active
              const context = {
                workflow: childWorkflow,
                type: key,
                index: i,
              };

              setActiveWorkflowContext(context);

              // Store initial context for toggle functionality
              if (!initialActiveContext) {
                setInitialActiveContext(context);
              }

              // Update selected workflow
              setSelectedWorkflow(childWorkflow);

              foundInChildWorkflow = true;
              break;
            }
          }
          if (foundInChildWorkflow) break;
        }
      }

      // If we didn't find the task in a child workflow, use the main workflow
      if (!foundInChildWorkflow) {
        // Set the main workflow as active by default
        if (allWorkflowData.workflows.length > 0) {
          const context = {
            workflow: allWorkflowData.workflows[0],
            type: 'main',
            index: 0,
          };

          setSelectedWorkflow(allWorkflowData.workflows[0]);
          setActiveWorkflowContext(context);

          // Store initial context for toggle functionality
          if (!initialActiveContext) {
            setInitialActiveContext(context);
          }
        }
      }

      // Store email info in state if available
      if (allWorkflowData.email_info) {
        setEmailInfo(allWorkflowData.email_info as unknown as EmailInfo);
      }

      // Store notice summary in state if available
      if (allWorkflowData.notice_summary) {
        setNoticeSummary(allWorkflowData.notice_summary as unknown as Record<string, number>);
      }

      // Store client_not_found status if available
      if (typeof allWorkflowData.client_not_found === 'boolean') {
        setClientNotFound(allWorkflowData.client_not_found);
      }
    } catch (err) {
      console.error('Error fetching workflow data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch workflow data');
    } finally {
      setLoading(false);
      setPageDataLoading(false);
      
    }
  }, [
    work_flow_id,
    router.query.taskId,
    initialTaskIdState,
    initialWorkflowId,
    initialActiveContext,
  ]);

  useEffect(() => {
    if (router.isReady && work_flow_id) {
      fetchWorkflowData();
    }
  }, [router.isReady, work_flow_id]); // Removed fetchWorkflowData to prevent continuous API calls

  // Effect to handle route changes and detect external navigation
  useEffect(() => {
    const handleRouteChangeStart = (url: string) => {
      // CRITICAL: Save current filter state before ANY navigation
      try {
        const currentUrl = window.location.href;
        const currentUrlObj = new URL(currentUrl);
        const currentWorkflowId = currentUrlObj.searchParams.get('work_flow_id');

        if (currentWorkflowId) {
          localStorage.setItem('currentWorkflowId', currentWorkflowId);
          localStorage.setItem('workflowFilters', JSON.stringify(filters));
          console.log('🔄 Saved filters before route change:', filters);
        }
      } catch (e) {
        console.error('❌ Failed to save filters before route change:', e);
      }

      // Check if this is navigation to a different page (not just task switching within workflow)
      const currentUrl = window.location.href;
      const newUrl = new URL(url, window.location.origin);
      const currentUrlObj = new URL(currentUrl);

      // If navigating to a different page (not just query params), mark as external
      if (newUrl.pathname !== currentUrlObj.pathname) {
        // Check if we already have a workflow navigation flag set (internal navigation)
        const hasWorkflowNavigation = sessionStorage.getItem('workflowNavigation') === 'true';

        // Only mark as external if we don't have an active workflow navigation session
        if (!hasWorkflowNavigation) {
          sessionStorage.setItem('isFromExternalRoute', 'true');
          sessionStorage.removeItem('workflowNavigation');
          console.log('🔄 External route navigation detected:', url);
        } else {
          console.log('🔄 Internal workflow navigation detected, preserving session:', url);
        }
      } else {
        // Same pathname, different query params - check if this is workflow navigation
        const currentWorkflowId = currentUrlObj.searchParams.get('work_flow_id');
        const newWorkflowId = newUrl.searchParams.get('work_flow_id');
        const currentTaskId = currentUrlObj.searchParams.get('taskId');
        const newTaskId = newUrl.searchParams.get('taskId');

        // Only mark as external if switching to different workflow
        if (currentWorkflowId !== newWorkflowId) {
          sessionStorage.setItem('isFromExternalRoute', 'true');
          sessionStorage.removeItem('workflowNavigation');
          console.log('🔄 Different workflow navigation detected:', url);
        } else if (currentWorkflowId === newWorkflowId && currentTaskId !== newTaskId) {
          // Same workflow, different task - this is ALWAYS internal navigation
          // Make sure we preserve the workflow navigation flag for reverse navigation
          sessionStorage.setItem('workflowNavigation', 'true');
          sessionStorage.removeItem('isFromExternalRoute');
          console.log('🔄 Same workflow task navigation detected (including reverse):', {
            from: currentTaskId,
            to: newTaskId,
            workflow: currentWorkflowId
          });
        }
      }
    };

    const handleBeforeUnload = () => {
      // On page refresh/close, save current filters and clear the workflow navigation flag
      try {
        if (typeof window !== 'undefined') {
          const urlParams = new URLSearchParams(window.location.search);
          const currentWorkflowId = urlParams.get('work_flow_id');
          if (currentWorkflowId) {
            localStorage.setItem('currentWorkflowId', currentWorkflowId);
            localStorage.setItem('workflowFilters', JSON.stringify(filters));
            console.log('🔄 Saved filters before unload:', filters);
          }
        }
      } catch (e) {
        console.error('❌ Failed to save filters before unload:', e);
      }

      sessionStorage.removeItem('workflowNavigation');
    };

    // Listen to Next.js router events
    router.events.on('routeChangeStart', handleRouteChangeStart);
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      router.events.off('routeChangeStart', handleRouteChangeStart);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [router.events, filters]); // Include filters in dependency array

  const generateInitials = (name: string): string => {
    const nameParts = name.trim().split(' ');
    if (nameParts.length >= 2) {
      return (nameParts[0][0] + nameParts[nameParts.length - 1][0]).toUpperCase();
    }
    return name[0]?.toUpperCase() || '';
  };

  const fetchAssignToOptions = useCallback(async () => {
    try {
      setAssignToLoading(true);
      const response = await apiClient.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/users-and-roles`
      );
      const { data } = response.data;

      //  @typescript-eslint/no-explicit-any
      const transformedUsers = data.combined.map((item: any) => {
        const isRole = item.type === 'role';
        const displayName = item.displayName || item.name;
        const showGroupIcon = item._id === '6894b0d7cbeb6e838b8e7ec6';

        return {
          label: displayName,
          profile_picture: showGroupIcon
            ? '/assets/ai-robot-new-2.svg'
            : isRole
              ? '/assets/Group-icon.svg'
              : null,
          value: item.id,
          text: displayName,
          id: item.id,
          _id: item._id,
          is_active: item.is_active,
          profileImage: showGroupIcon
            ? '/assets/ai-robot-new-2.svg'
            : isRole
              ? '/assets/Group-icon.svg'
              : null,
          initials: showGroupIcon ? null : generateInitials(displayName),
          type: item.type,
          category: item.category,
        };
      });
      setAssignToOptions(transformedUsers);
    } catch (error) {
      console.error('Error fetching assign to options:', error);
      setAssignToOptions([]);
    } finally {
      setAssignToLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchAssignToOptions();
  }, [fetchAssignToOptions]);

  const fetchAssignedUsers = useCallback(async () => {
    if (!selectedTask?.id) {
      return;
    }

    try {
      let workflowId: string;
      let taskExecutionId: string | undefined;

      workflowId = activeWorkflowContext?.workflow?.workflow_execution_id || '';

      if (activeWorkflowContext?.workflow?.workflow_status === 'COMPLETED') {
        taskExecutionId = selectedTask?.id;
      } else {
        taskExecutionId = activeWorkflowContext?.workflow?.tasks?.find(
          task => task.id === router.query?.taskId
        )?.id;
      }
      workflowId = activeWorkflowContext?.workflow?.workflow_execution_id || '';

      if (!taskExecutionId || !workflowId) {
        return;
      }

      const response = await apiClient.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/task-assigned-users`,
        {
          params: {
            task_execution_id: taskExecutionId,
            work_flow_id: workflowId,
          },
        }
      );

      if (response?.data?.statusCode === 200 && response?.data?.data) {
        const assignedUsers = response.data.data.assigned_users || [];

        const assignedUserIds = activeWorkflowContext?.type?.startsWith('child_workflow_')
          ? assignedUsers.map((user: any) => user.id)
          : assignedUsers.map((user: any) => user.id);

        setAssignToSelectedValues(assignedUserIds);
        setAssignedUsersData(assignedUsers);
        setTaskAssignments(prev => ({
          ...prev,
          [selectedTask.id]: assignedUserIds,
        }));
      }
    } catch (error) {
      console.error('Error fetching assigned users:', error);
    }
  }, [
    selectedTask?.id,
    activeWorkflowContext?.type,
    selectedWorkflow?.workflow_execution_id,
    work_flow_id,
    router.query.taskId,
  ]);

  useEffect(() => {
    if (selectedTask?.id) {
      fetchAssignedUsers();
    }
  }, [selectedTask?.id, fetchAssignedUsers]);
  const fetchAllTaskAssignments = useCallback(async (workflow: Workflow) => {
    if (!workflow?.workflow_execution_id || !workflow?.tasks) {
      return;
    }

    try {
      const workflowId = workflow.workflow_execution_id;
      const allAssignments: Record<string, string[]> = {};

      // Fetch assignments for each task in the workflow
      for (const task of workflow.tasks) {
        try {
          let taskExecutionId: string;

          if (workflow.workflow_status === 'COMPLETED') {
            taskExecutionId = task.id;
          } else {
            taskExecutionId = task.id;
          }

          const response = await apiClient.get(
            `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/task-assigned-users`,
            {
              params: {
                task_execution_id: taskExecutionId,
                work_flow_id: workflowId,
              },
            }
          );

          if (response?.data?.statusCode === 200 && response?.data?.data) {
            const assignedUsers = response.data.data.assigned_users || [];

            const assignedUserIds = assignedUsers.map((user: any) => user.id);

            allAssignments[task.id] = assignedUserIds;
          }
        } catch (error) {
          console.error(`Error fetching assignments for task ${task.id}:`, error);
          allAssignments[task.id] = [];
        }
      }

      // Update the taskAssignments state with all fetched assignments
      setTaskAssignments(prev => ({
        ...prev,
        ...allAssignments,
      }));
    } catch (error) {
      console.error('Error fetching all task assignments:', error);
    }
  }, []);

  const fetchAllWorkflowAssignments = useCallback(async () => {
    if (!workflowData) return;

    // Fetch assignments for main workflow
    if (workflowData.workflows) {
      for (const workflow of workflowData.workflows) {
        await fetchAllTaskAssignments(workflow);
      }
    }

    // Fetch assignments for child workflows
    if (workflowData.child_workflow_1) {
      for (const workflow of workflowData.child_workflow_1) {
        await fetchAllTaskAssignments(workflow);
      }
    }

    if (workflowData.child_workflow_2) {
      for (const workflow of workflowData.child_workflow_2) {
        await fetchAllTaskAssignments(workflow);
      }
    }
  }, [workflowData, fetchAllTaskAssignments]);

  // Fetch all task assignments when workflow data is loaded
  useEffect(() => {
    if (workflowData) {
      fetchAllWorkflowAssignments();
    }
  }, [workflowData, fetchAllWorkflowAssignments]);

  const assignUserToTask = async (userId: string, isdeSelected?: boolean) => {
    if (!selectedTask?.id || !selectedTask?.work_flow_id || !userId) {
      return;
    }

    const currentUserId = getUserFromLocalStorage()?.user_id;
    if (!currentUserId) {
      return;
    }

    try {
      const payload = {
        task_execution_id: selectedTask?.id,
        user_id: userId,
        work_flow_id: selectedTask?.work_flow_id,
        assigned_by: currentUserId,
        isdeSelected: isdeSelected,
      };

      const response = await apiClient.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/assign-user-to-task`,
        payload
      );

      if (response?.data?.data?.success) {
        // Fetch updated assigned users from API
        await fetchAssignedUsers();

        // Refresh the entire workflow data to get updated assignments
        await refreshWorkflowData();
      }
    } catch (error: any) {
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
    }
  };

  // Function to fetch assigned users for a specific task
  // const fetchAssignedUsers = useCallback(async () => {
  //   if (!selectedTask?.id) {
  //     console.log('No selectedTask.id, returning early');
  //     return;
  //   }

  //   try {
  //     let workflowId: string;
  //     let taskExecutionId: string | undefined;

  //     workflowId = activeWorkflowContext?.workflow?.workflow_execution_id || '';

  //     if (activeWorkflowContext?.workflow?.workflow_status === 'COMPLETED') {
  //       taskExecutionId = selectedTask?.id;
  //     } else {
  //       taskExecutionId = activeWorkflowContext?.workflow?.tasks?.find(
  //         task => task.id === router.query?.taskId
  //       )?.id;
  //     }
  //     workflowId = activeWorkflowContext?.workflow?.workflow_execution_id || '';

  //     if (!taskExecutionId || !workflowId) {
  //       console.log('Missing taskExecutionId or workflowId:', { taskExecutionId, workflowId });
  //       return;
  //     }

  //     const response = await axios.get(
  //       `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/task-assigned-users`,
  //       {
  //         params: {
  //           task_execution_id: taskExecutionId,
  //           work_flow_id: workflowId
  //         }
  //       }
  //     );

  //     if (response?.data?.statusCode === 200 && response?.data?.data) {
  //       const assignedUsers = response.data.data.assigned_users || [];

  //       const assignedUserIds = activeWorkflowContext?.type?.startsWith('child_workflow_')
  //         ? assignedUsers.map((user: any) => user.id)
  //         : assignedUsers.map((user: any) => user.id);

  //       setAssignToSelectedValues(assignedUserIds);
  //       setTaskAssignments(prev => ({
  //         ...prev,
  //         [selectedTask.id]: assignedUserIds,
  //       }));
  //     }
  //   } catch (error) {
  //     console.error('Error fetching assigned users:', error);
  //   }
  // }, [selectedTask?.id, activeWorkflowContext?.type, selectedWorkflow?.workflow_execution_id, work_flow_id, router.query.taskId]);

  // New function to fetch assignments for all tasks in a workflow
  // eslint-disable-next-line @typescript-eslint/no-unused-vars

  // Function to fetch assignments for all workflows

  // Function to remove user from task
  const removeUserFromTask = useCallback(
    async (userId: string, isdeSelected?: boolean) => {
      if (!selectedTask?.id || !selectedTask?.work_flow_id || !userId) {
        return;
      }

      try {
        const userFromStorage = getUserFromLocalStorage();
        if (!userFromStorage?.id) {
          console.error('No user found in localStorage');
          return;
        }

        const payload = {
          task_execution_id: selectedTask.id,
          user_id: userId,
          work_flow_id: selectedTask.work_flow_id,
          isdeSelected: isdeSelected,
        };

        const response = await apiClient.post(
          `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/remove-user-from-task`,
          payload
        );

        if (response?.data?.data?.success) {
          // Fetch updated assigned users from API
          await fetchAssignedUsers();

          // Refresh the entire workflow data to get updated assignments
          await refreshWorkflowData();
        }
      } catch (error: any) {
        console.error('Error removing user from task:', {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
        });
      }
    },
    [selectedTask?.id, selectedTask?.work_flow_id, fetchAssignedUsers, refreshWorkflowData]
  );

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!assignToDropdownOpen) return;

      const isClickInsideDropdown = assignToDropdownRef.current?.contains(event.target as Node);
      const isClickInsideTrigger = assignToTriggerRef.current?.contains(event.target as Node);

      // Close dropdown if click is outside both dropdown and trigger
      if (!isClickInsideDropdown && !isClickInsideTrigger) {
        setAssignToDropdownOpen(false);
        setAssignToSearchTerm('');
        setAssignToHighlightedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [assignToDropdownOpen]);

  useEffect(() => {
    if (workflowData && initialActiveContext && initialTaskIdState) {
      if (!showAllWorkflows) {
        setActiveWorkflowContext(initialActiveContext);
        setSelectedWorkflow(initialActiveContext.workflow);
        const initialTask = initialActiveContext.workflow.tasks.find(
          t => t.id === initialTaskIdState
        );
        if (initialTask) {
          selectTask(initialTask);
        }
      }
    }
  }, [showAllWorkflows, workflowData, initialActiveContext, initialTaskIdState]);

  // Monitor selected task validity when filters change
  useEffect(() => {
    if (selectedTask && workflowData) {
      // Check if the currently selected task is still visible with current filters
      const isTaskStillVisible = selectedTask.task_visible_status !== 'DRAFT' && shouldShowTask(selectedTask);
      
      if (!isTaskStillVisible) {
        console.log('🔍 Selected task no longer visible due to filters, clearing selection');
        setSelectedTask(null);
        setFormFields([]);
        setFormData({});
        
        // Check if there are any other visible tasks to select
        const hasAnyVisibleTasks = hasVisibleTasks();
        const hasActiveFilters = !filters.allTasks && 
          (filters.myTasks || filters.unassigned || filters.assigned_to_my_group);
        
        if (!hasAnyVisibleTasks && hasActiveFilters) {
          setIsEmptyDueToFilters(true);
        }
      }
    }
  }, [filters, selectedTask, workflowData, shouldShowTask, hasVisibleTasks]);

  // When workflow changes or task ID from URL changes, select the appropriate task
  useEffect(() => {
    if (selectedWorkflow) {
      // Skip task selection if we already have a selected task that matches the criteria
      // This helps prevent an infinite update loop
      const currentTaskId = selectedTask?.id;

      // Priority 1: Use initialTaskId if provided (from server-side props)
      if (initialTaskIdState && initialTaskIdState !== currentTaskId) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const task = selectedWorkflow.tasks.find((t: any) => t.id === initialTaskIdState);
        if (task) {
          selectTask(task);
          return;
        }
      }

      // Priority 2: Use taskId from query params if available
      if (taskId && typeof taskId === 'string' && taskId !== currentTaskId) {
        // First check if this taskId belongs to a child workflow
        if (workflowData) {
          // Search all child workflows for a matching task first
          for (const key in workflowData) {
            if (key.startsWith('child_workflow_') && workflowData[key]) {
              const childWorkflows = workflowData[key] as Workflow[];
              for (let i = 0; i < childWorkflows.length; i++) {
                const childWorkflow = childWorkflows[i];
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const childTask = childWorkflow.tasks.find((t: any) => t.id === taskId);
                if (childTask) {
                  // Set the child workflow as active FIRST
                  setActiveWorkflowContext({
                    workflow: childWorkflow,
                    type: key,
                    index: i,
                  });

                  // Update selected workflow
                  setSelectedWorkflow(childWorkflow);

                  // Select the task
                  selectTask(childTask);
                  return;
                }
              }
            }
          }
        }

        // Then try to find in current workflow
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const task = selectedWorkflow.tasks.find((t: any) => t.id === taskId);
        if (task) {
          // If this is the main workflow, explicitly set to main
          if (workflowData?.workflows?.includes(selectedWorkflow)) {
            setActiveWorkflowContext({
              workflow: selectedWorkflow,
              type: 'main',
              index: 0,
            });
          }
          selectTask(task);
          return;
        }
      }

      // Priority 3: If this is a child workflow, select the first task that matches filters
      if (
        activeWorkflowContext?.type.startsWith('child_workflow_') &&
        !currentTaskId &&
        selectedWorkflow.tasks.length > 0
      ) {
        // Check if there are any visible tasks that match the current filters
        const visibleTasks = selectedWorkflow.tasks.filter(task =>
          task.task_visible_status !== 'DRAFT' && shouldShowTask(task)
        );

        // Only select the first task if there are visible tasks that match filters
        if (visibleTasks.length > 0) {
          const firstVisibleTask = visibleTasks[0];
          selectTask(firstVisibleTask);
        } else {
          // No tasks match filters, clear the selected task to show blank screen
          setSelectedTask(null);
          setFormFields([]);
          setFormData({});
          console.log('🔍 No child workflow tasks match current filters, showing empty state');
        }
      }

      // Priority 4: Select only the first task initially for main workflow, but only if it matches filters
      if (!currentTaskId && selectedWorkflow.tasks.length > 0) {
        // Check if there are any visible tasks that match the current filters
        const visibleTasks = selectedWorkflow.tasks.filter(task =>
          task.task_visible_status !== 'DRAFT' && shouldShowTask(task)
        );
        
        console.log(`🔍 Task selection: Found ${visibleTasks.length} visible tasks out of ${selectedWorkflow.tasks.length} total tasks`);
        
        // Only select the first task if there are visible tasks that match filters
        if (visibleTasks.length > 0) {
          const firstVisibleTask = visibleTasks[0];
          console.log(`✅ Selecting first visible task: "${firstVisibleTask.name}"`);
          setIsEmptyDueToFilters(false); // Reset flag when task is found
          selectTask(firstVisibleTask);
          setInitialFilterCheckComplete(true); // Mark filter check as complete
        } else {
          // No tasks match filters, clear the selected task to show blank screen
          console.log(`❌ NO TASKS MATCH FILTERS - Clearing selected task to show "No record found"`);
          setSelectedTask(null);
          setFormFields([]);
          setFormData({});
          setIsEmptyDueToFilters(true); // Mark as empty due to filters
          setInitialFilterCheckComplete(true); // Mark filter check as complete
          console.log('🔍 No tasks match current filters, showing empty state');
        }
      }
    }
  }, [
    selectedWorkflow,
    taskId,
    initialTaskIdState,
    activeWorkflowContext,
    workflowData,
    showAllWorkflows,
    // Removed filters from dependency array to prevent infinite loops
  ]);

  // REMOVED: Effect to handle filter changes - was causing infinite loops
  // Filter-based task clearing is now handled in the applyFilters() function
  // This prevents continuous API calls by avoiding dependency on filters state

  // New useEffect to log reviewTask changes
  useEffect(() => {
    // When reviewTask changes, also see if there are any tasks in child workflows
    // that should be updated
    if (workflowData) {
      Object.keys(workflowData).forEach(key => {
        if (key.startsWith('child_workflow_') && workflowData[key]) {
          const childWorkflows = workflowData[key] as Workflow[];
          childWorkflows.forEach(workflow => {
            workflow.tasks.forEach(task => {
              if (reviewTask.includes(task.id)) {
                // This task should be marked as reviewed/completed
                task.status = 'completed';
              }
            });
          });
        }
      });
    }
  }, [reviewTask, workflowData]);

  // First, let's create a function to check form validity
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const checkFormValidity = (formData: Record<string, any>, task: Task): boolean => {
    // Check if any field has "no" selected
    const hasNoSelected = task.formFields.some(section => {
      return section.fields.some(field => {
        const value = formData[field._id];
        return value === 'no';
      });
    });

    // If "no" is selected, bypass all other validations
    if (hasNoSelected) {
      return true;
    }

    // Check if all required fields are filled
    const hasAllRequiredFields = task.formFields.every(section => {
      return section.fields.every(field => {
        // Skip validation if field is not required
        if (!field.required) return true;

        const value = formData[field._id];
        // Check if value exists and is not empty
        return value !== undefined && value !== null && value !== '';
      });
    });

    // Check if all checkboxes are checked
    const allCheckboxesChecked = checkAllCheckboxesSelected(formData);

    // Check if court notice events exist (if applicable)
    const hasCourtNoticeEvents = task.formFields.some(section =>
      section.fields.some(field => {
        if (field.type === 'court_notice') {
          const value = formData[field._id];
          return value && Array.isArray(value.events) && value.events.length > 0;
        }
        return true;
      })
    );

    // Return true only if all conditions are met
    return hasAllRequiredFields && allCheckboxesChecked && hasCourtNoticeEvents;
  };

  // Then modify the selectTask function to use this validation
  const selectTask = (task: Task) => {
    // If this task is already selected, do nothing to prevent an infinite loop
    if (selectedTask?.id === task.id) {
      return;
    }

    setSelectedTask(task);

    // Make sure we have form fields from the task
    if (task.formFields) {
      setFormFields(task.formFields);
    }

    // Reset validation state when switching tasks
    setFormHasErrors(false);
    setValidationErrors({});

    // Reset initial assignment flag for the new task
    setInitialAssignmentDone(false);

    // Fetch assigned users for this task from API
    if (task.id && task.work_flow_id) {
      fetchAssignedUsers();
    } else {
      // Clear assigned values if we don't have the required IDs
      setAssignToSelectedValues([]);
      setAssignedUsersData([]);
    }

    // Initialize form data with values from API
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const initialFormData: Record<string, any> = {};

    // Extract all field values from the task's form fields
    if (task.formFields) {
      task.formFields.forEach(section => {
        if (section.fields && section.fields.length > 0) {
          section.fields.forEach(field => {
            if (field.value !== undefined) {
              if (Array.isArray(field.value) && field.value.length > 0) {
                initialFormData[field._id] = (field.value[0] as { value: unknown }).value;
              } else {
                initialFormData[field._id] = field.value;
              }
            }
          });
        }
      });
    }

    // Set the form data with these initial values
    setFormData(initialFormData);

    // Check for initial assignment and call API if needed
    if (!initialAssignmentDone && task.id) {
      // Check if there's an assigned user in "Assign this workflow to" field
      const assignWorkflowField = task.formFields
        ?.find(section => section.fields?.some(field => field.label === 'Assign this workflow to'))
        ?.fields?.find(field => field.label === 'Assign this workflow to');

      if (assignWorkflowField && assignWorkflowField.value) {
        let assignedUserId: string | undefined;

        // Handle array format if necessary
        if (Array.isArray(assignWorkflowField.value) && assignWorkflowField.value.length > 0) {
          const firstValue = assignWorkflowField.value[0];
          if (typeof firstValue === 'object' && firstValue !== null && 'value' in firstValue) {
            assignedUserId = String((firstValue as { value: unknown }).value);
          }
        } else if (typeof assignWorkflowField.value === 'string') {
          assignedUserId = assignWorkflowField.value;
        }

        if (assignedUserId) {
          // Call API for initial assignment - Scenario 1: Page render with auto-assigned user (user._id, isdeselected:false)
          assignUserToTask(assignedUserId, true);
          setInitialAssignmentDone(true);
        }
      }
    }

    // Check form validity and set canComplete accordingly
    const isFormValid = checkFormValidity(initialFormData, task);
    setCanComplete(isFormValid);

    // Update URL with task ID without triggering a full page reload
    if (router.query.taskId !== task.id) {
      // Ensure internal navigation flag is set before router.push
      if (typeof window !== 'undefined') {
        sessionStorage.setItem('workflowNavigation', 'true');
        sessionStorage.removeItem('isFromExternalRoute');
      }

      router.push(
        {
          pathname: router.pathname,
          query: { ...router.query, taskId: task.id },
        },
        undefined,
        { shallow: true }
      );
    }
  };

  // Function to update task status in the workflow
  const updateTaskStatus = (
    taskId: string,
    status: 'active' | 'pending' | 'completed' | 'skipped'
  ) => {
    if (!selectedWorkflow) return;

    // Create a deep copy of the workflow to update its tasks
    const updatedWorkflow = {
      ...selectedWorkflow,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      tasks: selectedWorkflow.tasks.map((task: any) => {
        if (task.id === taskId) {
          return { ...task, status };
        }
        return task;
      }),
    };

    setSelectedWorkflow(updatedWorkflow);
  };

  // Update the handleTaskClick function to maintain toggle state
  const handleTaskClick = (workflowType: string, workflowIndex: number, taskIndex: number) => {
    // Prevent clicking during loading/saving operations
    if (loading || saving) {
      return;
    }

    // Mark this as internal workflow navigation - CRITICAL for reverse navigation
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('workflowNavigation', 'true');
      sessionStorage.removeItem('isFromExternalRoute');

      // CRITICAL: Save current filter state before navigation to ensure it's available
      // during component reinitialization
      const urlParams = new URLSearchParams(window.location.search);
      const currentWorkflowId = urlParams.get('work_flow_id');
      if (currentWorkflowId) {
        localStorage.setItem('currentWorkflowId', currentWorkflowId);
        localStorage.setItem('workflowFilters', JSON.stringify(filters));
      }

      console.log('🔄 Task click navigation - setting internal flags and saving filters for:', {
        workflowType,
        workflowIndex,
        taskIndex,
        filters
      });
    }

    // Use current state instead of localStorage to avoid conflicts

    let targetWorkflow: Workflow | undefined;
    let targetTask: Task | undefined;

    if (workflowType === 'main' && workflowData?.workflows) {
      // Handle main workflow
      targetWorkflow = workflowData.workflows[workflowIndex];
      if (targetWorkflow && targetWorkflow.tasks && targetWorkflow.tasks.length > taskIndex) {
        targetTask = targetWorkflow.tasks[taskIndex];
      }
    } else if (workflowData) {
      // Handle child workflow
      const childWorkflows = workflowData[workflowType] as Workflow[] | undefined;
      if (childWorkflows && childWorkflows.length > workflowIndex) {
        targetWorkflow = childWorkflows[workflowIndex];
        if (targetWorkflow && targetWorkflow.tasks && targetWorkflow.tasks.length > taskIndex) {
          targetTask = targetWorkflow.tasks[taskIndex];
        }
      }
    }

    if (targetWorkflow && targetTask) {
      // If we're already on this task, don't re-select it
      if (selectedTask?.id === targetTask.id) {
        return;
      }

      // Important: Update the active workflow context FIRST before setting selected workflow
      // Update the active workflow context
      setActiveWorkflowContext({
        workflow: targetWorkflow,
        type: workflowType,
        index: workflowIndex,
      });

      // Update the selected workflow
      setSelectedWorkflow(targetWorkflow);

      // Mark the task as active if it's not already completed or skipped
      if (targetTask.status !== 'completed' && targetTask.status !== 'skipped') {
        updateTaskStatus(targetTask.id, 'active');
      }

      // Reset form state when changing tasks
      setFormHasErrors(false);
      setValidationErrors({});

      // Since we're changing tasks manually, reset any save state
      setSaveSuccess(false);
      setSaveError(null);

      // Select the task - make sure this happens AFTER setting workflow context
      selectTask(targetTask);

      // Ensure internal navigation flag is set before router.push
      if (typeof window !== 'undefined') {
        sessionStorage.setItem('workflowNavigation', 'true');
        sessionStorage.removeItem('isFromExternalRoute');
      }

      // Update URL to reflect the selected task
      router.push(
        {
          pathname: router.pathname,
          query: { ...router.query, taskId: targetTask.id },
        },
        undefined,
        { shallow: true }
      );
    } else {
      console.error('Could not find target workflow or task', {
        workflowType,
        workflowIndex,
        taskIndex,
      });
    }
  };

  // Check if a condition is met based on the formData
  // eslint-disable-next-line
  const checkConditionMatch = (condition: Condition): boolean => {
    // Handle AND condition type
    if ('type' in condition && condition.type === 'AND') {
      return condition.conditions.every(c => checkConditionMatch(c));
    }

    // Handle OR condition type
    if ('type' in condition && condition.type === 'OR') {
      return condition.conditions.some(c => checkConditionMatch(c));
    }

    // Handle simple field/value condition
    if ('field' in condition && 'value' in condition) {
      return formData[condition.field] === condition.value;
    }

    return false;
  };

  // Get all visible fields based on current formData
  // const getVisibleFields = (): FormField[] => {
  //   if (!selectedTask) return [];

  //   return selectedTask.formFields
  //     .filter(section => !section.condition || checkConditionMatch(section.condition))
  //     .flatMap(section => section.fields);
  // };

  // Validate a specific field
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  // const validateField = (field: FormField, value: any): string => {
  //   // Check required fields
  //   if (field.required && (value === undefined || value === null || value === '')) {
  //     return 'This field is required';
  //   }

  //   // Skip validation if the field is empty and not required
  //   if (!field.required && (value === undefined || value === null || value === '')) {
  //     return '';
  //   }

  //   // Apply validation rules if they exist
  //   if (field.validation) {
  //     const validation = field.validation;

  //     // String length validation
  //     if (
  //       typeof value === 'string' &&
  //       validation.minLength &&
  //       value.length < validation.minLength
  //     ) {
  //       return `Minimum length is ${validation.minLength} characters`;
  //     }

  //     // Numeric range validation
  //     if (typeof value === 'number') {
  //       if (validation.min !== undefined && value < validation.min) {
  //         return `Minimum value is ${validation.min}`;
  //       }
  //       if (validation.max !== undefined && value > validation.max) {
  //         return `Maximum value is ${validation.max}`;
  //       }
  //     }

  //     // Pattern validation
  //     if (typeof value === 'string' && validation.pattern) {
  //       const regex = new RegExp(validation.pattern);
  //       if (!regex.test(value)) {
  //         return validation.errorMessage || 'Invalid format';
  //       }
  //     }
  //   }

  //   return '';
  // };

  // Validate all visible form fields
  // const validateForm = (): boolean => {
  //   const visibleFields = getVisibleFields();
  //   const errors: Record<string, string> = {};
  //   let hasErrors = false;

  //   visibleFields.forEach(field => {
  //     const fieldId = field._id;
  //     const value = formData[fieldId];
  //     const errorMessage = validateField(field, value);

  //     if (errorMessage) {
  //       errors[fieldId] = errorMessage;
  //       hasErrors = true;
  //     }
  //   });

  //   setValidationErrors(errors);
  //   setFormHasErrors(hasErrors);
  //   return !hasErrors;
  // };

  // Add a function to check if all checkboxes are checked
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const checkAllCheckboxesSelected = (data: Record<string, any>) => {
    // Get all form fields that are checkboxes
    const checkboxFields: string[] = [];

    if (selectedTask && selectedTask.formFields) {
      selectedTask.formFields.forEach(section => {
        if (section.fields) {
          section.fields.forEach(field => {
            if (field.type === 'checkbox') {
              checkboxFields.push(field._id);
            }
          });
        }
      });
    }

    // If there are no checkboxes, return true
    if (checkboxFields.length === 0) return true;

    // Check if all checkboxes are checked
    return checkboxFields.every(id => data[id] === true);
  };

  // Function to format workflow name based on clients
  const formatWorkflowName = useCallback((clients: string[]): string => {
    if (clients.length === 0) {
      return 'New Court notice';
    }

    if (clients.length === 1) {
      return clients[0];
    }

    if (clients.length === 2) {
      const name = `${clients[0]} & ${clients[1]}`;
      return name.length <= 60 ? name : truncateWithoutBreakingName(name, 60);
    }

    // More than 2 clients.
    const baseName = `${clients[0]}, ${clients[1]} & more…`;
    return baseName.length <= 60 ? baseName : truncateWithoutBreakingName(baseName, 60);
  }, []);

  // Function to truncate name without breaking mid-name
  const truncateWithoutBreakingName = useCallback((text: string, maxLength: number): string => {
    if (text.length <= maxLength) {
      return text;
    }

    // Find the last complete name within the limit
    const truncated = text.substring(0, maxLength - 1); // Leave space for ellipsis
    const lastSpaceIndex = truncated.lastIndexOf(' ');
    const lastCommaIndex = truncated.lastIndexOf(',');
    const lastAmpersandIndex = truncated.lastIndexOf('&');

    // Find the best breaking point (space, comma, or ampersand)
    const breakPoint = Math.max(lastSpaceIndex, lastCommaIndex, lastAmpersandIndex);

    if (breakPoint > 0) {
      return truncated.substring(0, breakPoint).trim() + '…';
    }

    // If no good breaking point, just truncate at character limit
    return truncated + '…';
  }, []);

  // Function to handle notice summary updates from AdvisorPanel
  const handleNoticeSummaryUpdate = useCallback(
    (taskId: string, clientName: string, count: number) => {
      setNoticeSummary(prev => {
        const updatedSummary = prev ? { ...prev } : {};

        if (!updatedSummary[taskId]) {
          updatedSummary[taskId] = {};
        }

        // Normalize client name to prevent whitespace duplicates
        const normalizedClientName = clientName.trim();

        // Find existing client with same normalized name (case sensitive)
        const existingClientKey = Object.keys(updatedSummary[taskId]).find(key =>
          key.trim() === normalizedClientName
        );

        // Update or remove the client based on count
        if (count > 0) {
          if (existingClientKey) {
            // Update existing client count (preserve original case)
            updatedSummary[taskId] = {
              ...updatedSummary[taskId],
              [existingClientKey]: count,
            };
          } else {
            // Add new client with normalized name
            updatedSummary[taskId] = {
              ...updatedSummary[taskId],
              [normalizedClientName]: count,
            };
          }
        } else {
          // Remove client if count is 0 (client deleted or no events)
          if (existingClientKey) {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { [existingClientKey]: removed, ...remainingClients } = updatedSummary[taskId];
            updatedSummary[taskId] = remainingClients;
            console.log(`🗑️ Removed client "${existingClientKey}" from notice summary (count: 0)`);
          }
        }

        // Update workflow name based on current clients
        const clients = Object.keys(updatedSummary[taskId]).filter(
          client => updatedSummary[taskId][client] > 0
        );

        const newWorkflowName = formatWorkflowName(clients);

        // Update workflow data with new name
        setWorkflowData(prevData => {
          if (!prevData || !prevData.workflows || prevData.workflows.length === 0) {
            return prevData;
          }

          return {
            ...prevData,
            workflows: [
              {
                ...prevData.workflows[0],
                name: newWorkflowName,
              },
              ...prevData.workflows.slice(1),
            ],
          };
        });
        console.log('Updated notice summary:', updatedSummary);

        return updatedSummary;
      });
    },
    [formatWorkflowName]
  );

  // Modify handleInputChange function to check all checkboxes
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleInputChange = (fieldId: string, value: any) => {
    // Check if this is a COURT_NOTICE field and update events count
    if (typeof value === 'object' && value !== null && Array.isArray(value.events)) {
      setCourtNoticeEventsCount(value.events.length);

      // If events array is empty, disable the button
      if (value.events.length === 0) {
        setCanComplete(false);
      }
    }

    setFormData(prev => {
      const newFormData = {
        ...prev,
        [fieldId]: value,
      };
      // Reset the specific field error on change
      if (validationErrors[fieldId]) {
        setValidationErrors(prevErrors => {
          const updatedErrors = { ...prevErrors };
          delete updatedErrors[fieldId];
          return updatedErrors;
        });
      }

      // Check form validity with the new data
      if (selectedTask) {
        const isFormValid = checkFormValidity(newFormData, selectedTask);
        setCanComplete(isFormValid);
      }

      return newFormData;
    });

    // Rest of the existing code...
    // Check if this field is used in any task's condition_task
    // Only update task selection when the field has dynamic_selected_task: true
    if (selectedWorkflow && selectedTask) {
      const fieldWithDynamicTask = selectedTask.formFields.find(
        section =>
          section.dynamic_selected_task === true &&
          section.fields.some(field => field._id === fieldId)
      );

      if (fieldWithDynamicTask) {
        const updatedWorkflow = {
          ...selectedWorkflow,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          tasks: selectedWorkflow.tasks.map((task: any) => {
            // Check if this task should be selected based on the condition_task
            const isSelected =
              task.condition_task &&
              task.condition_task.id === fieldId &&
              task.condition_task.value === value;

            return {
              ...task,
              selected_task: isSelected,
            };
          }),
        };
        setSelectedWorkflow(updatedWorkflow);
      }
    }

    // Reset save states when user makes changes
    setSaveSuccess(false);
    setSaveError(null);
  };

  // Function to save a single field to the API
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const saveFieldToAPI = async (payload: any) => {
    setSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      const response = await apiClient.put(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update`,
        payload
      );

      if (response.data.statusCode === 200) {
        setSaveSuccess(true);

        // Clear success message after 2 seconds
        setTimeout(() => setSaveSuccess(false), 2000);
      } else {
        throw new Error(response.data.message || 'Save failed');
      }
    } catch (error: any) {
      console.error('❌ Error saving field:', error);
      setSaveError(error.response?.data?.message || error.message || 'Failed to save field');

      // Clear error message after 5 seconds
      setTimeout(() => setSaveError(null), 5000);
    } finally {
      setSaving(false);
    }
  };

  // Function to call assign-user-to-task API
  // This API is used for both assigning and deselecting users
  // isDeselected parameter differentiates between "Assign To" dropdown vs "Assign this workflow to" dropdown

  // Get the appropriate icon for a task
  // const getTaskIcon = (iconName: string) => {
  //   switch (iconName) {
  //     case 'user':
  //       return <User size={16} className="text-blue-[#3F73F6]" />;
  //     case 'alert-triangle':
  //       return <AlertTriangle size={16} className="text-amber-500" />;
  //     case 'phone':
  //       return <Phone size={16} className="text-green-500" />;
  //     case 'mail':
  //       return <Mail size={16} className="text-indigo-500" />;
  //     default:
  //       return <User size={16} className="text-blue-[#3F73F6]" />;
  //   }
  // };

  // Determine if current task is the last one or not
  const isLastTask = () => {
    if (!selectedWorkflow || !selectedTask) return false;

    // Get the visible tasks (default_task or selected_task)
    const visibleTasks = selectedWorkflow.tasks.filter(
      task => task.default_task === true || task.selected_task === true
    );

    // Check if the current task is the last one in the visible tasks
    const currentIndex = visibleTasks.findIndex(task => task.id === selectedTask.id);
    return currentIndex === visibleTasks.length - 1;
  };

  // Handler for "Skip" or "Complete" or "Next" buttons
  const handleTaskAction = async (action: 'skip' | 'complete' | 'next') => {
    if (!selectedWorkflow || !selectedTask) return;

    // Prevent multiple API calls
    if (saving) return;
    setSaving(true);

    // Mark as internal navigation when moving between tasks
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('workflowNavigation', 'true');
      sessionStorage.removeItem('isFromExternalRoute');
    }

    // Store the current workflow context before we make any changes
    const currentWorkflowContext = activeWorkflowContext;

    // Always treat 'next' as 'complete' since we're renaming the button
    let nextTaskId = null;
    if (action === 'next') {
      // Call API to get next task info if needed
      const response = await apiClient.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/next-task?task_id=${selectedTask.id}&work_flow_id=${getWorkflowIdFromUrl()}`
      );

      // Extract next_task_id from the response if available
      if (response?.data?.data?.next_task_id) {
        nextTaskId = response.data.data.next_task_id;
      }
    }

    // For Complete action, validate form fields first
    if (action === 'complete') {
      localStorage.removeItem('isUpdated');
      // const isValid = validateForm();
      // if (!isValid) {
      //   // If validation fails, don't proceed
      //   setSaving(false);
      //   return;
      // }
      // Check if there's a "Confirmed" checkbox field that needs to be checked
      // const visibleFields = getVisibleFields();
      // const confirmedField = visibleFields.find(
      //   field =>
      //     field.type === 'checkbox' &&
      //     (field.label?.toLowerCase().includes('confirm') ||
      //       field.id.toLowerCase().includes('confirm'))
      // );
      // if (
      //   confirmedField &&
      //   (!formData[confirmedField._id] || formData[confirmedField._id] !== true)
      // ) {
      //   setValidationErrors(prev => ({
      //     ...prev,
      //     [confirmedField._id]: 'You must confirm before proceeding',
      //   }));
      //   setSaving(false);
      //   return; // Don't proceed if the confirmation checkbox isn't checked
      // }
    }

    // Get available tasks (those that are either default or selected)
    const availableTasks = selectedWorkflow.tasks.filter(
      t => t.default_task === true || t.selected_task === true
    );

    // Find the index of the current task in available tasks
    const currentTaskIndex = availableTasks.findIndex(t => t.id === selectedTask.id);
    const isLastTaskInWorkflow = currentTaskIndex === availableTasks.length - 1;

    try {
      // Handle task completion logic
      if (action === 'complete' || action === 'next') {
        // Mark task as completed in your data/API
        updateTaskStatus(selectedTask.id, 'completed');

        // Add the completed task to the reviewTask array
        const updatedReviewTasks = [...reviewTask];
        if (!updatedReviewTasks.includes(selectedTask.id)) {
          updatedReviewTasks.push(selectedTask.id);
          setReviewTask(updatedReviewTasks);

          // Store the updated review tasks in localStorage for persistence
          try {
            // localStorage.setItem('reviewTasks', JSON.stringify(updatedReviewTasks));
            if (typeof window !== 'undefined') {
              localStorage.setItem('reviewTasks', JSON.stringify(updatedReviewTasks));
            }
          } catch (e) {
            console.error('Failed to save review tasks to localStorage:', e);
          }
        }

        // Check if we're in a child workflow to determine the API payload
        const isChild = currentWorkflowContext?.type.startsWith('child_workflow_');

        if (action === 'complete') {
          // Update workflow status via API with isChild flag if needed
          await apiClient.put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update-status`, {
            workflow_id: selectedWorkflow?.tasks[0]?.work_flow_id,
            isChild: isChild, // Add isChild flag for API
          });

          setSelectedTask(prev => (prev ? { ...prev, task_visible_status: 'REVIEWED' } : prev));
        }

        // After updating status, re-fetch the workflow data to get fresh child workflows
        const updatedResponse = await apiClient.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-render`,
          {
            params: {
              work_flow_id: work_flow_id,
              userId: userId,
            },
          }
        );

        // Update workflow data with the refreshed data
        const allWorkflowData = updatedResponse?.data?.data as unknown as WorkflowData;
        setWorkflowData(allWorkflowData);

        // Extract child workflows
        const childWorkflowsObj: { [key: string]: Workflow[] } = {};
        Object.keys(allWorkflowData).forEach(key => {
          if (key.startsWith('child_workflow_') && allWorkflowData[key]) {
            childWorkflowsObj[key] = allWorkflowData[key] as Workflow[];
          }
        });
        setChildWorkflows(childWorkflowsObj);

        // Check if we're in a child workflow
        if (currentWorkflowContext?.type.startsWith('child_workflow_')) {
          const currentChildType = currentWorkflowContext.type;
          const currentChildNumberMatch = currentChildType.match(/child_workflow_(\d+)/);

          if (currentChildNumberMatch && currentChildNumberMatch[1]) {
            const currentChildNumber = parseInt(currentChildNumberMatch[1], 10);
            const nextChildNumber = currentChildNumber + 1;
            const nextChildType = `child_workflow_${nextChildNumber}`;

            // Check if the next child workflow exists
            if (childWorkflowsObj[nextChildType] && childWorkflowsObj[nextChildType].length > 0) {
              const currentIndex = currentWorkflowContext.index;

              // Get the next child workflow directly from the workflows object
              const nextChildWorkflows = childWorkflowsObj[nextChildType];
              if (nextChildWorkflows.length > currentIndex) {
                const nextChildWorkflow = nextChildWorkflows[currentIndex];

                // Create updated workflow with activated first task
                const updatedNextWorkflow = {
                  ...nextChildWorkflow,
                  tasks: nextChildWorkflow.tasks.map((task, index) =>
                    index === 0 ? { ...task, status: 'active' } : task
                  ),
                };

                // Update context and workflow state first
                setActiveWorkflowContext({
                  workflow: updatedNextWorkflow,
                  type: nextChildType,
                  index: currentIndex,
                });
                setSelectedWorkflow(updatedNextWorkflow);

                // Then select the first task
                const firstTask = updatedNextWorkflow.tasks[0];
                selectTask(firstTask);

                // Update URL immediately
                router.push(
                  {
                    pathname: router.pathname,
                    query: { ...router.query, taskId: firstTask.id },
                  },
                  undefined,
                  { shallow: true }
                );

                setSaving(false);
                return;
              }
            } else {
              router.push(`/all-work-flow?type=court-notice-follow-up`);
            }
          }
        }

        // If we're not in a child workflow or there's no next child workflow,
        // check if we have a next_task_id from the API
        if (nextTaskId) {
          // First, look for this task in the main workflow
          let nextTask = null;
          if (allWorkflowData.workflows && allWorkflowData.workflows.length > 0) {
            nextTask = allWorkflowData.workflows[0].tasks.find(task => task.id === nextTaskId);

            if (nextTask) {
              // Set main workflow as active
              setActiveWorkflowContext({
                workflow: allWorkflowData.workflows[0],
                type: 'main',
                index: 0,
              });

              // Update selected workflow
              setSelectedWorkflow(allWorkflowData.workflows[0]);

              // Select the task and mark it as active
              updateTaskStatus(nextTask.id, 'active');
              selectTask(nextTask);

              // Ensure internal navigation flag is set before router.push
              if (typeof window !== 'undefined') {
                sessionStorage.setItem('workflowNavigation', 'true');
                sessionStorage.removeItem('isFromExternalRoute');
              }

              // Update URL to reflect the next task
              router.push(
                {
                  pathname: router.pathname,
                  query: { ...router.query, taskId: nextTask.id },
                },
                undefined,
                { shallow: true }
              );

              // Stop further processing as we've selected the next task from API
              setSaving(false);
              return;
            }
          }

          // If not found in main workflow, check in all child workflows
          if (!nextTask) {
            for (const key in childWorkflowsObj) {
              const childWorkflows = childWorkflowsObj[key];
              for (let i = 0; i < childWorkflows.length; i++) {
                const childWorkflow = childWorkflows[i];
                nextTask = childWorkflow.tasks.find(task => task.id === nextTaskId);

                if (nextTask) {
                  // Set the child workflow as active
                  setActiveWorkflowContext({
                    workflow: childWorkflow,
                    type: key,
                    index: i,
                  });

                  // Update selected workflow
                  setSelectedWorkflow(childWorkflow);

                  // Select the task and mark it as active
                  updateTaskStatus(nextTask.id, 'active');
                  selectTask(nextTask);

                  // Ensure internal navigation flag is set before router.push
                  if (typeof window !== 'undefined') {
                    sessionStorage.setItem('workflowNavigation', 'true');
                    sessionStorage.removeItem('isFromExternalRoute');
                  }

                  // Update URL to reflect the child workflow task
                  router.push(
                    {
                      pathname: router.pathname,
                      query: { ...router.query, taskId: nextTask.id },
                    },
                    undefined,
                    { shallow: true }
                  );

                  // Stop further processing as we've selected the next task from API
                  setSaving(false);
                  return;
                }
              }
            }
          }
        }

        // If we don't have a next_task_id or couldn't find it, try the next task in sequence
        if (currentTaskIndex >= 0 && currentTaskIndex < availableTasks.length - 1) {
          // If there's a next task in the current workflow, use that
          const nextTask = availableTasks[currentTaskIndex + 1];

          // Keep the current workflow context
          // No need to update activeWorkflowContext - we're in the same workflow

          // Update the next task to active status before selecting it
          updateTaskStatus(nextTask.id, 'active');
          selectTask(nextTask);
          setSaving(false);
          return;
        }

        // If we get here, check for child workflows only if we've completed all tasks in the main workflow
        if (isLastTaskInWorkflow && currentWorkflowContext?.type === 'main') {
          // Find the first child workflow (should be child_workflow_1)
          const childWorkflowKeys = Object.keys(childWorkflowsObj).sort();
          if (childWorkflowKeys.length > 0) {
            const firstChildKey = childWorkflowKeys[0];
            const firstChildWorkflows = childWorkflowsObj[firstChildKey];

            if (firstChildWorkflows && firstChildWorkflows.length > 0) {
              const firstChildWorkflow = firstChildWorkflows[0];

              if (
                firstChildWorkflow &&
                firstChildWorkflow.tasks &&
                firstChildWorkflow.tasks.length > 0
              ) {
                // Get the first task in the child workflow
                const firstTask = firstChildWorkflow.tasks[0];

                // Set the child workflow as active
                setActiveWorkflowContext({
                  workflow: firstChildWorkflow,
                  type: firstChildKey,
                  index: 0,
                });

                // Update selected workflow
                setSelectedWorkflow(firstChildWorkflow);

                // Select the first task and mark it as active
                updateTaskStatus(firstTask.id, 'active');
                selectTask(firstTask);

                // Update URL to reflect the child workflow task
                router.push(
                  {
                    pathname: router.pathname,
                    query: { ...router.query, taskId: firstTask.id },
                  },
                  undefined,
                  { shallow: true }
                );

                setSaving(false);
                return;
              }
            }
          }
        }
      } else if (action === 'skip') {
        // Mark task as skipped in your data/API

        updateTaskStatus(selectedTask.id, 'skipped');

        // If there's a next task, select it and mark it as active
        if (currentTaskIndex >= 0 && currentTaskIndex < availableTasks.length - 1) {
          const nextTask = availableTasks[currentTaskIndex + 1];

          // Update the next task to active status before selecting it
          updateTaskStatus(nextTask.id, 'active');
          selectTask(nextTask);
        }
      }
    } catch (error) {
      console.error('Error in handleTaskAction:', error);
      // Handle errors appropriately
    } finally {
      setSaving(false);
    }
  };

  const handleCompleteRunTask = async () => {
    if (!selectedWorkflow || !selectedTask) return;

    // Prevent multiple API calls
    if (saving) return;
    setSaving(true);

    try {
      // Mark task as completed
      updateTaskStatus(selectedTask.id, 'completed');

      // Add the completed task to the reviewTask array
      const updatedReviewTasks = [...reviewTask];
      if (!updatedReviewTasks.includes(selectedTask.id)) {
        updatedReviewTasks.push(selectedTask.id);
        setReviewTask(updatedReviewTasks);

        // Store the updated review tasks in localStorage for persistence
        try {
          if (typeof window !== 'undefined') {
            localStorage.setItem('reviewTasks', JSON.stringify(updatedReviewTasks));
          }
        } catch (e) {
          console.error('Failed to save review tasks to localStorage:', e);
        }
      }

      // // Update workflow status via API
      // await axios.put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-update-status`, {
      //   workflow_id: selectedWorkflow?.tasks[0]?.work_flow_id,
      //   isChild: false, // Not triggering child workflows
      // });

      // Re-fetch the workflow data to get fresh data
      const updatedResponse = await apiClient.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-render`,
        {
          params: {
            work_flow_id: work_flow_id,
            userId: userId,
          },
        }
      );

      // Update workflow data with the refreshed data
      const allWorkflowData = updatedResponse?.data?.data as unknown as WorkflowData;
      setWorkflowData(allWorkflowData);

      // Extract child workflows for state consistency
      const childWorkflowsObj: { [key: string]: Workflow[] } = {};
      Object.keys(allWorkflowData).forEach(key => {
        if (key.startsWith('child_workflow_') && allWorkflowData[key]) {
          childWorkflowsObj[key] = allWorkflowData[key] as Workflow[];
        }
      });
      setChildWorkflows(childWorkflowsObj);

      // Update the current workflow with fresh data while staying on the same task
      if (
        activeWorkflowContext?.type === 'main' &&
        allWorkflowData.workflows &&
        allWorkflowData.workflows.length > 0
      ) {
        const updatedMainWorkflow = allWorkflowData.workflows[0];
        setSelectedWorkflow(updatedMainWorkflow);

        // Update the active workflow context with fresh data
        setActiveWorkflowContext({
          workflow: updatedMainWorkflow,
          type: 'main',
          index: 0,
        });

        // Find and update the current task with fresh data
        const updatedCurrentTask = updatedMainWorkflow.tasks.find(
          task => task.id === selectedTask.id
        );
        if (updatedCurrentTask) {
          selectTask(updatedCurrentTask);
        }
      } else if (activeWorkflowContext?.type.startsWith('child_workflow_')) {
        // Handle child workflow updates
        const currentChildType = activeWorkflowContext.type;
        const currentIndex = activeWorkflowContext.index;

        if (
          childWorkflowsObj[currentChildType] &&
          childWorkflowsObj[currentChildType][currentIndex]
        ) {
          const updatedChildWorkflow = childWorkflowsObj[currentChildType][currentIndex];
          setSelectedWorkflow(updatedChildWorkflow);

          // Update the active workflow context with fresh data
          setActiveWorkflowContext({
            workflow: updatedChildWorkflow,
            type: currentChildType,
            index: currentIndex,
          });

          // Find and update the current task with fresh data
          const updatedCurrentTask = updatedChildWorkflow.tasks.find(
            task => task.id === selectedTask.id
          );
          if (updatedCurrentTask) {
            selectTask(updatedCurrentTask);
          }
        }
      }
    } catch (error) {
      console.error('Error in handleCompleteTask:', error);
      // Handle errors appropriately - you might want to show a toast or error message
    } finally {
      setSaving(false);
    }
  };

  const handleActiveRunTask = async () => {
    if (!selectedWorkflow || !selectedTask) return;

    // Prevent multiple API calls
    if (saving) return;
    setSaving(true);

    try {
      // Re-fetch the workflow data to get fresh data
      const updatedResponse = await apiClient.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-render`,
        {
          params: {
            work_flow_id: work_flow_id,
            userId: userId,
          },
        }
      );

      // Update workflow data with the refreshed data
      const allWorkflowData = updatedResponse?.data?.data as unknown as WorkflowData;
      setWorkflowData(allWorkflowData);
    } catch (error) {
      console.error('Error in handleCompleteTask:', error);
      // Handle errors appropriately - you might want to show a toast or error message
    } finally {
      setSaving(false);
    }
  };

  // const handleCreateTaskForWorkflow = (workflowType: string) => {
  //   // For demo purposes, we will just alert
  //   alert(
  //     `This would create a new task in the ${workflowType === 'main' ? 'main workflow' : getChildWorkflowTitle(workflowType)}. In a real implementation, this would open a form to define a new task.`
  //   );
  // };

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setShowFilter(false);
      }
    }

    if (showFilter) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showFilter]);

  // Load saved reviewTask from localStorage on component mount
  useEffect(() => {
    try {
      if (typeof window !== 'undefined') {
        const savedReviewTasks = localStorage.getItem('reviewTasks');
        if (savedReviewTasks) {
          const parsedReviewTasks = JSON.parse(savedReviewTasks);
          if (Array.isArray(parsedReviewTasks)) {
            setReviewTask(parsedReviewTasks);
          }
        }
      }
    } catch (e) {
      console.error('Failed to load review tasks from localStorage:', e);
    }
  }, []);

  useEffect(() => {
    if (showNotification) {
      // const timer = setTimeout(() => {
      //   setShowNotification(false);
      // }, 5000); // Hide after 5 seconds
      // return () => clearTimeout(timer);
    }
  }, [showNotification]);

  // Show loading state
  if (loading && !initialFilterCheckComplete) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3F73F6] mx-auto mb-4"></div>
          <p>Loading workflow...</p>
        </div>
      </div>
    );
  }

  // Show loading state for filters - same as initial load but with different message
  if ((pageDataLoading || filtersLoading) && !initialFilterCheckComplete) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3F73F6] mx-auto mb-4"></div>
          <p>{pageDataLoading ? 'Loading workflow data...' : 'Applying filters...'}</p>
        </div>
      </div>
    );
  }

  // Show access check loading state
  // if (accessCheckLoading) {
  //   return (
  //     <div className="flex items-center justify-center h-screen">
  //       <div className="text-center">
  //         <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3F73F6] mx-auto mb-4"></div>
  //         <p>Checking access permissions...</p>
  //       </div>
  //     </div>
  //   );
  // }
  // if (userHasAccess === false && !accessCheckLoading) {
  //   return (
  //     <div className="flex items-center justify-center h-screen">
  //       <div className="text-center">
  //         <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
  //           <svg
  //             className="w-8 h-8 text-red-600"
  //             fill="none"
  //             stroke="currentColor"
  //             viewBox="0 0 24 24"
  //           >
  //             <path
  //               strokeLinecap="round"
  //               strokeLinejoin="round"
  //               strokeWidth={2}
  //               d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
  //             />
  //           </svg>
  //         </div>
  //         <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
  //         <p className="text-gray-600 mb-4">You don&rsquo;t have permission to access this task</p>
  //         <button
  //           onClick={() => router.push('/court-notice/new')}
  //           className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
  //         >
  //           Go Back
  //         </button>
  //       </div>
  //     </div>
  //   );
  // }

  // Show error state
  if (error) {
    return (
      <div className="flex   items-center justify-center h-screen">
        <div className="text-center p-6 bg-red-50 rounded-[12px]">
          <AlertTriangle size={24} className="text-red-500 mx-auto mb-2" />
          <h2 className="text-lg font-medium text-red-700 mb-2">Error Loading Workflow</h2>
          <p className="text-red-600">{error}</p>
          <button
            className="mt-4 px-4 py-2 cursor-pointer bg-[#3F73F6] text-white rounded-[12px]"
            onClick={() => router.reload()}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // const handleCreateTask = () => {
  //   // This would normally call an API to create a new task
  //   console.log('Creating a new task');

  //   // For demo purposes, we will just alert
  //   alert(
  //     'This would create a new task. In a real implementation, this would open a form to define a new task.'
  //   );
  // };

  // Helper function to get a readable name for child workflow sections
  const getChildWorkflowTitle = (key: string): string => {
    // Extract the number for more dynamic labeling

    switch (key) {
      case 'child_workflow_1':
        return 'Court Notice Follow Up';
      case 'child_workflow_2':
        return 'Court Notice Follow Up';
      default:
        return 'Court Notice Follow Up';
    }
  };

  const handleToggleChange = () => {
    const newState = !showAllWorkflows;
    setShowAllWorkflows(newState);

    // The useEffect will handle the workflow selection logic
    // No need to manually set context here to avoid state conflicts
  };

  const handleSidebarIconClick = (index: number, iconPath: string) => {
    setActiveSidebarIcon(index);

    // Get current window width directly for more reliable check
    const currentWidth = typeof window !== 'undefined' ? window.innerWidth : 0;
    const isUnder1440px = currentWidth < 1440;

    // Debug logging
    console.log('🔍 handleSidebarIconClick - screenSize:', screenSize, 'index:', index, 'window.innerWidth:', currentWidth, 'isUnder1440px:', isUnder1440px);

    // For non-first icons (not advisor panel), mark as potential external navigation
    if (index !== 0 && typeof window !== 'undefined') {
      sessionStorage.setItem('isFromExternalRoute', 'true');
      sessionStorage.removeItem('workflowNavigation');
      console.log('🔄 Sidebar icon click - marked as external navigation for index:', index);
    }

    // First icon (index 0) toggles the right panel overlay on screens under 1440px
    if (index === 0 && isUnder1440px) {
      console.log('🔍 Toggling overlay - screen is under 1440px:', currentWidth);
      setIsRightPanelOverlayOpen(!isRightPanelOverlayOpen);
      return;
    }

    if (index === 0) {
      console.log('🔍 Not toggling overlay - screen is 1440px or above:', currentWidth);
    }

    if (iconPath.includes('dots-horizontal-two-toned')) {
      setActivePanel('details');
    } else {
      setActivePanel('advisor');
    }
  };



  // Calculate panel widths based on fixed right panel sizes
  // const getRightPanelWidth = () => {
  //   const width = getResponsiveRightPanelWidth();
  //   return `${width}px`;
  // };

  // // Calculate center panel width based on available space
  // const getCenterPanelWidth = () => {
  //   const totalWidth = window.innerWidth;
  //   const leftWidth = leftPanelWidth;
  //   const rightWidth = activePanel === 'advisor' ? 440 : 260;
  //   const sidebarWidth = 64;
  //   const availableWidth = totalWidth - leftWidth - rightWidth - sidebarWidth - 64;
  //   return `${availableWidth}px`;
  // };

  // Add handleArchiveChange function after the handleToggleChange function
  const handleArchiveChange = (archiveInfo: {
    is_archive: boolean;
    archive_by: string;
    archive_at: string;
  }) => {
    if (selectedWorkflow) {
      // Update the selected workflow with new archive status and info
      const updatedWorkflow = {
        ...selectedWorkflow,
        is_archive: archiveInfo.is_archive,
        archive_by: archiveInfo.archive_by,
        archive_at: archiveInfo.archive_at,
      };
      setSelectedWorkflow(updatedWorkflow);

      // Also update the workflow in the main data structure
      if (workflowData) {
        const updatedWorkflowData = {
          ...workflowData,
          workflows: workflowData.workflows.map(w =>
            w.id === selectedWorkflow.id ? updatedWorkflow : w
          ),
        };
        setWorkflowData(updatedWorkflowData);
      }
    }
  };

  console.log(activeWorkflowContext?.workflow?.workflow_execution_id, 'activeWorkflowContext?.workflow?.workflow_execution_id');

  // Add handleStatusChange function after handleArchiveChange
  const handleStatusChange = (status: string) => {
    if (selectedWorkflow) {
      // Update the selected workflow with new status
      const updatedWorkflow = {
        ...selectedWorkflow,
        workflow_status: status,
      };
      setSelectedWorkflow(updatedWorkflow);

      // Also update the workflow in the main data structure
      if (workflowData) {
        const updatedWorkflowData = {
          ...workflowData,
          workflows: workflowData.workflows.map(w =>
            w.id === selectedWorkflow.id ? updatedWorkflow : w
          ),
        };
        setWorkflowData(updatedWorkflowData);
      }
    }
  };

  // Add a function to check if the last task is active
  const isLastTaskActive = (): boolean => {
    if (!selectedWorkflow) return false;

    // Get the visible tasks (default_task or selected_task)
    const visibleTasks = selectedWorkflow.tasks.filter(
      task => task.default_task === true || task.selected_task === true
    );

    if (visibleTasks.length === 0) return false;

    // Get the last task
    const lastTask = visibleTasks[visibleTasks.length - 1];

    // Check if the last task is active
    return lastTask.id === selectedTask?.id;
  };

  // Responsive panel width calculation functions
  // const getResponsiveRightPanelWidthString = (): string => {
  //   const width = getResponsiveRightPanelWidth();
  //   return `${width}px`;
  // };

  // const getResponsiveCenterPanelWidth = (): string => {
  //   if (typeof window === 'undefined') return '50%';

  //   const totalWidth = window.innerWidth;
  //   const leftWidth = getResponsiveLeftPanelWidth();
  //   const rightWidth = getResponsiveRightPanelWidth();
  //   const sidebarWidth = getSidebarWidth();
  //   const padding = 16; // Account for padding/margins

  //   const availableWidth = totalWidth - leftWidth - rightWidth - sidebarWidth - padding;
  //   const minWidth = Math.max(320, availableWidth);

  //   return `${minWidth}px`;
  // };

  // Mobile panel management functions
  const toggleLeftPanel = () => {
    setIsLeftPanelCollapsed(!isLeftPanelCollapsed);
    setLeftPanelWidth(isLeftPanelCollapsed ? getResponsiveLeftPanelWidth() : 0);
  };

  // Mobile responsive panel overlay management
  const toggleRightPanelMobile = () => {
    if (isMobileView) {
      // On mobile, we can toggle the right panel visibility
      setActivePanel(activePanel === 'advisor' ? 'details' : 'advisor');
    }
  };

  const handleAssignToOptionSelect = (option: { type: any; value: string; text: string }) => {
    const isSelected = assignToSelectedValues.includes(option.value);

    if (isSelected) {
      // User is being deselected - use remove API
      if (option.value) {
        removeUserFromTask(option.value);
      }
    } else {
      // User is being selected
      // Scenario 3: API call in WorkflowRun - user being selected (user.id, isdeselected:false)
      if (option.value) {
        assignUserToTask(option.value, true);
      }
    }
  };

  const handleAssignToKeyDown = (e: React.KeyboardEvent) => {
    if (!assignToDropdownOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setAssignToHighlightedIndex(prev => (prev < assignToOptions.length - 1 ? prev + 1 : prev));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setAssignToHighlightedIndex(prev => (prev > -1 ? prev - 1 : prev));
        break;
      case 'Enter':
        e.preventDefault();
        if (assignToHighlightedIndex >= 0 && assignToHighlightedIndex < assignToOptions.length) {
          handleAssignToOptionSelect({
            type: assignToOptions[assignToHighlightedIndex].type,
            value: assignToOptions[assignToHighlightedIndex].value,
            text: assignToOptions[assignToHighlightedIndex].text,
          });
        }
        break;
      case 'Escape':
        e.preventDefault();
        setAssignToDropdownOpen(false);
        break;
    }
  };

  // Function to refresh assignments for a specific task

  // Clear cache when task changes

    return (
      <div ref={containerRef} className="flex w-full h-full bg-[#F3F5F9] relative">
        {/* Full-screen overlay during filter application */}
        {filtersLoading && (
          <div className="absolute inset-0 z-[9999] bg-white flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3F73F6] mx-auto mb-4"></div>
              <p>Loading workflow...</p>
            </div>
          </div>
        )}
      {/* Mobile overlay for left panel */}
      {isMobileView && !isLeftPanelCollapsed && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-10 lg:hidden"
          onClick={toggleLeftPanel}
        />
      )}

      {/* Left sidebar - enhanced responsive design */}
      <div
        ref={leftPanelRef}
        className={`
          border-r border-[#DCE2EB] bg-white h-full flex flex-col overflow-hidden transition-all duration-300 z-20
          ${isLeftPanelCollapsed ? 'w-0 -ml-1' : ''}
          ${isMobileView ? 'fixed left-0 top-0 ' : ''}
          
        `}
        style={{
          flexBasis: window.innerWidth >= 2560 ? '758px' : window.innerWidth >= 2028 ? `${dynamicLeftPanelWidth}px` : window.innerWidth >= 1728 ? '404px' : window.innerWidth >= 1440 ? `${dynamicLeftPanelWidth}px` : '300px',
          minWidth: window.innerWidth >= 2560 ? '758px' : window.innerWidth >= 2028 ? '404px' : window.innerWidth >= 1728 ? '404px' : window.innerWidth >= 1440 ? '300px' : '300px',
          maxWidth: window.innerWidth >= 2560 ? '758px' : window.innerWidth >= 2028 ? '758px' : window.innerWidth >= 1728 ? '404px' : window.innerWidth >= 1440 ? '404px' : '300px',
          flexShrink: 0,
        }}
      // style={{ flexBasis: `${leftPanelPercent}%`, minWidth: "404px", maxWidth: '40%', flexShrink: 0 }}

      // style={{
      //   width: isLeftPanelCollapsed ? '0px' : `${leftPanelWidth}px`,
      //   flexShrink: 0,
      //   maxWidth: screenSize === '2xl' ? '600px' : 'none' // Prevent excessive width on large screens
      // }}
      >
        {/* Mobile close button */}
        {isMobileView && !isLeftPanelCollapsed && (
          <div className="flex justify-end p-2 border-b border-[#DCE2EB] lg:hidden">
            <button
              onClick={toggleLeftPanel}
              className="p-2 text-[#5F6F84] hover:text-[#2A2E34] hover:bg-[#F3F5F9] rounded-full transition-all duration-200"
            >
              <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        )}

        {/* Filter button - enhanced for large screens */}
        <div className="px-[30px] pt-[30px] pb-[20px] relative">
          <div className={`flex items-center justify-between`}>
            <div className="relative flex items-center">
              <button
                 className={`bg-white text-[#3b82f6] font-medium ${showFilter ? 'outline-1 outline-[#3f73f6]' : ' outline outline-[#DCE2EB]'} h-[40px] px-[16px] py-[10px] flex items-center justify-between ${window.innerWidth < 1728 ? 'gap-[8px]' : 'gap-[14px]'} cursor-pointer rounded-[12px] hover:bg-gray-50 transition text-[14px] leading-[20px]`}
                onClick={() => (showFilter ? handleFilterDialogCancel() : handleFilterDialogOpen())}
              >
                Filter
                <div
                  className={`flex items-center justify-center bg-[#3b82f6] text-white font-medium rounded-full
                ${screenSize === '2xl' ? 'w-5 h-5 text-xs' : 'w-5 h-5 text-xs'}`}
                >
                  {(() => {
                    const activeFilters = Object.values(filters).filter(Boolean).length;
                    console.log('🔢 Active filters count:', activeFilters, 'Filters:', filters);
                    return activeFilters;
                  })()}
                </div>
              </button>
            </div>

            <div className="flex items-center">
              <span className={`mr-2 text-[#5F6F84] text-[14px] leading-[20px]`}>All workflows</span>
              <div className="flex items-center space-x-2">
                <div
                  className={`w-[39px] h-5 ${showAllWorkflows ? 'bg-[#3F73F6]' : 'bg-[#5F6F84]'} rounded-full p-0.5 flex items-center cursor-pointer transition-colors duration-300`}
                  onClick={handleToggleChange}
                >
                  <div
                    className={`bg-white rounded-full shadow-md transition-transform duration-300
                    ${screenSize === '2xl' ? 'w-4 h-4' : 'w-4 h-4'}`}
                    style={{
                      transform: showAllWorkflows
                        ? screenSize === '2xl'
                          ? 'translateX(19px)'
                          : 'translateX(17px)'
                        : 'translateX(0)',
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          {showFilter && (
            <div
              ref={(el) => {
                if (el) {
                  const updateFilterWidth = () => {
                    // Use the leftPanelRef directly
                    if (leftPanelRef.current) {
                      const sidebarRect = leftPanelRef.current.getBoundingClientRect();
                      const availableWidth = sidebarRect.width - 60;
                      const finalWidth = Math.min(availableWidth, 344);
                      el.style.width = `${finalWidth}px`;
                      console.log('Filter width calculation:', {
                        sidebarWidth: sidebarRect.width,
                        availableWidth,
                        finalWidth
                      });
                    } else {
                      console.log('Left panel ref not found');
                    }
                  };
                  
                  // Set initial width
                  updateFilterWidth();
                  
                  // Add resize listener
                  const handleResize = () => updateFilterWidth();
                  window.addEventListener('resize', handleResize);
                  
                  // Store cleanup function
                  (el as any)._cleanup = () => {
                    window.removeEventListener('resize', handleResize);
                  };
                }
              }}
              className={`absolute mt-[4px] top-16 z-10 bg-white border border-[#DCE2EB] rounded-[12px] p-[16px] gap-[16px]
              ${screenSize === '2xl' ? 'top-18 px-4 py-4' : 'mt-[12px]'}`}
              style={{
                maxWidth: '344px',
                boxShadow: '0 10px 20px 0 rgba(0, 0, 0, 0.08)',
              }}
            >
              <div className={`space-y-1 ${screenSize === '2xl' ? 'space-y-1' : ''}`}>
                <label className="flex items-center cursor-pointer py-[8px]">
                  <CustomCheckbox
                    id="myTasks"
                    checked={pendingFilters.myTasks}
                    onChange={() => handleFilterChange('myTasks')}
                  />
                  <span
                    className={`ml-[10px] text-[#2a2e34]
                  ${screenSize === '2xl' ? 'text-sm' : 'text-sm'}`}
                  >
                    Assigned to Me
                  </span>
                </label>

                <label className="flex items-center cursor-pointer py-[8px]">
                  <CustomCheckbox
                    id="unassigned"
                    checked={pendingFilters.unassigned}
                    onChange={() => handleFilterChange('unassigned')}
                  />
                  <span
                    className={`ml-[10px] text-[#2a2e34]
                  ${screenSize === '2xl' ? 'text-sm' : 'text-sm'}`}
                  >
                    Unassigned
                  </span>
                </label>

                <label className="flex items-center cursor-pointer py-[8px]">
                  <CustomCheckbox
                    id="assigned_to_my_group"
                    checked={pendingFilters.assigned_to_my_group}
                    onChange={() => handleFilterChange('assigned_to_my_group')}
                  />
                  <span
                    className={`ml-[10px] text-[#2a2e34]
                  ${screenSize === '2xl' ? 'text-sm' : 'text-sm'}`}
                  >
                    Assigned to My Group
                  </span>
                </label>

                <label className="flex items-center cursor-pointer py-[8px]">
                  <CustomCheckbox
                    id="allTasks"
                    checked={pendingFilters.allTasks}
                    onChange={() => handleFilterChange('allTasks')}
                  />
                  <span
                    className={`ml-[10px] text-[#2a2e34]
                  ${screenSize === '2xl' ? 'text-sm' : 'text-sm'}`}
                  >
                    All Tasks
                  </span>
                </label>
              </div>

              <div className={`mt-4 flex justify-end gap-2 ${screenSize === '2xl' ? 'mt-4' : ''}`}>
                {/* <button
                  className={`border border-[#DCE2EB] text-[#5F6F84] px-[16px] py-[8px] rounded-[4px] h-[36px] text-[14px] leading-[20px] cursor-pointer font-medium hover:bg-gray-50 transition
                  ${screenSize === '2xl' ? 'px-4 py-2.5 text-sm' : 'text-sm'}`}
                  onClick={handleFilterDialogCancel}
                >
                  Cancel
                </button> */}
                <button
                  className={`bg-[#3F73F6] text-white px-[16px] py-[4px] rounded-[4px] h-[36px] text-[14px] leading-[20px] cursor-pointer font-medium hover:bg-[#305ED2] transition
                  ${screenSize === '2xl' ? 'px-4 py-2.5 text-sm' : 'text-sm'}`}
                  onClick={applyFilters}
                >
                  Apply
                </button>
              </div>
            </div>
          )}
        </div>

        {expanded && (
          <div
            className={`pt-0 px-[20px] pb-[30px] flex flex-col space-y-3 overflow-y-auto flex-grow
          ${screenSize === '2xl' ? 'px-5 pb-5 space-y-3' : ''}`}
          >
            {/* Render all workflows and their tasks */}
            {workflowData && (
              <>
                {/* Main workflow section */}
                {workflowData.workflows &&
                  workflowData.workflows.length > 0 &&
                  (() => {
                    // Show main workflow if toggle is ON or if it's the initial context
                    const shouldShowMainWorkflow =
                      showAllWorkflows || initialActiveContext?.type === 'main';

                    if (!shouldShowMainWorkflow) {
                      return null;
                    }

                    // First check if there are any visible tasks after filtering
                    const visibleTasks = workflowData.workflows[0].tasks.filter(
                      task => task.task_visible_status !== 'DRAFT' && shouldShowTask(task)
                    ); // Always remove DRAFT tasks and apply user/filter logic

                    // Always render the section, even if no visible tasks
                    return (
                      <div
                        className={`mb-[20px] p-[10px] wrapper sidebar-main-wrapper rounded-[12px] ${activeWorkflowContext?.type === 'main' ? 'bg-[#F3F5F9]' : ''}`}
                      >
                        <div
                          className="flex items-top justify-between mb-[16px] cursor-pointer rounded"
                          onClick={() => toggleSectionExpansion('main')}
                        >
                          <div className="flex flex-col">
                            <h3 className="font-normal leading-[24px] mb-[2px] text-[16px] text-[#2A2E34]">
                              {workflowData.workflows[0].name || 'Jeffery Price, SA'}
                            </h3>
                            <p className="text-[12px] font-normal leading-[16px] text-[#2A2E34]">
                              New Court notice
                            </p>
                          </div>

                          {expandedSections['main'] ? (
                            <ChevronUp size={16} className="text-[#5F6F84]" />
                          ) : (
                            <ChevronDown size={16} className="text-[#5F6F84]" />
                          )}
                        </div>

                        {expandedSections['main'] && (
                          <>
                            {visibleTasks.map((task, idx) => {
                              // Check if this task is in the reviewTask array or completed
                              const isReviewed =
                                reviewTask.includes(task.id) || task.status === 'completed';
                              const isCurrentTask =
                                selectedTask?.id === task.id &&
                                activeWorkflowContext?.type === 'main';

                              return (
                                <div key={task.id} className="relative flex items-center mb-2 pl-7">
                                  <div
                                    className={`absolute left-0 w-5 h-5 flex items-center justify-center ${isCurrentTask ? 'bg-[#3F73F6] text-white' : isReviewed ? ' text-gray-600' : ' text-gray-600'} rounded-full`}
                                  >
                                    <span className="text-xs">{idx + 1}</span>
                                  </div>
                                  <div className="flex-1">
                                    <div
                                      className={`flex items-center justify-between h-[40px] p-[8px] rounded-[12px] border text-[14px] leading-[20px]
                                      
                                      ${task?.task_visible_status !== 'REVIEWED'
                                          ? isCurrentTask
                                            ? 'border-[#3F73F6]'
                                            : 'border-[#5F6F84]'
                                          : isCurrentTask
                                            ? 'border-[#3F73F6]'
                                            : 'border-[#C7D1DF]'
                                        }

                                      bg-white cursor-pointer`}
                                      onClick={() => handleTaskClick('main', 0, idx)}
                                    >
                                      <div className="flex items-center task-container">
                                        <Image
                                          src={
                                            task?.task_visible_status !== 'REVIEWED'
                                              ? isCurrentTask
                                                ? '/assets/squares.svg'
                                                : '/assets/square (2).svg'
                                              : '/assets/check-square-gray.svg'
                                          }
                                          alt="Task status"
                                          className="mr-2"
                                          width={20}
                                          height={20}
                                        />
                                        <span
                                          ref={(el) => {
                                            if (el) {
                                              const updateWidth = () => {
                                                const container = el.closest('.sidebar-main-wrapper');
                                                console.log('Container found:', !!container);
                                                if (container) {
                                                  const containerRect = container.getBoundingClientRect();
                                                  const availableWidth = containerRect.width - 100; // Subtract 80px from container width
                                                  console.log('Container width:', containerRect.width, 'Available width:', availableWidth);
                                                  el.style.maxWidth = `${Math.max(availableWidth, 0)}px`;
                                                }
                                              };

                                              // Set initial width
                                              updateWidth();

                                              // Add resize listener
                                              const handleResize = () => updateWidth();
                                              window.addEventListener('resize', handleResize);

                                              // Store cleanup function for later use
                                              (el as any)._cleanup = () => {
                                                window.removeEventListener('resize', handleResize);
                                              };
                                            }
                                          }}
                                          className={`text-[14px] leading-[20px] truncate ${task?.task_visible_status !== 'REVIEWED'
                                            ? isCurrentTask
                                              ? 'text-[#3F73F6]'
                                              : 'text-[#5F6F84]'
                                            : isCurrentTask
                                              ? 'text-[#3F73F6] line-through'
                                              : 'text-[#A2AFC2] line-through'
                                            }`}
                                        >
                                          {task?.name}
                                        </span>
                                      </div>
                                      <div className="relative flex items-center space-x-2">
                                        {shouldShowBlueDot(task) && (
                                          <div className="w-2 h-2 bg-blue-500 rounded-full" />
                                        )}
                                        {(() => {
                                          try {
                                            const taskAssignmentsForThisTask =
                                              taskAssignments[task.id] || [];
                                            const uniqueAssignments = [
                                              ...new Set(taskAssignmentsForThisTask),
                                            ];

                                            if (uniqueAssignments.length > 0) {
                                              const firstValue = uniqueAssignments[0];

                                              const option = assignToOptions.find(
                                                opt =>
                                                  opt.value === firstValue || opt._id === firstValue
                                              );

                                              return (
                                                <div className="relative">
                                                  {option?.profileImage ? (
                                                    <Image
                                                      src={option.profileImage}
                                                      alt={option.text}
                                                      width={28}
                                                      height={28}
                                                      className="w-[24px] h-[24px]  rounded-full"
                                                    />
                                                  ) : option?.initials ? (
                                                    <div className="w-[24px] h-[24px] rounded-full bg-[#5F6F84] flex items-center justify-center text-white text-xs font-medium">
                                                      {option.initials}
                                                    </div>
                                                  ) : (
                                                    <div className="w-[24px] h-[24px]  rounded-full bg-gray-300 flex items-center justify-center">
                                                      <User size={14} className="text-gray-600" />
                                                    </div>
                                                  )}

                                                  {uniqueAssignments.length > 1 && (
                                                    <div className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                                                      {uniqueAssignments.length}
                                                    </div>
                                                  )}
                                                </div>
                                              );
                                            } else {
                                              return null;
                                              // <div className="relative bg-[#3f73f65e] w-7 h-7 rounded-full flex items-center justify-center">
                                              //   <Image
                                              //     src="/assets/ai-robot-new-2.svg"
                                              //     alt="AI assistant"
                                              //     width={25}
                                              //     height={25}
                                              //   />
                                              //   <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                                              //     3
                                              //   </span>
                                              // </div>
                                            }
                                          } catch (error) {
                                            console.error(
                                              'Error displaying task assignments for task:',
                                              task.id,
                                              error
                                            );
                                            return null;
                                            // <div className="relative bg-[#3f73f65e] w-7 h-7 rounded-full flex items-center justify-center">
                                            //   <Image
                                            //     src="/assets/ai-robot-new-2.svg"
                                            //     alt="AI assistant"
                                            //     width={25}
                                            //     height={25}
                                            //   />
                                            //   <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                                            //     3
                                            //   </span>
                                            // </div>
                                          }
                                        })()}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              );
                            })}

                            {/* Create Task button moved inside expanded section */}
                            <div className="flex-1 pl-[28px]">
                              <div className="flex items-center justify-between border-b border-[#DCE2EB] my-2"></div>
                              <button
                                className="w-full flex items-center cursor-pointer border border-[#5F6F84] rounded-[12px] h-[40px] p-[8px] hover:bg-[#f3f5f9b9] transition bg-white text-[#5F6F84] text-[14px] leading-[20px] gap-[8px]"
                                // onClick={() => handleCreateTaskForWorkflow('main')}
                              >
                                <Plus size={16} className="text-[#5F6F84]" />
                                <span className="text-[#5F6F84]">Create a Task</span>
                              </button>
                            </div>
                          </>
                        )}
                      </div>
                    );
                  })()}

                {/* Child workflows - each in its own independently collapsible section */}
                {Object.entries(childWorkflows).map(([key, workflows]) =>
                  workflows.map((childWorkflow, childIndex) => {
                    // Show child workflow if toggle is ON or if it's the initial context
                    const shouldShowChildWorkflow =
                      showAllWorkflows ||
                      (initialActiveContext?.type === key &&
                        initialActiveContext?.index === childIndex);

                    if (!shouldShowChildWorkflow) {
                      return null;
                    }

                    // First check if there are any visible tasks after filtering
                    const visibleTasks = childWorkflow.tasks.filter(
                      task => task.task_visible_status !== 'DRAFT' && shouldShowTask(task)
                    ); // Always remove DRAFT tasks and apply user/filter logic

                    // Hide entire child workflow section if no visible tasks
                    if (visibleTasks.length === 0) {
                      return null;
                    }

                    const sectionKey = `${key}-${childIndex}`;

                    return (
                      <div
                        key={sectionKey}
                        className={`mb-[20px] rounded-[12px] sidebar-main-wrapper p-[10px] ${activeWorkflowContext?.type === key && activeWorkflowContext?.index === childIndex ? 'bg-[#F3F5F9]' : ''}`}
                      >
                        <div className="">
                          <div
                            className="flex items-start justify-between cursor-pointer mb-[16px]"
                            onClick={() => toggleSectionExpansion(sectionKey)}
                          >
                            <div>
                              <h2 className="font-normal text-[#2A2E34] mb-[2px]">
                                {childWorkflow?.tasks[0]?.name}
                              </h2>
                              <p className="text-[12px] font-normal text-[#2A2E34]">
                                {' '}
                                {getChildWorkflowTitle(key)}
                              </p>
                            </div>
                            {expandedSections[sectionKey] ? (
                              <ChevronUp size={16} className="text-[#5F6F84]" />
                            ) : (
                              <ChevronDown size={16} className="text-[#5F6F84]" />
                            )}
                          </div>
                        </div>

                        {expandedSections[sectionKey] && (
                          <>
                            {visibleTasks.map((task, taskIndex) => {
                              const isCurrentChildTask =
                                activeWorkflowContext?.type === key &&
                                activeWorkflowContext?.index === childIndex &&
                                selectedTask?.id === task.id;

                              const isReviewed =
                                reviewTask.includes(task.id) || task.status === 'completed';

                              return (
                                <div key={task.id} className="relative flex items-center mb-2">
                                  <div
                                    className={`absolute left-0 w-5 h-5 flex items-center justify-center ${isCurrentChildTask ? 'bg-[#3F73F6] text-white' : isReviewed ? 'text-gray-600' : ' text-gray-600'} rounded-full`}
                                  >
                                    <span className="text-xs">{taskIndex + 1}</span>
                                  </div>
                                  <div className="flex-1 ml-[28px]">
                                    <div
                                      className={`flex items-center justify-between h-[40px] p-[8px] rounded-[12px] border text-[14px] leading-[20px]
                                        
                                        ${task?.task_visible_status !== 'REVIEWED'
                                          ? isCurrentChildTask
                                            ? 'border-[#3F73F6]'
                                            : 'border-[#5F6F84]'
                                          : isCurrentChildTask
                                            ? 'border-[#3F73F6]'
                                            : 'border-[#C7D1DF]'
                                        }

                                        bg-white cursor-pointer`}
                                      onClick={() => handleTaskClick(key, childIndex, taskIndex)}
                                    >
                                      <div className="flex items-center">
                                        <Image
                                          src={
                                            task?.task_visible_status !== 'REVIEWED'
                                              ? isCurrentChildTask
                                                ? '/assets/squares.svg'
                                                : '/assets/square (2).svg'
                                              : '/assets/check-square-gray.svg'
                                          }
                                          alt="Task status"
                                          className="mr-2"
                                          width={20}
                                          height={20}
                                        />
                                        <div className="flex items-center gap-2">
                                          <span
                                            ref={(el) => {
                                              if (el) {
                                                const updateWidth = () => {
                                                  const container = el.closest('.sidebar-main-wrapper');
                                                  console.log('Container found:', !!container);
                                                  if (container) {
                                                    const containerRect = container.getBoundingClientRect();
                                                    const availableWidth = containerRect.width - 120; // Subtract 80px from container width
                                                    console.log('Container width:', containerRect.width, 'Available width:', availableWidth);
                                                    el.style.maxWidth = `${Math.max(availableWidth, 0)}px`;
                                                  }
                                                };

                                                // Set initial width
                                                updateWidth();

                                                // Add resize listener
                                                const handleResize = () => updateWidth();
                                                window.addEventListener('resize', handleResize);

                                                // Store cleanup function for later use
                                                (el as any)._cleanup = () => {
                                                  window.removeEventListener('resize', handleResize);
                                                };
                                              }
                                            }}
                                            className={`text-[14px] leading-[20px] truncate ${task?.task_visible_status !== 'REVIEWED'
                                              ? isCurrentChildTask
                                                ? 'text-[#3F73F6]'
                                                : 'text-[#5F6F84]'
                                              : isCurrentChildTask
                                                ? 'text-[#3F73F6] line-through'
                                                : 'text-[#A2AFC2] line-through'
                                              }`}
                                          >
                                            {'Notify all parties'}
                                          </span>
                                          {shouldShowBlueDot(task) && (
                                            <div className="w-2 h-2 bg-blue-500 rounded-full" />
                                          )}
                                        </div>
                                      </div>
                                      <div className="relative flex items-center space-x-2">
                                        {/* Show assigned users profile picture */}
                                        {(() => {
                                          try {
                                            const taskAssignmentsForThisTask =
                                              taskAssignments[task.id] || [];
                                            const uniqueAssignments = Array.from(
                                              new Set(taskAssignmentsForThisTask)
                                            );
                                            const firstValue = uniqueAssignments[0];

                                            const option = assignToOptions.find(
                                              opt =>
                                                opt?._id === firstValue || opt?.value === firstValue
                                            );

                                            if (uniqueAssignments.length > 0 && option) {
                                              return (
                                                <div className="relative">
                                                  {option.profileImage ? (
                                                    <Image
                                                      src={option.profileImage}
                                                      alt={option.text}
                                                      width={28}
                                                      height={28}
                                                      className="w-7 h-7 rounded-full"
                                                    />
                                                  ) : option.initials ? (
                                                    <div className="w-7 h-7 rounded-full bg-[#5F6F84] flex items-center justify-center text-white text-xs font-medium">
                                                      {option.initials}
                                                    </div>
                                                  ) : (
                                                    <div className="w-7 h-7 rounded-full bg-gray-300 flex items-center justify-center">
                                                      <User size={14} className="text-gray-600" />
                                                    </div>
                                                  )}
                                                  {uniqueAssignments.length > 1 && (
                                                    <div className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                                                      {uniqueAssignments.length}
                                                    </div>
                                                  )}
                                                </div>
                                              );
                                            } else {
                                              return null;
                                              // <div className="relative bg-[#3f73f65e] w-7 h-7 rounded-full flex items-center justify-center">
                                              //   <Image
                                              //     src="/assets/ai-robot-new-2.svg"
                                              //     alt="AI assistant"
                                              //     width={25}
                                              //     height={25}
                                              //   />
                                              //   <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                                              //     3
                                              //   </span>
                                              // </div>
                                            }
                                          } catch (error) {
                                            console.error(
                                              'Error displaying task assignments for task:',
                                              task.id,
                                              error
                                            );
                                            return null;
                                            // <div className="relative bg-[#3f73f65e] w-7 h-7 rounded-full flex items-center justify-center">
                                            //   <Image
                                            //     src="/assets/ai-robot-new-2.svg"
                                            //     alt="AI assistant"
                                            //     width={25}
                                            //     height={25}
                                            //   />
                                            //   <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                                            //     3
                                            //   </span>
                                            // </div>
                                          }
                                        })()}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              );
                            })}

                            {/* Create Task button moved inside expanded section for child workflows */}
                            <div className="flex-1 pl-[28px]">
                              <div className="flex items-center justify-between border-b border-[#DCE2EB] my-2"></div>
                              <button
                                className="w-full flex items-center cursor-pointer border border-[#5F6F84] rounded-[12px] h-[40px] p-[8px] hover:bg-[#f3f5f9b9] transition bg-white text-[#5F6F84] text-[14px] leading-[20px] gap-[8px]"
                                // onClick={() => handleCreateTaskForWorkflow(key)}
                              >
                                <Plus size={16} className="text-[#5F6F84]" />
                                <span className="text-[#5F6F84]">Create a Task</span>
                              </button>
                            </div>
                          </>
                        )}
                      </div>
                    );
                  })
                )}
              </>
            )}
          </div>
        )}
      </div>

      {/* Main content area - enhanced responsive design */}
      <div className="flex-1 flex min-w-0 bg-white">
        {/* Center panel - enhanced responsive */}
        <div
          ref={centerPanelRef}
          className="flex-1 flex flex-col bg-white relative min-w-0 max-w-[920px] mx-auto"
          // style={{
          //   width: getCenterPanelWidth(),
          //   flexShrink: 0,
          //   maxWidth: screenSize === '2xl' ? '1400px' : 'none' // Prevent excessive width on large screens
          // }}
          style={{
            flexGrow: window.innerWidth >= 2560 ? 0 : window.innerWidth >= 2028 ? 0 : window.innerWidth >= 1728 ? 1 : window.innerWidth >= 1440 ? 0 : 1,
            minWidth: window.innerWidth >= 2560 ? '920px' : window.innerWidth >= 2028 ? '920px' : window.innerWidth >= 1728 ? 200 : window.innerWidth >= 1440 ? '600px' : 200,
            flexShrink: window.innerWidth >= 2560 ? 0 : window.innerWidth >= 2028 ? 0 : window.innerWidth >= 1728 ? 1 : window.innerWidth >= 1440 ? 0 : 1,
            width: window.innerWidth >= 2560 ? '920px' : window.innerWidth >= 2028 ? '920px' : window.innerWidth >= 1728 ? 'auto' : window.innerWidth >= 1440 ? '600px' : 'auto'
          }}
        >
          {/* Notification Banner - Enhanced for large screens */}
          {selectedWorkflow?.is_archive && (
            <div
              className={`w-full bg-[#C7D1DF] transform transition-all duration-500 ease-in-out ${showNotification ? 'opacity-100' : 'opacity-0'}
            ${screenSize === '2xl' ? 'py-4' : 'py-3'}`}
            >
              <div className="flex items-center justify-center gap-2">
                <Image
                  src="/IconsBar/check-circle.svg"
                  alt="Task status"
                  width={screenSize === '2xl' ? 24 : 20}
                  height={screenSize === '2xl' ? 24 : 20}
                />
                <span
                  className={`text-[#2A2E34]
                ${screenSize === '2xl' ? 'text-[14px]' : 'text-[14px]'}`}
                >
                  This workflow run was archived by{' '}
                  {(() => {
                    try {
                      const userData = localStorage?.getItem('user');
                      if (userData) {
                        const user = JSON.parse(userData);
                        return selectedWorkflow?.archive_by
                          ? selectedWorkflow?.archive_by
                          : user?.email;
                      }
                      return 'a user';
                    } catch (e) {
                      console.error(e);
                      return 'a user';
                    }
                  })()}{' '}
                  {selectedWorkflow?.archive_at === 'Invalid date'
                    ? 'Just Now'
                    : selectedWorkflow?.archive_at}
                  .
                </span>
              </div>
            </div>
          )}

          {selectedWorkflow?.workflow_status == 'COMPLETED' && (
            <div
              className={`w-full bg-[#8CF1BD] transform transition-all duration-500 px-2 ease-in-out ${showNotification ? 'opacity-100' : 'opacity-0'}
            ${screenSize === '2xl' ? 'py-4' : 'py-3'}`}
            >
              <div className="flex items-center justify-center gap-2">
                <Image
                  src="/IconsBar/check-circle.svg"
                  alt="Task status"
                  width={screenSize === '2xl' ? 24 : 20}
                  height={screenSize === '2xl' ? 24 : 20}
                />
                <span
                  className={`text-[#2A2E34]
                ${screenSize === '2xl' ? 'text-[14px]' : 'text-[14px]'}`}
                >
                  This workflow run was completed by{' '}
                  {(() => {
                    try {
                      const userData = localStorage?.getItem('user');
                      if (userData) {
                        const user = JSON.parse(userData);
                        return selectedWorkflow?.archive_by
                          ? selectedWorkflow?.archive_by
                          : user?.email;
                      }
                      return 'a user';
                    } catch (e) {
                      console.log(e);
                      return 'a user';
                    }
                  })()}{' '}
                  {selectedWorkflow?.archive_at === 'Invalid date'
                    ? 'Just Now'
                    : selectedWorkflow?.archive_at}
                  .
                </span>
              </div>
            </div>
          )}

          <div className="flex-1 overflow-y-auto [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100">
            {/* Loader during filters is now handled by a full-screen overlay. */}
            
            {/* REMOVED: NoDataFound component - We want only blank display like first image */}
            
            {/* Show main content ONLY after filter check is complete and there are visible tasks OR when we need to show no data message */}
            {!filtersLoading && initialFilterCheckComplete && (
              <div className="p-[30px]">
                <div
                  className="border-b border-gray-200 flex justify-between items-center bg-white pb-[32px]"
                >
                <div className="flex items-center">
                  {/* Mobile hamburger menu */}
                  {isMobileView && (
                    <button
                      onClick={toggleLeftPanel}
                      className="p-2 mr-3 text-[#5F6F84] hover:text-[#2A2E34] hover:bg-[#F3F5F9] rounded-lg transition-all duration-200 lg:hidden"
                    >
                      <svg
                        width="20"
                        height="20"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 6h16M4 12h16M4 18h16"
                        />
                      </svg>
                    </button>
                  )}
                  <span
                    className={`font-medium text-[#2A2E34] truncate
                  ${screenSize === '2xl' ? 'text-2xl leading-8' : 'text-lg sm:text-xl lg:text-2xl leading-[36px]'}`}
                  >
                    {activeWorkflowContext?.type.startsWith('child_workflow_') 
                      ? 'Court notice follow up' 
                      : (selectedTask ? selectedTask.name : selectedWorkflow?.name || '')
                    }
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  {/* Mobile right panel toggle */}
                  {isMobileView && (
                    <button
                      onClick={toggleRightPanelMobile}
                      className="p-2 text-[#5F6F84] hover:text-[#2A2E34] hover:bg-[#F3F5F9] rounded-lg transition-all duration-200 lg:hidden"
                    >
                      <svg
                        width="18"
                        height="18"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </button>
                  )}
                  <button
                    className="border border-[#DCE2EB] rounded-[12px] transition ml-2 w-[40px] h-[40px] flex items-center justify-center"
                  >
                    <Image
                      src="/assets/dots-horizontal.svg"
                      alt="Options"
                      className="text-[#5F6F84] cursor-pointer"
                      width={screenSize === '2xl' ? 18 : 18}
                      height={screenSize === '2xl' ? 18 : 18}
                    />
                  </button>
                </div>
              </div>

              {/* Form validation error summary - Enhanced for large screens */}
              {formHasErrors && Object.keys(validationErrors).length > 0 && (
                <div
                  className={`mb-6 p-3 bg-red-50 border border-red-200 rounded-[12px]
                ${screenSize === '2xl' ? 'p-4 mb-6' : ''}`}
                >
                  <div className="flex items-center text-red-800 mb-2">
                    <AlertTriangle size={screenSize === '2xl' ? 18 : 16} className="mr-2" />
                    <span className={`font-medium ${screenSize === '2xl' ? 'text-base' : ''}`}>
                      Please fix the following errors:
                    </span>
                  </div>
                  <ul
                    className={`text-red-600 ml-6 list-disc
                  ${screenSize === '2xl' ? 'text-sm' : 'text-sm'}`}
                  >
                    {Object.values(validationErrors).map((error, i) => (
                      <li key={i}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}


              {/* Show message when no workflow is selected */}
              {!selectedWorkflow && !workflowData && !router.query.work_flow_id && (
                <div className="flex flex-col items-center justify-center py-16 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <svg
                      className="w-8 h-8 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Workflow Selected</h3>
                  <p className="text-gray-600 max-w-md">
                    Please select a workflow from the sidebar to get started.
                  </p>
                </div>
              )}

              {/* Show centered message when no filter data found - HANDLES PAGE RELOAD */}
              {(router.query.work_flow_id || window.location.pathname.includes('workflowrun')) && 
               initialFilterCheckComplete && 
               (!selectedTask || isEmptyDueToFilters) && 
               (!loading || (router.isReady && (!pageDataLoading || initialFilterCheckComplete))) && (
                <div className="flex flex-col items-center justify-center py-16 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <svg
                      className="w-8 h-8 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No filter data found</h3>
                  <p className="text-gray-600 max-w-md">
                    No records match your current filter criteria. Try adjusting your filters to see more results.
                  </p>
                </div>
              )}

              {/* Render the workflow form ONLY after filter check is complete and task is valid */}
              {!pageDataLoading && !filtersLoading && selectedTask && !isEmptyDueToFilters && initialFilterCheckComplete && (
                <div className={`mb-6 ${screenSize === '2xl' ? 'mb-6' : ''}`}>
                  <div
                    ref={assignToTriggerRef}
                    className="flex justify-between items-center mt-4 bg-white rounded-[12px] group"
                  >
                    <div
                      className="flex items-center space-x-2"
                    >
                      <User
                        className={`${assignToDropdownOpen || assignToSelectedValues.length > 0 ? 'text-[#3F73F6]' : 'text-[#5F6F84]'} flex-shrink-0`}
                        size={20}
                      />
                      <div
                        className={`text-normal text-[#5F6F84] cursor-pointer group-hover:text-[#2A2E34] transition-colors
                        ${screenSize === '2xl' ? 'text-sm leading-5' : 'text-xs sm:text-sm leading-[20px]'}`}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (selectedTask?.task_visible_status !== 'REVIEWED') {
                            setAssignToDropdownOpen(!assignToDropdownOpen);
                          }
                        }}
                      >
                        {assignToSelectedValues.length > 0 ? (
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center space-x-1">
                              {(() => {
                                const uniqueSelectedValues = [...new Set(assignToSelectedValues)];

                                const firstValue = uniqueSelectedValues[0];
                                const option = assignToOptions.find(
                                  opt => opt._id === firstValue || opt.value === firstValue
                                );

                                return (
                                  <div className="relative">
                                    {option?.profileImage ? (
                                      <Image
                                        src={option.profileImage}
                                        alt={option.text}
                                        width={24}
                                        height={24}
                                        className="w-6 h-6 rounded-full"
                                      />
                                    ) : option?.initials ? (
                                      <div className="w-6 h-6 rounded-full bg-[#5F6F84] flex items-center justify-center text-white text-xs font-medium">
                                        {option.initials}
                                      </div>
                                    ) : (
                                      <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center">
                                        <User
                                          size={20}
                                          className={
                                            assignToDropdownOpen
                                              ? 'text-[#3F73F6]'
                                              : 'text-[#5F6F84]'
                                          }
                                        />
                                      </div>
                                    )}

                                    {uniqueSelectedValues.length > 1 && (
                                      <div className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                                        {uniqueSelectedValues.length}
                                      </div>
                                    )}
                                  </div>
                                );
                              })()}
                            </div>
                          </div>
                        ) : (
                          <span
                            className={`${assignToDropdownOpen ? 'text-[#3f73f6]' : 'text-[#5F6F84]'} group-hover:text-[#2A2E34]`}
                          >
                            Assign To
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 group hover:cursor-pointer">
                      <Image
                        src="/assets/calendar1.svg"
                        alt="Calendar"
                        className="text-[#5F6F84] group-hover:text-[#2A2E34] cursor-pointer flex-shrink-0 w-5 h-5"
                        width={20}
                        height={20}
                        onClick={() => {
                          if (selectedTask?.task_visible_status !== 'REVIEWED' && selectedTask?.end_date) {
                            setShowDateModal(true);
                          }
                        }}
                      />
                      <div className="flex items-center justify-center space-x-1">
                        <span
                          className={`text-normal text-[#5F6F84] cursor-pointer group-hover:text-[#2A2E34] transition-colors truncate
                          ${screenSize === '2xl' ? 'text-sm leading-[20px]' : 'text-xs sm:text-sm leading-[20px]'}`}
                          onClick={() => {
                            if (selectedTask?.task_visible_status !== 'REVIEWED' && selectedTask?.end_date) {
                              setShowDateModal(true);
                            }
                          }}
                          title="Click to change date and time"
                        >
                          {parseAndFormatTaskDate(selectedTask?.end_date)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Multi-select dropdown for Assign To */}
                  <div
                    className={`mb-4 bg-white rounded-[12px] w-1/2 ${screenSize === '2xl' ? 'p-5' : 'p-3 sm:p-4'}`}
                    style={{ display: assignToDropdownOpen ? 'contents' : 'none' }}
                  >
                    <div className="relative w-full">
                      <div
                        onKeyDown={handleAssignToKeyDown}
                        tabIndex={selectedTask?.task_visible_status === 'REVIEWED' ? -1 : 0}
                        role="button"
                        style={{ display: 'none' }}
                        className={`flex items-center justify-between w-full px-3 py-2 text-left text-sm border border-[#DCE2EB] rounded-[8px] bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${assignToDropdownOpen ? 'ring-2 ring-blue-500 border-transparent' : ''
                          } ${selectedTask?.task_visible_status === 'REVIEWED' ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
                      >
                        <div className="flex items-center space-x-2 min-w-0 flex-1">
                          {assignToSelectedValues.length > 0 ? (
                            <div className="flex items-center space-x-1">
                              {assignToSelectedValues?.map((val, index) => {
                                const option = assignToOptions.find(opt => opt.value === val);

                                return (
                                  <div key={val} className="flex items-center space-x-1">
                                    {option?.profile_picture ? (
                                      <img
                                        src={option.profile_picture}
                                        alt={option.label}
                                        className="w-6 h-6 rounded-full object-cover"
                                      />
                                    ) : (
                                      <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center">
                                        <User className="w-3 h-3 text-black" />
                                      </div>
                                    )}
                                    {index === 1 && assignToSelectedValues.length > 2 && (
                                      <span className="text-xs text-gray-500 ml-1">
                                        {/* +{assignToSelectedValues.length - 2} */}
                                      </span>
                                    )}
                                  </div>
                                );
                              })}
                              {assignToSelectedValues.length === 1 && (
                                <span className="text-sm text-gray-700 ml-2">
                                  {
                                    assignToOptions.find(
                                      opt => opt.value === assignToSelectedValues[0]
                                    )?.label
                                  }
                                </span>
                              )}
                            </div>
                          ) : (
                            <span className="text-gray-500">Assign To</span>
                          )}
                        </div>
                        <ChevronDown
                          className={`h-4 w-4 text-gray-400 transition-transform ${assignToDropdownOpen ? 'rotate-180' : ''}`}
                        />
                      </div>
                      {assignToDropdownOpen && selectedTask?.task_visible_status !== 'REVIEWED' && (
                        <div
                          ref={assignToDropdownRef}
                          className="absolute z-10 bg-white border border-[#DCE2EB] rounded-[12px]"
                          style={{
                            width: '280px',
                            top: '10px',
                            boxShadow: '0px 10px 20px 0px rgba(0, 0, 0, 0.08)',
                          }}
                        >
                          <div className="p-[16px]">
                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <img
                                  src="/assets/search.svg"
                                  alt="Search"
                                  className="h-5 w-5 text-[#5F6F84]"
                                />
                              </div>
                              <input
                                ref={assignToSearchInputRef}
                                type="text"
                                value={assignToSearchTerm}
                                onChange={e => {
                                  setAssignToSearchTerm(e.target.value);
                                }}
                                onKeyDown={handleAssignToKeyDown}
                                placeholder="Type a name"
                                className="w-full h-[36px] pl-10 pr-[12px] py-[8px] border border-[#DCE2EB] rounded-[4px] text-[14px] leading-[20px] focus:outline-none focus:ring-2 placeholder:text-[#5F6F84] focus:ring-blue-200 focus:border-[#3F73F6]"
                                onClick={e => e.stopPropagation()}
                              />
                              {assignToSearchTerm && (
                                <button
                                  type="button"
                                  onClick={e => {
                                    e.stopPropagation();
                                    setAssignToSearchTerm('');
                                    assignToSearchInputRef.current?.focus();
                                  }}
                                  className="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-800 text-gray-400 transition-colors"
                                >
                                  <svg
                                    className="h-5 w-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M6 18L18 6M6 6l12 12"
                                    />
                                  </svg>
                                </button>
                              )}
                            </div>
                          </div>

                          <div className="pb-[16px] max-h-[402px] overflow-y-auto">
                            {[...new Set(assignToSelectedValues)].length > 0 && (
                              <div>
                                <div className="px-[16px] py-[8px] mb-1">
                                  <div className="text-[14px] leading-[20px] font-medium text-[#2A2E34]">
                                    Assigned
                                  </div>
                                </div>
                                <div className="space-y-1 mb-1">
                                  {[...new Set(assignToSelectedValues)].map((val, index) => {
                                    const option = assignToOptions.find(
                                      opt => opt.value === val || opt._id === val
                                    );

                                    // Find the assigned user data to check isdeSelected flag
                                    const assignedUser = assignedUsersData.find(
                                      user => user.id === val || user._id === val
                                    );

                                    return (
                                      <div
                                        key={index}
                                        className="flex items-center justify-between px-[16px] py-[8px] transition-colors duration-150"
                                      >
                                        <div className="flex items-center space-x-3">
                                          {option?.profileImage ? (
                                            <Image
                                              src={option.profileImage}
                                              alt={option.text}
                                              width={32}
                                              height={32}
                                              className="w-8 h-8 rounded-full object-cover"
                                            />
                                          ) : option?.initials ? (
                                            <div className="w-[30px] h-[30px] rounded-full bg-[#5F6F84] flex items-center justify-center text-white text-sm font-medium">
                                              {option.initials}
                                            </div>
                                          ) : (
                                            <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                                              <User size={16} className="text-gray-600" />
                                            </div>
                                          )}
                                          <span className="text-sm font-normal">
                                            {option?.text || val}
                                          </span>
                                        </div>
                                        {/* Only show X button if user is not deselected */}
                                        {assignedUser?.isdeSelected && (
                                          <button
                                            type="button"
                                            onClick={e => {
                                              e.stopPropagation();
                                              removeUserFromTask(val, true);
                                            }}
                                            className="text-[#5F6F84] hover:text-gray-600 cursor-pointer transition-colors"
                                          >
                                            <X size={20} />
                                          </button>
                                        )}
                                      </div>
                                    );
                                  })}
                                </div>
                                {/* Divider */}
                                <div className="border-b border-[#DCE2EB] my-[8px] mx-[16px]"></div>
                              </div>
                            )}

                            {/* Suggestions section */}
                            <div className="px-[16px] py-[4px] mb-1">
                              <div className="text-[14px] leading-[20px] font-medium text-[#2A2E34]">
                                Suggestions
                              </div>

                            </div>
                            <div className="space-y-1">
                              {assignToLoading ? (
                                <div className="py-4 text-center text-gray-500 text-sm">
                                  Loading...
                                </div>
                              ) : filteredAssignToOptions.length > 0 ? (
                                filteredAssignToOptions
                                  .filter(
                                    option => !assignToSelectedValues.includes(option.value)
                                  )
                                  .map((option, index) => (
                                    <div
                                      key={index}
                                      onClick={() => handleAssignToOptionSelect(option as any)}
                                      onMouseEnter={() => setAssignToHighlightedIndex(index)}
                                      className={`group px-[16px] py-[8px] cursor-pointer flex items-center space-x-[10px] transition-colors duration-150 ${assignToHighlightedIndex === index
                                        ? 'bg-[#D9E3FD] text-[#3F73F6]'
                                        : 'hover:bg-[#D9E3FD] hover:text-[#3F73F6]'
                                        }`}
                                    >
                                      {option.profileImage ? (
                                        <Image
                                          src={option.profileImage}
                                          alt={option.text}
                                          width={30}
                                          height={30}
                                          className="w-[30px] h-[30px] rounded-full object-cover"
                                        />
                                      ) : option.initials ? (
                                        <div className="w-[30px] h-[30px] rounded-full bg-[#5F6F84] flex items-center justify-center text-white text-sm font-medium">
                                          {option.initials}
                                        </div>
                                      ) : (
                                        <div className="w-[30px] h-[30px] rounded-full bg-gray-300 flex items-center justify-center">
                                          <User size={16} className="text-gray-600" />
                                        </div>
                                      )}
                                      <span className="text-[14px] leading-[20px] font-normal text-[#2A2E34] group-hover:text-[#3F73F6]">
                                        {option.text}
                                      </span>
                                    </div>
                                  ))
                              ) : (
                                <div className="py-4 text-gray-500 text-sm text-center">
                                  {assignToSearchTerm ? 'No users found' : 'No users available'}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Form fields - Enhanced spacing for large screens */}
                  {formFields.map((section: FormSection, idx: number) => {
                    // Determine if this section should be disabled based on workflow context and section id
                    const isChildWorkflowSection =
                      activeWorkflowContext?.type.startsWith('child_workflow_');
                    const isSensitiveSection =
                      section.id === 'assign-section-01' || section.id === 'court-notice-section';

                    // Check if the task is reviewed or in a special state
                    const isTaskReviewed = selectedTask?.task_visible_status === 'REVIEWED';

                    // Determine if section should be disabled
                    // If task is reviewed, all sections are disabled
                    // Otherwise, only disable sensitive sections in child workflows
                    const shouldDisableSection =
                      isTaskReviewed || (isChildWorkflowSection && isSensitiveSection);

                    return (
                      <div
                        key={`section-${section.id || section._id || idx}`}
                        className={idx === 0 ? 'mt-[50px] mb-[50px]' : 'mb-[50px]'}
                      >
                        <WorkflowField
                          section={section}
                          setFormFields={setFormFields}
                          onInputChange={handleInputChange}
                          onBlur={(fieldId, value) =>
                            saveFieldToAPI({
                              workflow_id: selectedWorkflow?.tasks[0]?.work_flow_id,
                              task_id: selectedTask?.id,
                              forms: [
                                {
                                  form_component_id: fieldId,
                                  value: [{ id: null, value: value }],
                                },
                              ],
                            })
                          }
                          task_id={selectedTask?.id}
                          user_group_id={selectedTask?.group_id}
                          formData={formData}
                          formFields={formFields}
                          work_flow_id={selectedWorkflow?.tasks[0]?.work_flow_id || ''}
                          isDisabled={shouldDisableSection}
                          isChildWorkflow={isChildWorkflowSection}
                          apiOptions={apiOptions as Record<string, unknown> | undefined}
                          isTaskReviewed={isTaskReviewed}
                          work_flow_execution_id={activeWorkflowContext?.workflow?.workflow_execution_id}
                          setAssignToSelectedValues={setAssignToSelectedValues}
                          assignToSelectedValues={assignToSelectedValues}
                          assignUserToTask={assignUserToTask}
                          removeAssignToItem={removeUserFromTask}
                        />
                      </div>
                    );
                  })}

                  {/* Save status messages - Enhanced for large screens */}
                  {saveSuccess && (
                    <div
                      className={`mt-4 p-3 bg-green-50 text-green-700 rounded-[12px] flex items-center
                    ${screenSize === '2xl' ? 'mt-4 p-3' : ''}`}
                    >
                      <CheckCircle size={screenSize === '2xl' ? 16 : 16} className="mr-2" />
                      <span className={screenSize === '2xl' ? 'text-sm' : ''}>
                        Data saved successfully!
                      </span>
                    </div>
                  )}
                </div>
              )}

              {/* Only show action buttons when a task is selected AND filter check is complete */}
              {selectedTask && !isEmptyDueToFilters && initialFilterCheckComplete && (
                <div
                  className={`flex justify-between items-center gap-4
                ${screenSize === '2xl' ? 'gap-4' : ''}`}
                >
                  <button
                    className={`border w-full font-medium border-[#DCE2EB] rounded-[12px] text-[#A2AFC2] cursor-not-allowed h-[40px] px-[16px] py-[10px] text-[14px] leading-[20px]`}
                    disabled
                  >
                    Skip
                  </button>
                  {isLastTask() ? (
                    <button
                      className={`cursor-pointer bg-[#3F73F6] w-full text-white rounded-[12px] font-medium hover:bg-[#305ED2] transition h-[40px] px-[16px] py-[10px] text-[14px] leading-[20px]
                      disabled:border disabled:text-[#A2AFC2] disabled:border-[#D0D5DD]
                       disabled:bg-[#EFF2F7] disabled:cursor-not-allowed`}
                      onClick={() => handleTaskAction('complete')}
                      disabled={selectedTask?.task_visible_status === 'REVIEWED'}
                    >
                      Complete
                    </button>
                  ) : (
                    <button
                      className={`cursor-pointer bg-[#3F73F6] w-full text-white rounded-[12px] font-medium hover:bg-[#305ED2] transition h-[40px] px-[16px] py-[10px] text-[14px] leading-[20px] 
                      disabled:border disabled:text-[#A2AFC2] 
                      disabled:bg-[#EFF2F7] disabled:cursor-not-allowed
                      ${screenSize === '2xl' ? 'px-5 py-3 text-sm' : 'px-3 sm:px-4 py-2 sm:py-2.5 text-xs sm:text-sm'}`}
                      onClick={() => handleTaskAction('next')}
                      disabled={selectedTask?.task_visible_status === 'REVIEWED'}
                    >
                      Complete
                    </button>
                  )}
                </div>
              )}
              </div>
            )}
          </div>
        </div>

        {/* Right panel container - enhanced responsive */}
        {!filtersLoading && (
          <div
            className={`
            flex-shrink-0 border-l border-[#DCE2EB]
            ${(screenSize as string) !== 'xl-plus' && isRightPanelOverlayOpen ? 'absolute top-0 h-full bg-white shadow-xl z-50' : 'relative'}
            ${isMobileView ? 'hidden' : ''}
            ${(screenSize as string) === 'xl-plus' || (screenSize as string) === '2xl' || (screenSize as string) === '3xl' || (screenSize as string) === '4xl' || (screenSize as string) === '5xl' || ((screenSize as string) !== 'xl-plus' && isRightPanelOverlayOpen) ? '' : 'hidden'}
          `}
            style={{
            flexBasis: window.innerWidth >= 2560 ? '758px' : window.innerWidth >= 2028 ? `${dynamicRightPanelWidth}px` : window.innerWidth >= 1728 ? '600px' : window.innerWidth >= 1440 ? 'auto' : 'auto',
            maxWidth: window.innerWidth >= 2560 ? '758px' : window.innerWidth >= 2028 ? '758px' : window.innerWidth >= 1728 ? '600px' : window.innerWidth >= 1440 ? 'none' : 'none',
            flexGrow: window.innerWidth >= 2560 ? 0 : window.innerWidth >= 2028 ? 0 : window.innerWidth >= 1728 ? 0 : window.innerWidth >= 1440 ? 1 : 1,
            flexShrink: window.innerWidth >= 2560 ? 0 : window.innerWidth >= 2028 ? 0 : window.innerWidth >= 1728 ? 0 : window.innerWidth >= 1440 ? 1 : 1,
            right: (screenSize as string) !== 'xl-plus' && isRightPanelOverlayOpen ? `${getSidebarWidth()}px` : 'auto',
            width: window.innerWidth >= 2560 ? '758px' : window.innerWidth >= 2028 ? `${dynamicRightPanelWidth}px` : window.innerWidth >= 1728 ? '600px' : window.innerWidth >= 1440 ? 'auto' : (screenSize as string) !== 'xl-plus' && isRightPanelOverlayOpen ? '416px' : 'auto'
          }}
          >
          {/* Right panel - sidebar */}
          {activePanel === 'advisor' ? (
            <>
              {/* Show blank Personal Advisor section when no tasks match filters or filter check not complete */}
              {!initialFilterCheckComplete || isEmptyDueToFilters || !selectedTask ? (
                <div className="flex flex-col w-full bg-white h-full">
                  {/* Header Section */}
                  <div className={`px-[30px] pt-[30px] pb-[30px] flex justify-between items-center ${screenSize === '2xl' ? 'p-5' : ''}`}>
                    <h2 className={`font-medium text-[20px] text-gray-800 ${screenSize === '2xl' ? 'text-base' : ''}`}>
                      Personal Advisor
                    </h2>
                    <button
                      className={`p-2 cursor-pointer text-[#5F6F84] hover:text-gray-700 hover:bg-[#F3F5F9] rounded-full transition ${screenSize === '2xl' ? 'p-2' : ''} ${screenSize === 'xl-plus' || screenSize === '2xl' || screenSize === '3xl' || screenSize === '4xl' || screenSize === '5xl' ? 'hidden' : ''}`}
                      onClick={screenSize !== 'xl-plus' ? () => setIsRightPanelOverlayOpen(false) : undefined}
                    >
                      <svg
                        width={screenSize === '2xl' ? '18' : '16'}
                        height={screenSize === '2xl' ? '18' : '16'}
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  </div>

                  {/* Blank/Empty Content Area - No Court Notice Email, No Notice Summary */}
                  <div className="flex-1 bg-white">
                    {/* Intentionally blank - no content when no tasks match filters */}
                  </div>
                </div>
              ) : (
                <AdvisorPanel
                  emailInfo={emailInfo || undefined}
                  noticeSummary={noticeSummary || undefined}
                  clientNotFound={clientNotFound}
                  panelRef={rightPanelRef}
                  style={{ width: '100%', height: '100%' }}
                  screenSize={
                    screenSize === '3xl' || screenSize === '4xl' || screenSize === '5xl'
                      ? '2xl'
                      : screenSize
                  } // Pass screen size to advisor panel
                  onNoticeSummaryUpdate={handleNoticeSummaryUpdate}
                  onClose={screenSize !== 'xl-plus' ? () => setIsRightPanelOverlayOpen(false) : undefined}
                />
              )}
            </>
          ) : (
            <DetailsPanel
              panelRef={rightPanelRef}
              work_flow_id={selectedWorkflow?.tasks[0]?.work_flow_id || ''}
              is_archive={selectedWorkflow?.is_archive}
              workflow_status={selectedWorkflow?.workflow_status}
              style={{ width: '100%', height: '100%' }}
              onClose={() => {
                setActivePanel('advisor');
                setActiveSidebarIcon(0);
              }}
              onNotificationChange={setShowNotification}
              onArchiveChange={handleArchiveChange}
              onArchiveSuccess={fetchWorkflowData}
              onStatusChange={handleStatusChange}
              canComplete={Boolean(canComplete) && isLastTaskActive()}
              onTaskAction={handleCompleteRunTask}
              onActiveRunTask={handleActiveRunTask}
              task_id={typeof taskId === 'string' ? taskId : ''}
            />
          )}
          </div>
        )}

        {/* Mobile Right Panel Overlay - Enhanced */}
        {isMobileView && (
          <>
            {/* Overlay background */}
            <div
              className={`fixed inset-0 bg-black bg-opacity-50 z-30 transition-opacity duration-300 lg:hidden ${activePanel ? 'opacity-100' : 'opacity-0 pointer-events-none'
                }`}
              onClick={() => setActivePanel('advisor')}
            />

            {/* Right panel content */}
            <div
              className={`
              fixed top-0 right-0 h-full bg-white shadow-xl z-40 transform transition-transform duration-300 lg:hidden
              ${activePanel ? 'translate-x-0' : 'translate-x-full'}
              ${screenSize === 'sm' ? 'w-72' : 'w-80 sm:w-96'}
            `}
            >
              {activePanel === 'advisor' ? (
                <AdvisorPanel
                  emailInfo={emailInfo || undefined}
                  noticeSummary={noticeSummary || undefined}
                  clientNotFound={clientNotFound}
                  panelRef={rightPanelRef}
                  style={{ width: '100%', height: '100%' }}
                  screenSize={
                    screenSize === '3xl' || screenSize === '4xl' || screenSize === '5xl'
                      ? '2xl'
                      : screenSize
                  }
                  onNoticeSummaryUpdate={handleNoticeSummaryUpdate}
                  onClose={screenSize !== 'xl-plus' ? () => setIsRightPanelOverlayOpen(false) : undefined}
                />
              ) : (
                <DetailsPanel
                  panelRef={rightPanelRef}
                  work_flow_id={selectedWorkflow?.tasks[0]?.work_flow_id || ''}
                  is_archive={selectedWorkflow?.is_archive}
                  workflow_status={selectedWorkflow?.workflow_status}
                  style={{ width: '100%', height: '100%' }}
                  onClose={() => {
                    setActivePanel('advisor');
                    setActiveSidebarIcon(0);
                  }}
                  onNotificationChange={setShowNotification}
                  onArchiveChange={handleArchiveChange}
                  onArchiveSuccess={fetchWorkflowData}
                  onStatusChange={handleStatusChange}
                  canComplete={Boolean(canComplete) && isLastTaskActive()}
                  onTaskAction={handleCompleteRunTask}
                  onActiveRunTask={handleActiveRunTask}
                  task_id={typeof taskId === 'string' ? taskId : ''}
                />
              )}
            </div>
          </>
        )}



        {/* Sidebar icons section - enhanced responsive */}
        {!filtersLoading && (
          <div
            className={`flex flex-col cursor-pointer bg-white items-center border-l pb-2 border-[#DCE2EB] flex-shrink-0
          ${screenSize === '2xl' ? 'w-18 pb-3' : 'w-12 sm:w-14 lg:w-16'}`}
            style={{ width: `${getSidebarWidth()}px` }}
          >
          {sidebarIcons.map((item, index) => (
            <div
              key={index}
              className="w-full border-b border-[#DCE2EB] flex items-center justify-center p-3 transition-all duration-200"
            >
              <button
                className={`flex items-center cursor-pointer justify-center w-[40px] h-[40px] transition-all duration-200 ${index === activeSidebarIcon
                  ? `bg-[#3F73F6] rounded-[12px]`
                  : hoveredIcon === index
                    ? `bg-[#ECF1FE] rounded-[12px]`
                    : ''
                  }`}
                onClick={() => handleSidebarIconClick(index, item.icon)}
                onMouseEnter={() => setHoveredIcon(index)}
                onMouseLeave={() => setHoveredIcon(null)}
              >
                <Image
                  src={index === activeSidebarIcon ? item.activeIcon : item.icon}
                  alt={`Menu icon ${index + 1}`}
                  width={24}
                  height={24}
                  className={`w-6 h-6 transition-all duration-200 ${index === activeSidebarIcon ? 'brightness-0 invert' : ''}`}
                />
              </button>
            </div>
          ))}
          </div>
        )}
      </div>

      {/* Date Modal - Enhanced for large screens */}
      <DateTimeModal
        isOpen={showDateModal}
        onClose={() => setShowDateModal(false)}
        initialDate={showDateModal ? (() => {
          const parsed = parseTaskDateForModal(selectedTask?.end_date);
          return parsed.date;
        })() : '04/12/2025'}
        initialTime={showDateModal ? (() => {
          const parsed = parseTaskDateForModal(selectedTask?.end_date);
          return parsed.time;
        })() : '10:00'}
        initializePeriod={showDateModal ? (() => {
          const parsed = parseTaskDateForModal(selectedTask?.end_date);
          return parsed.period;
        })() : 'AM'}
        workflowId={getWorkflowIdFromUrl()}
        taskId={selectedTask?.id}
        onSave={(date, time) => {
          if (selectedWorkflow) {
            // Set the end_date to show exactly what user selected (no timezone conversion)
            const displayDate = `${date} ${time}`;
            
            const updatedWorkflow = {
              ...selectedWorkflow,
              end_date: displayDate,
            };

            if (selectedTask) {
              const updatedTasks = updatedWorkflow.tasks.map(task =>
                task.id === selectedTask.id ? { ...task, end_date: displayDate } : task
              );
              updatedWorkflow.tasks = updatedTasks;

              setSelectedTask({ ...selectedTask, end_date: displayDate });
            }

            setSelectedWorkflow(updatedWorkflow);
          }
        }}
      />

      {/* Access Denied Modal */}
      {showAccessDeniedModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                <svg
                  className="w-6 h-6 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Access Denied</h3>
                <p className="text-sm text-gray-600">
                  You don&rsquo;t have permission to access this task.
                </p>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-gray-700">
                This task is assigned to specific users or groups. Please contact your administrator
                if you believe you should have access to this task.
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowAccessDeniedModal(false);
                  // Redirect to workflow list or dashboard
                  router.push('/all-work-flow?type=new-court-notice');
                }}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
              >
                Go Back
              </button>
              <button
                onClick={() => {
                  setShowAccessDeniedModal(false);
                  // Try to refresh access check
                  if (selectedTask?.id && work_flow_id) {
                    // checkUserAccess(selectedTask.id, work_flow_id as string);
                  }
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
