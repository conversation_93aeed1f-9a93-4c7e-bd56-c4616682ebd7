import React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/router';

/**
 * AdvisorPanel Component
 * Displays the AI advisor panel with side navigation
 */
interface AdvisorPanelProps {
  emailInfo?: {
    email_from?: string;
    email_to?: string;
    email_subject?: string;
    email_body?: string;
    document?: Array<{
      name: string;
      size: string;
    }>;
  };
  noticeSummary?: Record<string, Record<string, number>>;
  panelRef?: React.Ref<HTMLDivElement>;
  style?: React.CSSProperties;
  className?: string;
  screenSize?: 'sm' | 'md' | 'lg' | 'xl' | 'xl-plus' | '2xl' | '3xl' | '4xl' | '5xl'; // Add screenSize prop
  onClose?: () => void; // Add onClose prop for overlay close functionality
}

interface DocumentType {
  name: string;
  size: string;
}

const AdvisorPanel: React.FC<AdvisorPanelProps> = ({
  emailInfo,
  noticeSummary,
  panelRef,
  style,
  screenSize = 'xl', // Default to xl if not provided
  onClose,
}) => {
  const router = useRouter();
  const { taskId } = router.query;

  /**
   * Helper function to get notice summary data for the current active task only
   * Takes a nested structure and returns events only for the current task ID from URL
   * 
   * @param noticeSummary - Object with task IDs as keys, containing person names and counts
   * @param currentTaskId - The current task ID from URL query parameters
   * @returns Events for current task only, or null if no data
   * 
   * @example
   * Input: noticeSummary = {
   *   "task1": { "John Doe": 2, "Jane Smith": 1 },
   *   "task2": { "John Doe": 3, "Bob Johnson": 1 }
   * }, currentTaskId = "task1"
   * Output: { "John Doe": 2, "Jane Smith": 1 }
   */
  const getCurrentTaskEvents = (
    noticeSummary?: Record<string, Record<string, number>>,
    currentTaskId?: string | string[]
  ): Record<string, number> | null => {
    if (!noticeSummary || !currentTaskId) return null;

    // Handle case where currentTaskId might be an array (from Next.js router)
    const taskIdString = Array.isArray(currentTaskId) ? currentTaskId[0] : currentTaskId;

    // Get events for the specific task
    const taskEvents = noticeSummary[taskIdString];

    if (!taskEvents || typeof taskEvents !== 'object') return null;

    const validatedEvents: Record<string, number> = {};

    // Validate and clean the events data
    Object.entries(taskEvents).forEach(([personName, count]) => {
      // Skip if count is not a valid number
      if (typeof count !== 'number' || count < 0) return;

      // Trim whitespace from person name for consistency
      const cleanName = personName.trim();
      if (!cleanName) return; // Skip empty names

      validatedEvents[cleanName] = count;
    });

    // Return null if no valid data was found
    return Object.keys(validatedEvents).length > 0 ? validatedEvents : null;
  };

  // Get events for the current task only
  const currentTaskEvents = getCurrentTaskEvents(noticeSummary, taskId);

  return (
    <div
      ref={panelRef}
      className={`flex flex-col w-full bg-white border-[#DCE2EB] ${screenSize === '2xl' ? 'text-base' : ''}`}
      style={style}
    >
      {/* Header Section - Enhanced for large screens */}
      <div className={`p-4 flex justify-between items-center ${screenSize === '2xl' ? 'p-6' : ''}`}>
        <h2 className={`font-medium text-gray-800 ${screenSize === '2xl' ? 'text-base' : ''}`}>Personal Advisor</h2>
        <button 
          className={`p-2 cursor-pointer text-[#5F6F84] hover:text-gray-700 hover:bg-[#F3F5F9] rounded-full transition ${screenSize === '2xl' ? 'p-3' : ''}`}
          onClick={onClose}
        >
          <svg
            width={screenSize === '2xl' ? "20" : "16"}
            height={screenSize === '2xl' ? "20" : "16"}
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      {/* Chat Messages - AI Generated - Enhanced for large screens */}
      <div className={`px-4 py-4 space-y-4 text-[#2A2E34] flex-1 overflow-y-auto ${screenSize === '2xl' ? 'px-8 py-6 space-y-6' : screenSize === 'xl' ? 'px-6 py-5 space-y-5' : screenSize === 'lg' ? 'px-5 py-4 space-y-4' : 'px-4 py-4 space-y-4'}`}>
        {/* First message - Introduction */}
        <div className="flex">
          <div className="flex self-end mr-2">
            <Image
              src="/assets/ai-robot-new-2.svg"
              alt="Bot"
              width={screenSize === '2xl' ? 24 : 24}
              height={screenSize === '2xl' ? 24 : 24}
            />
          </div>
          <div className="flex-1 max-w-[85%]">
            <div className={`talk-bubble-green tri-right-green round btm-left ${screenSize === '2xl' ? 'scale-110 origin-left' : ''}`}>
              <div className="talktext">
                <p className={`text-gray-800 leading-relaxed ${screenSize === '2xl' ? 'text-base' : 'text-sm'}`} style={{ color: '#2a2e34' }}>
                  The following court notice workflow below was processed by AI. Please review.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Second message - Email preview */}
        <div className="flex">
          <div className="flex self-end mr-2">
            <Image
              src="/assets/ai-robot-new-2.svg"
              alt="Bot"
              width={screenSize === '2xl' ? 24 : 24}
              height={screenSize === '2xl' ? 24 : 24}
            />
          </div>
          <div className="flex-1 max-w-[85%]">
            <div
              className={` talk-bubble-green tri-right-green-third round btm-left ${screenSize === '2xl' ? 'p-6 scale-110 origin-left' : 'p-4'}`}
              style={{ backgroundColor: '#f3f5f9' }}
            >
              <div className={`talktext-third text-gray-800 ${screenSize === '2xl' ? 'text-base' : 'text-sm'}`}>
                <p
                  className={`font-medium text-gray-800 ${screenSize === '2xl' ? 'mb-6 text-lg' : 'mb-2'}`}
                  style={{ color: '#2a2e34', marginBottom: screenSize === '2xl' ? '24px' : '20px', fontWeight: '500' }}
                >
                  Court Notice Email:
                </p>
                <p className={`${screenSize === '2xl' ? 'mb-3' : 'mb-2'}`} style={{ color: '#2a2e34' }}>
                  From: {emailInfo?.email_from || '<EMAIL>'}
                </p>
                <p className={`${screenSize === '2xl' ? 'mb-3' : 'mb-2'}`} style={{ color: '#2a2e34' }}>
                  To: {emailInfo?.email_to || '<EMAIL>'}
                </p>
                <p className={`${screenSize === '2xl' ? 'mb-6' : 'mb-2'}`} style={{ color: '#2a2e34', marginBottom: screenSize === '2xl' ? '24px' : '20px' }}>
                  Subject: {emailInfo?.email_subject || 'Case Management Order (CMO)'}
                </p>
                <div className={`whitespace-pre-line ${screenSize === '2xl' ? 'mb-[10px] leading-relaxed' : 'mb-2'}`}>
                  {emailInfo?.email_body ||
                    'Good Afternoon, please see attached notices.\nThank you,\nPaul Smith\nSuperior Court of NJ'}
                </div>

                {/* Attachment section - Enhanced for large screens */}
                {emailInfo?.document && emailInfo.document.length > 0 ? (
                  emailInfo.document.map((doc: DocumentType, index: number) => (
                    <div
                      key={index}
                      onClick={() => window.open('/NoticeofHearing.pdf', '_blank')}
                      className={`border w-fit bg-white border-gray-200 cursor-pointer rounded-xl flex items-center ${screenSize === '2xl' ? 'mt-4 p-4' : 'mt-3 p-3'}`}
                      style={{ color: '#2a2e34' }}
                    >
                      <svg
                        className={`text-red-500 mr-2 ${screenSize === '2xl' ? 'w-6 h-6' : 'w-5 h-5'}`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <div className="">
                        <p className={`font-medium ${screenSize === '2xl' ? 'text-sm' : 'text-xs'}`} style={{ color: '#2a2e34' }}>
                          {doc.name || 'Court notice.pdf'}
                        </p>
                        <p className={`${screenSize === '2xl' ? 'text-sm' : 'text-xs'}`} style={{ color: '#2a2e34' }}>
                          {doc.size || '1.4 MB'}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div
                    onClick={() => window.open('/NoticeofHearing.pdf', '_blank')}
                    className={`border w-fit border-gray-200 rounded-xl bg-white cursor-pointer flex items-center ${screenSize === '2xl' ? 'mt-4 p-4' : 'mt-3 p-3'}`}
                  >
                    <Image
                      src="/assets/pdf-filled.svg"
                      alt="PDF"
                      width={screenSize === '2xl' ? 36 : 30}
                      height={screenSize === '2xl' ? 36 : 30}
                      className="mr-2"
                    />
                    <div>
                      <p className={`font-medium ${screenSize === '2xl' ? 'text-sm' : 'text-xs'}`} style={{ color: '#2a2e34' }}>
                        Court notice.pdf
                      </p>
                      <p className={`text-gray-500 ${screenSize === '2xl' ? 'text-sm' : 'text-xs'}`} style={{ color: '#5F6F84' }}>
                        1.4 MB
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Third message - Notice Summary */}
        <div className="flex">
          <div className="flex self-end mr-2">
            <Image
              src="/assets/ai-robot-new-2.svg"
              alt="Bot"
              width={screenSize === '2xl' ? 24 : 24}
              height={screenSize === '2xl' ? 24 : 24}
            />
          </div>
          <div className="flex-1 max-w-[85%]">
            <div
              className={`talk-bubble-green tri-right-green-third round btm-left ${screenSize === '2xl' ? 'scale-110 origin-left' : ''}`}
              style={{ backgroundColor: '#f3f5f9' }}
            >
              <div className={`talktext ${screenSize === '2xl' ? 'text-base' : 'text-sm'}`}>
                <p className={`font-medium ${screenSize === '2xl' ? 'mb-[16px] text-lg' : 'mb-[16px]'}`} style={{ color: '#2a2e34', fontWeight: '500' }}>
                  Notice Summary:
                </p>
                {/* {taskId && (
                  <p
                    className="text-xs mb-2 text-gray-500"
                    style={{ color: '#6B7280', fontSize: '11px', marginBottom: '8px' }}
                  >
                    Task ID: {Array.isArray(taskId) ? taskId[0] : taskId}
                  </p>
                )} */}
                <p
                  className={`text-gray-800 ${screenSize === '2xl' ? 'mb-3 mt-6' : 'mb-1 mt-5'}`}
                  style={{ color: '#2a2e34', marginBottom: screenSize === '2xl' ? '12px' : '10px' }}
                >
                  Scheduled events
                </p>
                <ul className={`list-disc pl-5 ${screenSize === '2xl' ? 'space-y-3' : 'space-y-2'}`}>
                  {currentTaskEvents ? (
                    Object.entries(currentTaskEvents).map(([name, count], index) => (
                      <li key={index} className={screenSize === '2xl' ? 'text-base' : ''} style={{ color: '#2a2e34' }}>
                        {name} - {count} {count === 1 ? 'Event' : 'Events'}
                      </li>
                    ))
                  ) : (
                    <li className={screenSize === '2xl' ? 'text-base' : ''} style={{ color: '#2a2e34' }}>No events found for current task</li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Input Section */}
      {/* <div className="p-4 bg-[#F3F5F9] border-gray-200 mt-auto">
        <div className="relative flex items-center">
          <input
            type="text"
            placeholder="Write your prompt here..."
            className="w-full border border-[#DCE2EB] bg-white rounded-[12px] py-2 px-3 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={prompt}
            onChange={e => setPrompt(e.target.value)}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                handleSendPrompt();
              }
            }}
          />
          <button
            className="absolute right-3 top-1/2 cursor-pointer transform -translate-y-1/2 text-blue-[#3F73F6]"
            onClick={handleSendPrompt}
          >
            <Image src="/IconsBar/ai-blue.svg" alt="Send" width={20} height={20} />
          </button>
        </div>
      </div> */}

      <style>{`
        
        .talk-bubble-green,
        .talk-bubble-green-instruction {
          margin: 0 0 6px;
          display: inline-block;
          position: relative;
          width: auto;
          min-width: ${screenSize === '2xl' ? '240px' : '200px'};
          height: auto;
          border-radius: 12px;
          border-bottom-left-radius: 0px;
        }

        .talk-bubble-green {
          background-color: #8CF1BD;
        }

        .talk-bubble-green-instruction {
          background-color: #f3f5f9;
        }

        /* Success message triangle override */
        .talk-bubble-green[style*="background-color: #8cf1bd"] .tri-right-green.btm-left:after {
          border-color: transparent transparent transparent #8cf1bd;
        }


        .talk-bubble-green-instruction .talktext p {
          color: #2a2e34;
        }

        .talk-bubble-green-instruction .talktext-third p {
          color: #2a2e34;
        }

        /* Success message triangle override */
        .talk-bubble-green[style*="background-color: #8cf1bd"] .tri-right-green-third.btm-left:after {
          border-color: transparent transparent transparent #8cf1bd;
        }

        /* Bubble triangle */
        .tri-right-green.btm-left:after,
        .tri-right-green-instruction.btm-left:after {
          content: ' ';
          position: absolute;
          width: 0;
          height: 0;
          left: 0px;
          bottom: -6px;
          border: 6px solid;
        }

        .tri-right-green.btm-left:after {
          border-color: transparent transparent transparent #8CF1BD;
        }

        .tri-right-green-instruction.btm-left:after {
          border-color: transparent transparent transparent #f3f5f9;
        }


        .talktext {
          padding: ${screenSize === '2xl' ? '12px 18px' : '8px 14px'};
          text-align: left;
        }

        .talktext p {
          margin: 0;
          font-size: ${screenSize === '2xl' ? '16px' : '14px'};
          font-weight: normal;
          color: #fff;
        }

        ///////////////////

        .talktext-third {
          padding: ${screenSize === '2xl' ? '16px 20px' : '12px 16px'};
          text-align: left;
          line-height: ${screenSize === '2xl' ? '24px' : '20px'};
        }

        .talktext-third p {
          margin: 0;
          font-size: ${screenSize === '2xl' ? '16px' : '14px'};
          font-weight: normal;
          color: #fff;
        }




        /* Bubble triangle */
        .tri-right-green-third.btm-left:after,
        .tri-right-green-third-instruction.btm-left:after {
          content: ' ';
          position: absolute;
          width: 0;
          height: 0;
          left: 0px;
          bottom: -6px;
          border: 6px solid;
        }

        .tri-right-green-third.btm-left:after {
          border-color: transparent transparent transparent #f3f5f9;
        }

        .tri-right-green-third-instruction.btm-left:after {
          border-color: transparent transparent transparent #f3f5f9;
        }



      `}</style>
    </div>
  );
};

export default AdvisorPanel; 