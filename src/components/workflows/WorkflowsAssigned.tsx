import React, { useState, useEffect, useRef } from 'react';
// import { workflowService } from '@/services/api';
import courtNoticeService from '@/services/api/courtNoticeService';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { formatDate } from '@/utils/dateUtils';
import { DataTable, PageHeader, StatusBadge, Column } from '@/components/common';
import { Workflow } from '@/types/workflow';
import { CourtNoticeItem } from '@/types/courtNotice';
// import Image from 'next/image';
import { mapStatusVariant } from '@/types/courtNotice';
import { useRouter } from 'next/router';
import SaveViewModal from '@/components/common/SaveViewModal';
import { UserViewService, UserView } from '@/services/userViewService';
import { toast } from 'react-hot-toast';
import { formatWorkflowName } from '@/utils/format';

interface WorkflowsAssignedProps {
  _onNameClick: (id: string) => void;
  savedView?: UserView;
  viewTitle?: string;
}

const WorkflowsAssigned: React.FC<WorkflowsAssignedProps> = ({
  _onNameClick,
  savedView,
  viewTitle,
}) => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [workflows, setWorkflows] = useState<(Workflow | CourtNoticeItem)[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [selectedWorkflows, setSelectedWorkflows] = useState<string[]>([]);
  const [showSaveViewModal, setShowSaveViewModal] = useState(false);
  const [savingView, setSavingView] = useState(false);
  const [currentVisibleColumns, setCurrentVisibleColumns] = useState<string[]>([]);
  const [hasColumnsChanged, setHasColumnsChanged] = useState(false);
  const [originalColumns, setOriginalColumns] = useState<string[]>([]);
  const [currentFilters, setCurrentFilters] = useState<
    Array<{
      fieldName: string;
      filter: string;
      value: string[];
    }>
  >([]);

  // Reset filters to default when component mounts or when navigating
  useEffect(() => {
    // Clear any persisted currentFilters on component mount or navigation
    setCurrentFilters([]);

    // Also clear any filters persisted in localStorage from other court notice types
    if (typeof window !== 'undefined') {
      // Clear any stored filters from other components
      localStorage.removeItem('courtNoticeFilters');
      console.log('🔄 WorkflowsAssigned: Cleared filters on mount/navigation');
    }
  }, []); // Empty dependency array ensures this runs only on mount

  // Additional effect to handle URL navigation between court notice types
  useEffect(() => {
    const handleUrlChange = () => {
      setCurrentFilters([]);
      console.log('🔄 WorkflowsAssigned: Reset filters due to URL change');
    };

    // Listen for route changes
    if (typeof window !== 'undefined') {
      window.addEventListener('popstate', handleUrlChange);
      return () => window.removeEventListener('popstate', handleUrlChange);
    }
  }, []);
  const [hasFiltersChanged, setHasFiltersChanged] = useState(false);
  const [originalFilters, setOriginalFilters] = useState<
    Array<{
      fieldName: string;
      filter: string;
      value: string[];
    }>
  >([]);
  const limit = 10;

  // Add ref to track the latest request ID for deduplication
  const requestIdRef = useRef(0);

  const fetchWorkflows = async (pageNum: number) => {
    // Generate a unique request ID for this call
    const currentRequestId = ++requestIdRef.current;

    try {
      setLoading(true);

      // Always use court-notice-list API to support template filtering
      console.log('Saved view detected:', savedView);

      // Determine the base type from saved view or default to my_workflow
      const workflowType = savedView?.type || 'my_work_flow';

      // Map the type to court notice API types
      const typeMapping: Record<string, string> = {
        my_work_flow: 'new-court-notice', // Use new-court-notice for my_workflow
        new_court_notice: 'new-court-notice',
        completed: 'completed',
        follow_up: 'follow-up',
        archive: 'archived',
      };

      let apiType = typeMapping[workflowType] || 'new-court-notice';

      // Check if there's a template filter that should override the API type
      // Template filter always takes priority over URL type or saved view type
      const templateFilter = currentFilters?.find(
        filter =>
          (filter.fieldName === 'templates' || filter.fieldName === 'work_flow_runner') &&
          filter.value &&
          filter.value.length > 0
      );

      if (templateFilter) {
        // Map template filter values to API types
        if (templateFilter.value.includes('New Court Notice')) {
          apiType = 'new-court-notice';
        } else if (templateFilter.value.includes('Court Notice Follow Up')) {
          apiType = 'court-notice-follow-up';
        }
        console.log('Template filter detected, overriding type to:', apiType);
      }

      // Remove template filter from the filters array since it's only used for type determination
      const filtersWithoutTemplate =
        currentFilters?.filter(
          filter => !(filter.fieldName === 'templates' || filter.fieldName === 'work_flow_runner')
        ) || [];

      const payload = {
        page: pageNum,
        limit: limit,
        type: apiType,
        view: 'View_1', // Always use View_1 for now to ensure compatibility
        filter: filtersWithoutTemplate, // Pass filters without template filter
      };

      console.log('Applied filters in payload:', currentFilters);
      console.log('Court Notice API Payload:', payload);

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const response: any = await courtNoticeService.getCourtNoticeList(payload);
      console.log('Court Notice API Response received');

      // Check if this is still the latest request - if not, ignore the response
      if (currentRequestId !== requestIdRef.current) {
        console.log('Ignoring outdated API response, request ID:', currentRequestId);
        return;
      }

      // Check if response was successful
      if (response?.statusCode === 200 || response?.statusCode === 201) {
        // Handle multiple possible response structures (fallback pattern)
        const nestedResponse = response;
        const newWorkflows = nestedResponse.data?.data?.fields || response.data?.fields || [];
        console.log('Extracted workflows count:', newWorkflows.length);

        // Double-check this is still the latest request before updating state
        if (currentRequestId === requestIdRef.current) {
          // If it's the first page, replace the workflows
          // Otherwise append the new workflows to the existing ones
          if (pageNum === 1) {
            setWorkflows(newWorkflows);
          } else {
            setWorkflows(prev => [...(prev || []), ...newWorkflows]);
          }

          setHasMore(newWorkflows.length === limit);
        } else {
          console.log(
            'Ignoring outdated API response during state update, request ID:',
            currentRequestId
          );
        }
      } else {
        console.error('API returned non-success status:', response?.statusCode);
        if (currentRequestId === requestIdRef.current) {
          setWorkflows([]);
          setHasMore(false);
        }
      }
    } catch (error) {
      // Only log error if this is still the latest request
      if (currentRequestId === requestIdRef.current) {
        console.error('Error fetching workflows:', error);
      } else {
        console.log('Ignoring error from outdated request, request ID:', currentRequestId);
      }
    } finally {
      // Only update loading state if this is still the latest request
      if (currentRequestId === requestIdRef.current) {
        setLoading(false);
      }
    }
  };

  // Reset and fetch initial data when component mounts or saved view changes
  useEffect(() => {
    setPage(1);
    setWorkflows([]);

    // Clear search term initially for saved views to show all results
    setSearchTerm('');

    // If there's a saved view, initialize search term from saved filters
    // if (savedView?.filters?.search && typeof savedView.filters.search === 'string') {
    //   setSearchTerm(savedView.filters.search);
    // }

    // Initialize visible columns based on saved view
    if (savedView?.columns && Array.isArray(savedView.columns)) {
      const savedColumnNames = savedView.columns
        .filter(col => col.visible !== false)
        .map(col => col.FieldName);

      // Check if this saved view had no columns selected
      if (
        savedColumnNames.length === 0 ||
        (savedView && 'noColumnsSelected' in savedView && savedView.noColumnsSelected)
      ) {
        console.log('🚀 Saved view has no columns selected');
        setCurrentVisibleColumns([]);
        setOriginalColumns([]);
      } else {
        // Normalize saved column names to match TableActions expected format
        const normalizeFieldName = (name: string) => {
          const normalized = name.toLowerCase().trim();
          // Handle common variations and map to standard names
          if (normalized === 'started') return 'Started';
          if (normalized === 'due on') return 'Due On';
          if (normalized === 'task completed') return 'Task Completed';
          if (normalized === 'status') return 'Status';
          if (normalized === 'activity') return 'Activity';
          if (normalized === 'assignee') return 'Assignee';
          if (normalized === 'name') return 'Name';
          if (normalized === 'workflow name') return 'Name'; // Map Workflow Name to Name
          if (normalized === 'matter') return 'Matter';
          if (normalized === 'view') return 'View';
          if (normalized === 'attorney') return 'Attorney';
          // Remove notes mapping since we removed the column
          // Return as-is if no mapping found
          return name;
        };

        const normalizedColumnNames = savedColumnNames
          .map(normalizeFieldName)
          .filter(name => name !== 'Notes'); // Filter out Notes column if it exists in saved data
        setCurrentVisibleColumns(normalizedColumnNames);
        setOriginalColumns(normalizedColumnNames); // Store original for comparison
      }
    } else {
      // Default visible columns if no saved view
      setCurrentVisibleColumns([
        'Name',
        'Started',
        'Due On',
        'Task Completed',
        'Status',
        'Activity',
        'Assignee',
      ]);
    }

    // Initialize filters based on saved view
    if (savedView?.filter && Array.isArray(savedView.filter)) {
      const savedFilters = savedView.filter.map(filter => ({
        fieldName: filter.fieldName,
        filter: filter.filter,
        value: filter.value,
      }));
      setCurrentFilters(savedFilters);
      setOriginalFilters(savedFilters); // Store original for comparison
    } else {
      // Reset filters if no saved view
      setCurrentFilters([]);
      setOriginalFilters([]);
    }

    fetchWorkflows(1);
  }, [savedView]);

  // Fetch more data when page changes or filters change
  useEffect(() => {
    if (page > 1) {
      fetchWorkflows(page);
    }
  }, [page]);

  // Re-fetch data when filters change
  useEffect(() => {
    console.log('Filters changed, re-fetching data:', currentFilters);
    setPage(1); // Reset to page 1 when filters change
    setWorkflows([]); // Clear existing workflows
    fetchWorkflows(1);
  }, [currentFilters]);

  // Filter workflows based on search term
  const filteredWorkflows = (workflows || []).filter(workflow => {
    // If no search term, show all workflows
    if (!searchTerm || searchTerm.trim() === '') {
      return true;
    }

    // Handle CourtNoticeItem structure
    if ('template_name' in workflow) {
      const matches =
        workflow.template_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workflow.matter.toLowerCase().includes(searchTerm.toLowerCase());
      return matches;
    }
    // Handle Workflow structure
    else if ('work_flow_name' in workflow) {
      const matches =
        workflow.work_flow_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workflow.view.some((v: string) => v.toLowerCase().includes(searchTerm.toLowerCase()));
      return matches;
    }
    return false;
  });

  // Debug: Uncomment these lines if you need to debug filtering issues
  // console.log('Workflows state:', workflows);
  // console.log('Filtered workflows:', filteredWorkflows);
  // console.log('Search term:', searchTerm);

  const lastWorkflowElementRef = useInfiniteScroll({
    loading,
    hasMore,
    onLoadMore: () => setPage(prev => prev + 1),
  });

  const handleSelectWorkflow = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedWorkflows(prev => [...prev, id]);
    } else {
      setSelectedWorkflows(prev => prev.filter(wfId => wfId !== id));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected && filteredWorkflows.length > 0) {
      setSelectedWorkflows(
        filteredWorkflows
          .map(workflow => {
            // Handle different id fields for different data types
            if ('_id' in workflow) {
              return workflow._id;
            } else if ('id' in workflow) {
              return workflow.id;
            }
            return '';
          })
          .filter(id => id !== '')
      );
    } else {
      setSelectedWorkflows([]);
    }
  };

  const handleFilter = () => {
    console.log('Filter button clicked');
  };

  const handleColumns = (selectedColumns?: string[]) => {
    console.log('Columns button clicked', selectedColumns);
    if (selectedColumns !== undefined) {
      // Map "Workflow Name" to "Name" for internal column handling
      const mappedColumns = selectedColumns.map(col => {
        if (col === 'Workflow Name') return 'Name';
        return col;
      });
      // Use standard logic: selectedColumns = visible columns (can be empty array)
      setCurrentVisibleColumns(mappedColumns);

      // Check if columns have changed from original (only for saved views)
      if (savedView) {
        const columnsChanged =
          JSON.stringify([...mappedColumns].sort()) !== JSON.stringify([...originalColumns].sort());
        setHasColumnsChanged(columnsChanged);
      }
    }
  };

  // Helper function to check if field is a matter field
  const isMatterField = (field: string) => {
    const f = (field || '').toLowerCase();
    return f === 'matter' || f === 'matter_name' || f === 'matters';
  };

  const handleFilters = (
    filters: Array<{
      id: string;
      field: string;
      operator: string;
      value: string;
      selectedValues?: string[];
    }>
  ) => {
    console.log('Filters changed', filters);

    // Convert FilterRow format to backend format
    const backendFilters = filters
      .filter(
        f =>
          f.field &&
          f.operator &&
          (f.value || f.selectedValues?.length || f.operator === 'unassigned')
      )
      .map(f => {
        // Map frontend field names to backend field names
        const fieldMap: Record<string, string> = {
          'Workflow Name': 'template_name',
          Name: 'template_name', // Keep for backward compatibility
          Assignee: 'assigned_users',
          Status: 'status',
          Contact: 'contact',
          Matter: 'matter',
          Templates: 'templates',
          'Due date': 'due_date',
          'Create date': 'created_date',
          Attorney: 'attorney',
        };

        // Map frontend operators to backend operators
        const operatorMap: Record<string, string> = {
          contains: 'contains',
          equals: 'equal',
          not_contains: 'not_contains',
          not_equals: 'not_equal',
          unassigned: 'unassigned',
          before: 'before',
          after: 'after',
          between: 'between',
        };

        const fieldName = fieldMap[f.field] || f.field;
        const operator = operatorMap[f.operator] || f.operator;

        // Handle unassigned filter - no values needed
        let value: string[];
        if (f.operator === 'unassigned') {
          value = [];
        } else {
          const rawValues =
            f.selectedValues && f.selectedValues.length > 0
              ? f.selectedValues
              : f.value.split(', ').filter(v => v.trim());

          // Send full matter values for precise filtering (client|matter)
          if (isMatterField(f.field)) {
            value = rawValues; // Keep full values for precise filtering
          } else {
            value = rawValues;
          }
        }

        return {
          fieldName,
          filter: operator,
          value,
        };
      });

    // Final sanitization: keep matter field values as full values for precise filtering
    const sanitizedBackendFilters = backendFilters.map(filter => {
      if (isMatterField(filter.fieldName) && filter.value && Array.isArray(filter.value)) {
        return {
          ...filter,
          value: filter.value, // Keep full values (client|matter) for precise filtering
        };
      }
      return filter;
    });

    setCurrentFilters(sanitizedBackendFilters);

    // Check if filters have changed from original (only for saved views)
    if (savedView && originalFilters.length > 0) {
      const filtersChanged =
        JSON.stringify(
          sanitizedBackendFilters.sort((a, b) => a.fieldName.localeCompare(b.fieldName))
        ) !==
        JSON.stringify(originalFilters.sort((a, b) => a.fieldName.localeCompare(b.fieldName)));
      setHasFiltersChanged(filtersChanged);
    } else if (sanitizedBackendFilters.length > 0) {
      // If we have filters but no saved view, mark as changed
      setHasFiltersChanged(true);
    } else {
      setHasFiltersChanged(false);
    }
  };

  const handleSaveView = async () => {
    // If we're in a saved view and either columns or filters have changed, update directly without modal
    if (savedView && (hasColumnsChanged || hasFiltersChanged)) {
      await handleUpdateExistingView();
    } else {
      // Normal flow - open modal for new view
      setShowSaveViewModal(true);
    }
  };

  const handleUpdateExistingView = async () => {
    try {
      setSavingView(true);

      // Prepare updated view data
      const updatedViewData = {
        viewId: savedView?.id || '',
        name: savedView?.name,
        type: savedView?.type || 'my_work_flow',
        userId: savedView?.userId || 1,
        filters: {
          search: searchTerm,
          sortBy: '',
          sortOrder: 'asc',
        },
        columns: currentVisibleColumns.map(col => ({
          FieldName: col,
          searchable: true,
          sortable: true,
          visible: true,
        })),
        filter: currentFilters,
      };

      const response = await UserViewService.updateUserView(updatedViewData);

      if (response.data.success) {
        console.log('View updated successfully:', response.data.view);

        // Show success toast notification
        toast.success('View updated successfully');

        // Reset the changed flags
        setHasColumnsChanged(false);
        setHasFiltersChanged(false);
        // Update original state to new state
        setOriginalColumns([...currentVisibleColumns]);
        setOriginalFilters([...currentFilters]);

        // Refresh sidebar views if function exists
        const refreshSidebarViews = (window as unknown as { refreshSidebarViews?: () => void })
          .refreshSidebarViews;
        if (refreshSidebarViews) {
          refreshSidebarViews();
        }
      } else {
        console.error('Failed to update view:', response.data.message);
        // Show error toast
        toast.error('Failed to update view');
      }
    } catch (error) {
      console.error('Error updating view:', error);
      // Show error toast for network/API errors
      toast.error('Error updating view. Please try again.');
    } finally {
      setSavingView(false);
    }
  };

  const handleSaveViewSubmit = async (viewName: string) => {
    try {
      setSavingView(true);

      // Get current filters and columns state
      const viewData = {
        name: viewName,
        type: savedView?.type || 'my_work_flow', // Use saved view type or default to my_work_flow
        userId: savedView?.userId || 1, // Use saved view userId or default
        filters: {
          search: searchTerm,
          sortBy: '',
          sortOrder: 'asc',
        },
        columns: currentVisibleColumns.map(col => ({
          FieldName: col,
          searchable: true,
          sortable: true,
          visible: true,
        })),
        filter: currentFilters,
      };

      const response = await UserViewService.saveUserView(viewData);

      if (response.data.success) {
        toast.success('View saved successfully');
        setShowSaveViewModal(false);
      } else {
        toast.error('Failed to save view');
      }
    } catch (error) {
      console.error('Error saving view:', error);
      toast.error('Failed to save view');
    } finally {
      setSavingView(false);
    }
  };

  const handleRowClick = (rowData: Workflow | CourtNoticeItem) => {
    // Handle CourtNoticeItem structure
    if ('template_name' in rowData) {
      router.push(`/workflowrun?taskId=${rowData.last_task_id}&work_flow_id=${rowData._id}`);
    }
    // Handle Workflow structure
    else if ('work_flow_name' in rowData) {
      router.push(`/workflowrun?taskId=${rowData.latest_task_id}&work_flow_id=${rowData.id}`);
    }
  };

  // Define all available columns
  const allColumns: Column[] = [
    {
      id: 'name',
      header: 'Name',
      sortable: true,
      sortField: 'work_flow_runner',
      cell: (row: Workflow | CourtNoticeItem) => {
        const getName = () => {
          let clientNamesString = '';

          if ('work_flow_runner' in row) {
            clientNamesString = row.work_flow_runner || '';
          } else if ('work_flow_name' in row) {
            clientNamesString = row.work_flow_name || '';
          }

          // Parse comma-separated client names and apply intelligent formatting
          const clientNames = clientNamesString
            ? clientNamesString
                .split(',')
                .map(name => name.trim())
                .filter(Boolean)
            : [];

          return formatWorkflowName(clientNames, 60);
        };

        return (
          <div
            className="text-[14px] cursor-pointer text-[#2A2E34]"
            onClick={() => handleRowClick(row)}
            title={
              'work_flow_runner' in row
                ? row.work_flow_runner
                : 'work_flow_name' in row
                  ? row.work_flow_name
                  : ''
            } // Show full text on hover
          >
            <span>{getName()}</span>
          </div>
        );
      },
    },
    {
      id: 'started',
      header: 'Started',
      sortable: true,
      sortField: 'start_date',
      cell: (row: Workflow | CourtNoticeItem) => (
        <div
          className="text-[14px] text-[#5F6F84] cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          {formatDate(row.start_date)}
        </div>
      ),
    },
    {
      id: 'dueOn',
      header: 'Due On',
      sortable: true,
      sortField: 'end_date',
      cell: (row: Workflow | CourtNoticeItem) => (
        <div
          className="text-[14px] text-[#5F6F84] cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          {formatDate(row.end_date)}
        </div>
      ),
    },
    {
      id: 'taskCompleted',
      header: 'Task Completed',
      sortable: true,
      sortField: 'completed_tasks',
      cell: (row: Workflow | CourtNoticeItem) => {
        const getTaskCompleted = () => {
          if ('completed_tasks' in row) {
            return row.completed_tasks || '';
          } else if ('task_complete' in row) {
            return row.task_complete;
          }
          return '';
        };

        return (
          <div
            className="text-[14px] text-[#5F6F84] cursor-pointer"
            onClick={() => handleRowClick(row)}
          >
            {getTaskCompleted()}
          </div>
        );
      },
    },
    {
      id: 'status',
      header: 'Status',
      sortable: false,
      sortField: 'status',
      cell: (row: Workflow | CourtNoticeItem) => (
        <div onClick={() => handleRowClick(row)} className="cursor-pointer">
          <StatusBadge status={mapStatusVariant(row.status)} />
        </div>
      ),
    },
    {
      id: 'activity',
      header: 'Activity',
      sortable: true,
      sortField: 'last_activity',
      cell: (row: Workflow | CourtNoticeItem) => (
        <div className="text-[14px] text-[#5F6F84]">{row.last_activity}</div>
      ),
    },
    {
      id: 'assignee',
      header: 'Assignee',
      sortable: true,
      sortField: 'assigned_users',
      cell: (row: Workflow | CourtNoticeItem) => {
        const getInitials = (name: string) => {
          return name
            ?.split(' ')
            .map(word => word.charAt(0))
            .join('')
            ?.toUpperCase()
            ?.slice(0, 2);
        };

        const getAssignedUsers = () => {
          if ('assigned_users' in row) {
            // Handle null, undefined, empty string, or whitespace-only strings
            if (
              !row.assigned_users ||
              typeof row.assigned_users !== 'string' ||
              row.assigned_users.trim() === ''
            ) {
              return [];
            }
            return row.assigned_users
              .split(',')
              .map(s => s.trim())
              .filter(s => s.length > 0);
          }
          return [];
        };

        const assignedUsers = getAssignedUsers();
        const userCount = assignedUsers?.length || 0;
        const firstUser = assignedUsers[0];

        // Show AI robot icon when count is zero or no valid assignees
        if (userCount === 0 || !firstUser || firstUser.trim() === '') {
          return (
            <div
              className="w-[24px] h-[24px] cursor-pointer flex items-center justify-center"
              onClick={() => handleRowClick(row)}
            >
              <img src="/assets/ai-robot.svg" alt="AI Robot" className="w-[24px] h-[24px]" />
            </div>
          );
        }

        return (
          <div
            className="relative w-[24px] h-[24px] cursor-pointer"
            onClick={() => handleRowClick(row)}
          >
            <div className="w-[24px] h-[24px] rounded-full bg-[#5F6F84] text-white flex items-center justify-center font-semibold text-[10px]">
              {getInitials(firstUser)}
            </div>
            <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
              {userCount}
            </span>
          </div>
        );
      },
    },
    {
      id: 'matter',
      header: 'Matter',
      sortable: true,
      sortField: 'matter',
      cell: (row: Workflow | CourtNoticeItem) => {
        const getMatter = () => {
          if ('matter' in row) {
            return row.matter || '';
          }
          return '';
        };

        return (
          <div
            className="text-[14px] text-[#5F6F84] cursor-pointer"
            onClick={() => handleRowClick(row)}
          >
            {getMatter()}
          </div>
        );
      },
    },
    {
      id: 'view',
      header: 'View',
      sortable: true,
      sortField: 'work_flow_runner',
      cell: (row: Workflow | CourtNoticeItem) => {
        const getView = () => {
          // Check for the view field in different possible formats to match regular page behavior
          if ('isChild' in row && row.isChild) {
            return row.isChild; // Used in completed page
          } else if ('work_flow_runner' in row && row.work_flow_runner) {
            return row.work_flow_runner; // Used in regular page
          } else if ('work_flow_name' in row && row.work_flow_name) {
            return row.work_flow_name;
          } else if ('template_name' in row && row.template_name) {
            return row.template_name;
          }
          return 'New Court Notice'; // Default fallback
        };

        return (
          <div
            className="text-[14px] text-[#5F6F84] cursor-pointer"
            onClick={() => handleRowClick(row)}
          >
            {getView()}
          </div>
        );
      },
    },
    {
      id: 'attorney',
      header: 'Attorney',
      sortable: true,
      sortField: 'attorney',
      cell: (row: Workflow | CourtNoticeItem) => {
        const getAttorney = () => {
          if ('attorney' in row) {
            return row.attorney || '';
          }
          return '';
        };

        return (
          <div
            className="text-[14px] text-[#5F6F84] cursor-pointer"
            onClick={() => handleRowClick(row)}
          >
            {getAttorney()}
          </div>
        );
      },
    },
  ];

  // Filter columns based on current visible columns configuration
  const { columns, visibleColumnNames } = React.useMemo(() => {
    // If no columns are selected (empty array), return empty columns array
    if (currentVisibleColumns.length === 0) {
      console.log('🚀 No columns selected in saved view, returning empty array');
      return {
        columns: [],
        visibleColumnNames: [],
      };
    }

    if (currentVisibleColumns.length > 0) {
      // Get the field names from current visible columns
      const visibleColumnNames = currentVisibleColumns.map(col => col.toLowerCase());

      // Create a mapping for field name normalization
      const normalizeFieldName = (name: string) => {
        const normalized = name.toLowerCase().trim();
        // Handle common variations
        if (normalized === 'started') return 'started';
        if (normalized === 'due on') return 'dueon';
        if (normalized === 'task completed') return 'taskcompleted';
        if (normalized === 'status') return 'status';
        if (normalized === 'activity') return 'activity';
        if (normalized === 'assignee') return 'assignee';
        if (normalized === 'name') return 'name';
        if (normalized === 'workflow name') return 'name'; // Map Workflow Name to name
        if (normalized === 'matter') return 'matter';
        if (normalized === 'view') return 'view';
        if (normalized === 'attorney') return 'attorney';

        return normalized.replace(/\s+/g, '');
      };

      // Filter columns to only show those in current visible columns
      const filteredColumns = allColumns.filter(col => {
        const normalizedColumnId = normalizeFieldName(col.id);
        const normalizedColumnHeader = normalizeFieldName(col.header);

        return visibleColumnNames.some(visibleName => {
          const normalizedVisibleName = normalizeFieldName(visibleName);
          return (
            normalizedVisibleName === normalizedColumnId ||
            normalizedVisibleName === normalizedColumnHeader
          );
        });
      });

      // Get the actual column headers for the visible columns
      const actualVisibleNames = filteredColumns.map(col => col.header);

      // Map "Name" back to "Workflow Name" for TableActions component
      const mappedVisibleNames = actualVisibleNames.map(name => {
        if (name === 'Name') return 'Workflow Name';
        return name;
      });

      return {
        columns: filteredColumns,
        visibleColumnNames: mappedVisibleNames,
      };
    }

    // If no current visible columns set, return all columns
    const allVisibleNames = allColumns.map(col => col.header);

    // Map "Name" back to "Workflow Name" for TableActions component
    const mappedAllVisibleNames = allVisibleNames.map(name => {
      if (name === 'Name') return 'Workflow Name';
      return name;
    });

    return {
      columns: allColumns,
      visibleColumnNames: mappedAllVisibleNames,
    };
  }, [currentVisibleColumns, allColumns]);

  return (
    <div className="p-8 h-full">
      <PageHeader
        title={viewTitle || 'Workflows Assigned to Me'}
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        onFilter={handleFilter}
        onColumns={handleColumns}
        onSaveView={handleSaveView}
        showSaveView={true} // Always show save button
        disableSaveView={
          currentVisibleColumns.length === 0 || // Disable if no columns selected
          (savedView ? !(hasColumnsChanged || hasFiltersChanged) : false) // Enable if columns or filters changed in saved view
        }
        visibleColumns={visibleColumnNames} // Pass visible column names
        savedFilters={currentFilters} // Pass saved filters
        onFiltersChange={handleFilters} // Pass filter change handler
      />

      {currentVisibleColumns.length === 0 ? (
        // Show message when no columns are selected
        <div className="mt-8 border border-gray-200 rounded-lg overflow-hidden">
          <div className="bg-white py-12 text-center">
            <div className="text-gray-500 text-lg mb-2">No columns selected</div>
            <div className="text-gray-400 text-sm">
              Please select at least one column to view the data
            </div>
          </div>
        </div>
      ) : filteredWorkflows.length === 0 && !loading ? (
        // Show message when no data is found but columns are selected
        <div className="mt-8 border border-gray-200 rounded-lg overflow-hidden">
          <div className="bg-gray-50 border-b border-gray-200">
            <div className="flex items-center px-6 py-3">
              <input type="checkbox" disabled className="mr-4 opacity-50" />
              {columns.map((column, index) => (
                <div key={index} className="flex-1 text-sm font-medium text-gray-700 px-3">
                  {column.header}
                </div>
              ))}
            </div>
          </div>
          <div className="bg-white py-12 text-center">
            <div className="text-gray-500 text-lg">No record found</div>
          </div>
        </div>
      ) : (
        <DataTable
          columns={columns}
          data={filteredWorkflows}
          selectedIds={selectedWorkflows}
          onSelectRow={handleSelectWorkflow}
          onSelectAll={handleSelectAll}
          isAllSelected={
            selectedWorkflows.length === filteredWorkflows.length && filteredWorkflows.length > 0
          }
          isLoading={loading}
          lastItemRef={lastWorkflowElementRef}
          className="mt-8"
        />
      )}

      <SaveViewModal
        isOpen={showSaveViewModal}
        onClose={() => setShowSaveViewModal(false)}
        onSave={handleSaveViewSubmit}
        isLoading={savingView}
      />
    </div>
  );
};

export default WorkflowsAssigned;
