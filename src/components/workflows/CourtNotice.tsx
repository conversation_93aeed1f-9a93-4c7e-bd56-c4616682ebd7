import React, { useState, useEffect, useRef, useCallback } from 'react';
import { X, AlertTriangle, Calendar, Plus, ChevronDown } from 'lucide-react';
import { workflowStyles, conditionalDisabled } from '@/styles/workflow';
import CourtNoticeEditModal from './modals/CourtNoticeEditModal';
// import CourtNoticeAddModal from './modals/CourtNoticeAddModal';
import Image from 'next/image';
import { apiGet, apiPut, apiDelete, apiPost } from '@/services/api/apiUtils';
import ReusableAlertModal from '@/components/ui/ReusableAlertModal';
import { countTbdFields } from '@/types/courtNotice';
import debounce from 'lodash/debounce';
import moment from "moment-timezone";

// Define types for court notice data
export interface EventType {
  _id: string;
  id: string;
  caseNumber: string;
  clientName: string;
  description: string;
  matter_id?: string;
  selectedMatterId?: string;
  clientMatterId?: string;
  clientNamePart?: string;
  attendees?: string;
  notes?: string;
  attachments?: string[];
  files?: Array<{
    name: string;
    url?: string;
    type?: string;
    size?: number;
    key?: string;
    uniqueId?: string;
    isNameChanged?: boolean;
    originalName?: string; // Original file name before any renames
    newName?: string; // New file name after rename
  }>;
  deleteFiles?: Array<{
    name: string;
    key?: string;
  }>;

  // New fields for enhanced court notice functionality
  subject?: string;
  courtNoticeType?: string;
  court_notice_date?: string;
  appointmentAction?: string;
  eventStatus?: string;
  charge?: string;
  county?: string;
  courtLocation?: string;
  courtNoticeActions?: string;
  isAddSecondary?: boolean;

  // Updated date/time fields
  startDate?: string;
  endDate?: string;
  startTime: string;
  endTime: string;
  allDay?: boolean;
  allDayDeadLine?: boolean;

  // Attendees
  requiredAttendees?: string;
  optionalAttendees?: string;
  clientAttendance?: string;
  meetingLocation?: string;

  // Meeting details based on meeting location
  meetingLink?: string;
  phoneDetails?: string;
  meetingAddress?: string;

  // Legacy fields
  date?: string;
  isCompleted?: boolean;

  // Add new field to track TBD fields count
  tbdFieldsCount?: number;
  rescheduleAppointment?: string;
  appointmentToReschedule?: string;
  client_matter_id?: string;
  my_case_event_id?: string;
}

export interface Matter {
  id: string;
  name: string;
  caseDescription?: string;
  matter_id?: string;
  _id: string;
  client_name: string;
  case_number: string;
  ex_county_of_arrest: string;
  client_id: string;
  is_active: boolean;
  text: string;
  my_case_matter_id?: string;
}

export interface ClientMatter {
  id: string;
  name: string;
  matters: Matter[];
  newClient?: boolean;
}

/**
 * Normalize a client/matter display string by removing duplicated matter names in parentheses.
 * Examples:
 * - "JOE LEAL | DWI BAC >= 0.15 (DWI BAC >= 0.15)" → "JOE LEAL | DWI BAC >= 0.15"
 * - "DWI BAC >= 0.15 (DWI BAC >= 0.15)" → "DWI BAC >= 0.15"
 * - Leaves other formats untouched.
 *
 * @param raw - Original label string (may contain client, matter and parenthetical)
 * @returns Cleaned label string without redundant parenthetical duplication
 */


const formatClientMatterName = (raw: string): string => {
  if (!raw) return raw;

  const trimLower = (s: string) => s.trim().replace(/\s+/g, ' ').toLowerCase();

  // Case 1: Contains a client prefix separated by a pipe
  if (raw.includes('|')) {
    const [left, rightPart] = raw.split('|');
    const right = (rightPart || '').trim();

    const match = right.match(/^(.*?)\s*\((.*?)\)\s*$/);
    if (match) {
      const base = match[1] || '';
      const paren = match[2] || '';
      if (trimLower(base) === trimLower(paren)) {
        return `${left.trim()} | ${base.trim()}`;
      }
    }

    return `${left.trim()} | ${right}`;
  }

  // Case 2: No client prefix, just "Matter (Matter)"
  const onlyMatch = raw.match(/^(.*?)\s*\((.*?)\)\s*$/);
  if (onlyMatch) {
    const base = onlyMatch[1] || '';
    const paren = onlyMatch[2] || '';
    if (trimLower(base) === trimLower(paren)) {
      return base.trim();
    }
  }

  return raw;
};

const formatString = (input: string): string => {
  return input.replace(/\s*\((.*?)\)\s*$/, " | $1");
}

// Add new interface for API response type
export interface CourtNoticeApiData {
  clients: ClientMatter[];
  events: Record<string, EventType[]>;
}

export interface CourtNoticeProps {
  clients?: ClientMatter[];
  events?: Record<string, EventType[]>;
  onSave?: (matterId: string, events: EventType[], updatedEvent?: EventType) => void;
  onAddEvent?: (matterId: string) => void;
  onDeleteEvent?: (matterId: string, eventId: string) => void;
  onDeleteMatter?: (matterId: string) => void;
  onEditEvent?: (matterId: string, eventId: string, event: EventType) => void;
  onAddContact?: (status: boolean) => void;
  onDeleteClient?: (clientId: string) => void;
  onClientsUpdate?: (updatedClients: ClientMatter[]) => void;
  label?: string;
  fieldId?: string;
  workflowId?: string;
  isDisabled?: boolean;
  isUpdateMyCase?: boolean;
  isChildWorkflow?: boolean;
  user_group_id?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options?: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setClients?: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setEvents?: any;
  isTaskReviewed?: boolean;

}

const CourtNotice: React.FC<CourtNoticeProps> = ({
  clients = [],
  events = {},
  setClients,
  setEvents,
  onSave,
  onAddEvent,
  onDeleteEvent,
  onDeleteMatter,
  onEditEvent,
  onAddContact,
  onDeleteClient,
  onClientsUpdate,
  // label = 'Review the court notice and ensure all the dates are correct.',
  fieldId,
  workflowId,
  isDisabled,
  options,
  isChildWorkflow,
  isUpdateMyCase,
  isTaskReviewed,

}) => {
  console.log('🚀 ~ workflowId:789', events);
  // State for managing clients and their events
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [currentEditEvent, setCurrentEditEvent] = useState<EventType | null>(null);
  const [currentMatterId, setCurrentMatterId] = useState<string>('');
  const [currentClientId, setCurrentClientId] = useState<string>('');
  const [isPdfBeingViewed, setIsPdfBeingViewed] = useState(false);
  const [selectedMatters, setSelectedMatters] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [localOptions, setLocalOptions] = useState(options || null);
  const [confirmDeleteEventModalOpen, setConfirmDeleteEventModalOpen] = useState(false);
  const [confirmDeleteClientModalOpen, setConfirmDeleteClientModalOpen] = useState(false);
  const [confirmDeleteMatterModalOpen, setConfirmDeleteMatterModalOpen] = useState(false);
  const [pendingDeleteEventData, setPendingDeleteEventData] = useState<{
    matterId: string;
    eventId: string;
  } | null>(null);
  const [pendingDeleteClientId, setPendingDeleteClientId] = useState<string | null>(null);
  const [pendingDeleteMatterId, setPendingDeleteMatterId] = useState<string | null>(null);

  // States for searchable dropdown - updated to be client-specific
  const [clientSearchTerms, setClientSearchTerms] = useState<Record<string, string>>({});
  const [openDropdownClientId, setOpenDropdownClientId] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState(true); // Default to edit mode
  const [searchResults, setSearchResults] = useState<Matter[]>([]);
  const [allMatters, setAllMatters] = useState<Matter[]>([]); // Store all matters
  const [isSearching, setIsSearching] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const defaultMatter: Matter = {
    _id: '',
    name: '',
    client_id: '',
    is_active: false,
    client_name: '',
    text: '',
    id: '',
    ex_county_of_arrest: '',
    case_number: '',
  };

  const [selectedMatter, setSelectedMatter] = useState<Matter>(defaultMatter);

  // State to track client_name for each selected matter
  const [selectedMatterClientNames, setSelectedMatterClientNames] = useState<
    Record<string, string>
  >({});

  // New state for keyboard navigation
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);

  // State to store client/matter names array
  const [clientMatterNames, setClientMatterNames] = useState<string[]>([]);

  const allDisableButtons = true;

  const getUserTimezone = (): string => {
    if (typeof window === 'undefined') return 'UTC';
    return localStorage.getItem('timezone') || Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
  };

  // Modified fetch function to handle both initial load and search
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const fetchMatters = useCallback(
    async (query: string = '') => {
      try {
        setIsSearching(true);

        // Build API URL
        const params: Record<string, string> = {};
        if (query.trim()) {
          params.search = query;
        }

        const response = await apiGet<{
          statusCode: number;
          data?: {
            matters: Matter[];
          };
        }>(`/workflow/matter-list`, {
          params,
        });

        if (response.data?.statusCode === 200 && response.data?.data?.matters) {
          const matters = response.data.data.matters;

          // Don't filter at the global level - filtering will be done per client at display level
          // This ensures all matters are available for each dropdown to filter appropriately

          // If this is the initial load (no search term), store all matters
          if (!query.trim()) {
            setAllMatters(matters);
            setSearchResults(matters);
            setIsInitialLoad(true);
          } else {
            // For search results, update searchResults but keep allMatters unchanged
            setSearchResults(matters);
          }
        } else {
          if (!query.trim()) {
            setAllMatters([]);
          }
          setSearchResults([]);
        }
      } catch (error) {
        console.error('Error fetching matters:', error);
        if (!query.trim()) {
          setAllMatters([]);
        }
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    },
    [selectedMatters]
  ); // Add selectedMatters to dependencies

  // Add this useEffect to load persisted selections on mount
  useEffect(() => {
    const savedSelections = localStorage.getItem('selectedMatters');
    if (savedSelections) {
      try {
        const parsedSelections = JSON.parse(savedSelections);
        setSelectedMatters(parsedSelections);
      } catch (error) {
        console.error('Error parsing saved selectedMatters from localStorage:', error);
        localStorage.removeItem('selectedMatters');
      }
    }
  }, []);

  // Save selectedMatters to localStorage whenever it changes
  useEffect(() => {
    if (Object.keys(selectedMatters).length > 0) {
      localStorage.setItem('selectedMatters', JSON.stringify(selectedMatters));
    } else {
      // If no matters are selected, remove from localStorage
      localStorage.removeItem('selectedMatters');
    }
  }, [selectedMatters]);

  // Clear selectedMatters from localStorage on component unmount
  useEffect(() => {
    return () => {
      localStorage.removeItem('selectedMatters');
    };
  }, []);

  // Debounced search function for search queries
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (!query || query.length < 2) {
        // Show all matters when search is cleared
        setSearchResults(allMatters);
        setIsSearching(false);
        return;
      }

      fetchMatters(query);
    }, 300),
    [fetchMatters, allMatters]
  );

  // Load all matters when dropdown opens for the first time
  useEffect(() => {
    if (openDropdownClientId && !isInitialLoad && allMatters.length === 0) {
      fetchMatters(''); // Fetch all matters without search term
    }
  }, [openDropdownClientId, isInitialLoad, allMatters.length, fetchMatters]);

  // Trigger search when search term changes for the currently open dropdown
  useEffect(() => {
    if (openDropdownClientId && isInitialLoad) {
      const searchTerm = clientSearchTerms[openDropdownClientId] || '';
      if (searchTerm && searchTerm.length >= 2) {
        debouncedSearch(searchTerm);
      } else {
        // Show all matters when no search term or search term is too short
        setSearchResults(allMatters);
      }
    }
  }, [clientSearchTerms, openDropdownClientId, debouncedSearch, allMatters, isInitialLoad]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpenDropdownClientId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Add a ref to track processed rescheduled events to prevent infinite loops
  const processedRescheduledEvents = useRef<Set<string>>(new Set());

  // Cleanup processed events on unmount
  useEffect(() => {
    return () => {
      processedRescheduledEvents.current.clear();
    };
  }, []);

  // Handle window visibility change to prevent modal from closing when returning from PDF view
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden' && editModalOpen) {
        // When page becomes hidden and modal is open, user might be viewing PDF
        console.log('Page became hidden with modal open - user might be viewing PDF');
        setIsPdfBeingViewed(true);
      } else if (document.visibilityState === 'visible' && isPdfBeingViewed && editModalOpen) {
        // When page becomes visible again and modal was open, ensure it stays open
        console.log('Page became visible - ensuring modal stays open after PDF view');
        setTimeout(() => {
          setIsPdfBeingViewed(false);
          console.log('PDF viewing flag cleared');
        }, 1500); // Longer delay to ensure stability
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [editModalOpen, isPdfBeingViewed]);

  // Track if we've already fetched initial data
  const [initialDataFetched, setInitialDataFetched] = useState(false);

  // Track the last time a contact was added to force rerender
  const [lastContactAdded, setLastContactAdded] = useState<number>(0);

  // Track if the first contact has been added
  const [firstContactAdded, setFirstContactAdded] = useState(false);

  // Ref to track whether clients have been initialized from props
  const clientsInitialized = useRef(false);

  // Initialize client data and handle new clients
  useEffect(() => {
    if (clients?.length > 0) {
      // After clients are added (either initially or via add contact), set firstContactAdded
      if (!firstContactAdded && clients.length > 0) {
        setFirstContactAdded(true);
        console.log('Setting firstContactAdded=true from clients useEffect');
      }

      // Update selected matters for any new clients
      setSelectedMatters(prev => {
        const updatedSelectedMatters = { ...prev };
        let hasChanges = false;

        // Set default selected matter for each client that doesn't have one yet
        clients.forEach(client => {
          // Only set if not already set
          if (updatedSelectedMatters[client.id] === undefined && client.matters.length > 0) {
            // Don't auto-select for clients added via "Add event to another contact"
            if (client.newClient) {
              updatedSelectedMatters[client.id] = '';
            } else {
              updatedSelectedMatters[client.id] = client.matters[0].id;
            }
            hasChanges = true;
          }
        });

        // Remove entries for clients that no longer exist
        Object.keys(updatedSelectedMatters).forEach(clientId => {
          if (!clients.some(client => client.id === clientId)) {
            delete updatedSelectedMatters[clientId];
            hasChanges = true;
          }
        });

        return hasChanges ? updatedSelectedMatters : prev;
      });
    }
  }, [clients, currentClientId, firstContactAdded]);

  // Initialize state from props only on mount (not on every prop change)
  useEffect(() => {
    // This effect should only run once to initialize from props
    if (clients?.length > 0 && !clientsInitialized.current && setClients) {
      // Only set clients once on mount if provided
      const initialClients = [...clients];
      clientsInitialized.current = true; // Mark as initialized to prevent future runs
      setClients(initialClients);
    }
  }, [setClients]); // Only depend on setClients, not clients

  // Add an effect to log and potentially handle new contact additions
  useEffect(() => {
    if (lastContactAdded > 0) {
      console.log(`Contact added at: ${new Date(lastContactAdded).toLocaleTimeString()}`);
      // You could do additional processing here if needed
    }
  }, [lastContactAdded]);

  // Add useEffect to handle fetching rescheduled events
  useEffect(() => {
    const fetchRescheduledEvents = async () => {
      try {
        const eventsToUpdate: Record<string, EventType[]> = {};
        let hasUpdates = false;

        // Check all events for rescheduled ones that need fetching
        for (const [matterId, matterEvents] of Object.entries(events)) {
          const updatedEvents = [...matterEvents];
          let matterHasUpdates = false;

          for (let i = 0; i < updatedEvents.length; i++) {
            const event = updatedEvents[i];
            
            // Check if this event needs rescheduled data
            // We need to fetch if:
            // 1. appointmentAction is 'Cancel' 
            // 2. appointmentToReschedule exists
            // 3. This specific event instance hasn't been processed yet OR the event doesn't have complete data
            const eventInstanceKey = `${event.id}-${event.appointmentToReschedule}`;
            if (
              event?.appointmentAction === 'Cancel' &&
              event?.appointmentToReschedule &&
              (!processedRescheduledEvents.current.has(eventInstanceKey) ||
               !event?.startDate || !event?.startTime || !event?.subject || event?.subject === 'Save court notice only')
            ) {
              try {
                const response = await apiGet<{
                  statusCode: number;
                  data?: EventType;
                }>(`/workflow/event/${event.appointmentToReschedule}`);
                
                if (response.data?.statusCode === 200 && response.data?.data) {
                  const result = response.data?.data;
                  
                  // For Cancel events, always use the rescheduled appointment's date/time and details
                  const originalId = event.id;
                  const originalAppointmentToReschedule = event.appointmentToReschedule;
                  const originalCourtNoticeType = event.courtNoticeType;
                  
                  // Preserve the original appointmentToReschedule value and set appointmentAction
                  result.appointmentAction = 'Cancel';
                  result.appointmentToReschedule = originalAppointmentToReschedule;
                  result.courtNoticeType = originalCourtNoticeType;
                  result.id = originalId;
                  
                  // For Cancel events, we want to show the rescheduled appointment's date/time
                  // So we don't preserve any original date/time data - we use what we fetched from the rescheduled event
                  
                  updatedEvents[i] = result;
                  matterHasUpdates = true;
                  hasUpdates = true;
                  
                  // Mark this specific event instance as processed
                  processedRescheduledEvents.current.add(eventInstanceKey);
                  console.log('✅ Retrieved rescheduled event with preserved Cancel event date/time:', response.data.data);
                }
              } catch (error) {
                console.error('❌ Error fetching rescheduled event:', error);
                // Mark as processed even if failed to prevent continuous retries
                processedRescheduledEvents.current.add(eventInstanceKey);
              }
            }
          }

          if (matterHasUpdates) {
            console.log('=============updatedEvents==', updatedEvents);
            eventsToUpdate[matterId] = updatedEvents;
          }
        }

        // Update events state if we have updates
        if (hasUpdates && Object.keys(eventsToUpdate).length > 0) {
          setEvents((prevEvents: Record<string, EventType[]>) => ({
            ...prevEvents,
            ...eventsToUpdate,
          }));
        }
      } catch (error) {
        console.error('Error in fetchRescheduledEvents:', error);
      }
    };

    // Only run if we have events and they haven't been processed for rescheduling yet
    if (Object.keys(events).length > 0 && setEvents) {
      fetchRescheduledEvents();
    }
  }, [events, setEvents]); // Run when events change

  // Fetch court notice data from API if fieldId and workflowId are provided and we haven't fetched data yet
  useEffect(() => {
    const fetchCourtNoticeData = async () => {
      // Skip fetch if we don't have required IDs or we've already fetched data
      if (!fieldId || !workflowId || initialDataFetched) {
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch court notice data
        const response = await apiGet<{
          statusCode: number;
          data?: {
            clients?: ClientMatter[];
            events?: Record<string, EventType[]>;
            options?: Record<string, string[]>;
          };
        }>(`/workflow/court-notice`, {
          params: {
            field_id: fieldId,
            workflow_id: workflowId,
          },
        });

        if (response.data.statusCode === 200 && response.data.data) {
          const apiData = response.data.data;

          // Set clients and events from API data
          if (apiData.clients) {
            setClients(apiData.clients);
            // If clients exist in initial data, consider first contact already added
            if (apiData.clients.length > 0) {
              setFirstContactAdded(true);
              console.log('Initial data includes clients, firstContactAdded set to true');
            }
          }

          if (apiData.events) {
            // Process rescheduled events before setting the events
            const processedEvents = { ...apiData.events };

            for (const [matterId, matterEvents] of Object.entries(processedEvents)) {
              const updatedEvents = [...matterEvents];
              let hasUpdates = false;

              for (let i = 0; i < updatedEvents.length; i++) {
                const event = updatedEvents[i];

                // Check if this event needs rescheduled data
                // We need to fetch if:
                // 1. appointmentAction is 'Cancel' 
                // 2. appointmentToReschedule exists
                // 3. This specific event instance hasn't been processed yet OR the event doesn't have complete data
                const eventInstanceKey = `${event.id}-${event.appointmentToReschedule}`;
                if (
                  event?.appointmentAction === 'Cancel' &&
                  event?.appointmentToReschedule &&
                  (!processedRescheduledEvents.current.has(eventInstanceKey) ||
                   !event?.startDate || !event?.startTime || !event?.subject || event?.subject === 'Save court notice only')
                ) {
                  try {
                    const rescheduledResponse = await apiGet<{
                      statusCode: number;
                      data?: EventType;
                    }>(`/workflow/event/${event.appointmentToReschedule}`);

                    if (
                      rescheduledResponse.data?.statusCode === 200 &&
                      rescheduledResponse.data?.data
                    ) {
                      const result = rescheduledResponse.data?.data;
                      
                      // For Cancel events, always use the rescheduled appointment's date/time and details
                      const originalId = event.id;
                      const originalAppointmentToReschedule = event.appointmentToReschedule;
                      const originalCourtNoticeType = event.courtNoticeType;
                      
                      // Preserve the original appointmentToReschedule value and set appointmentAction
                      result.appointmentAction = 'Cancel';
                      result.appointmentToReschedule = originalAppointmentToReschedule;
                      result.courtNoticeType = originalCourtNoticeType;
                      result.id = originalId;
                      
                      // For Cancel events, we want to show the rescheduled appointment's date/time
                      // So we don't preserve any original date/time data - we use what we fetched from the rescheduled event

                      updatedEvents[i] = result;
                      hasUpdates = true;

                      // Mark this specific event instance as processed
                      processedRescheduledEvents.current.add(eventInstanceKey);

                      console.log(
                        '✅ Retrieved rescheduled event during initial load with preserved Cancel event date/time:',
                        rescheduledResponse.data.data
                      );
                    }
                  } catch (error) {
                    console.error(
                      '❌ Error fetching rescheduled event during initial load:',
                      error
                    );
                    // Mark as processed even if failed to prevent continuous retries
                    processedRescheduledEvents.current.add(eventInstanceKey);
                  }
                }
              }

              if (hasUpdates) {
                processedEvents[matterId] = updatedEvents;
              }
            }

            setEvents(processedEvents);
          }

          // Store options for dropdown fields if provided
          // Combine with options from props if available
          if (apiData.options) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            setLocalOptions((prevOptions: any) => ({
              ...prevOptions,
              ...apiData.options,
              ...options, // Overlay with options from props (higher priority)
            }));
          }

          // Mark that we've fetched initial data
          setInitialDataFetched(true);
        }
      } catch (err) {
        console.error('Error fetching court notice data:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch court notice data');
      } finally {
        setLoading(false);
      }
    };

    fetchCourtNoticeData();
  }, [fieldId, workflowId, initialDataFetched, options]);

  // Function to extract matter name from parentheses
  const extractLastParentheses = (str: string) => {
    // Split on '('
    const parts = str.split('(');
    if (parts.length < 2) return null;

    // Take the last part and remove the trailing ')'
    const lastPart = parts[parts.length - 1].trim();
    return lastPart.endsWith(')') ? lastPart.slice(0, -1).trim() : null;
  };

  // Store client/matter names in array and log them
  useEffect(() => {
    if (clients.length > 0) {
      console.log('obj->', clients);
      const names = clients.map(client =>
        client.matters.map(matter => {
          const matterName = extractLastParentheses(matter.name) || matter.name;
          return `${client.name} | ${matterName}`;
        })
      ).flat().map(name => name.replace(/\s+/g, ' ')); // Normalize spaces

      setClientMatterNames(names);
      console.log('Client/Matter Names:', names);
    }
  }, [clients]);

  // Handle matter selection change
  const handleMatterChange = useCallback(
    (clientId: string, matterId: string) => {
      // If a different matter was previously selected for this client, request backend cleanup
      const previouslySelected = selectedMatters[clientId];
      
      if (previouslySelected && previouslySelected !== matterId) {
        if (onDeleteMatter) {
          try {
            // Trigger parent handler to delete the old client/matter and its events via API
            onDeleteMatter(previouslySelected);
          } catch (e) {
            console.error('Failed to request deletion of previous matter:', previouslySelected, e);
          }
        }

        // Optimistically update local UI: remove previous matter's events
        if (setEvents) {
          setEvents((prev: Record<string, EventType[]>) => {
            const next = { ...prev };
            delete next[previouslySelected];
            return next;
          });
        }

        // Update AdvisorPanel event count for workflow name update
        if (typeof window !== 'undefined' && (window as unknown as { updateAdvisorPanelEventCount?: (clientName: string, count: number) => void }).updateAdvisorPanelEventCount) {
          // Find the previous matter to get its client name
          const previousMatter = allMatters.find(m => m.id === previouslySelected) || 
                                searchResults.find(m => m.id === previouslySelected);
          const previousClientName = previousMatter?.client_name || 'Unknown Client';
          
          // Get the count of events that were removed
          const previousEventCount = events[previouslySelected]?.length || 0;
          
          if (previousEventCount > 0) {
            (window as unknown as { updateAdvisorPanelEventCount: (clientName: string, count: number) => void }).updateAdvisorPanelEventCount(previousClientName, -previousEventCount);
          }
        }

        // Also remove the previous matter from this client's matters list locally
        if (setClients) {
          setClients((prevClients: ClientMatter[]) => {
            return prevClients.map(c => {
              if (c.id !== clientId) return c;
              return {
                ...c,
                matters: c.matters.filter(m =>
                  m.id !== previouslySelected && m._id !== previouslySelected && m.matter_id !== previouslySelected
                ),
              };
            });
          });
        }
      }

      const selectedMatter =
        allMatters.find(matter => matter.id === matterId) ||
        searchResults.find(matter => matter.id === matterId);

      if (selectedMatter) {
        setSelectedMatters(prev => ({
          ...prev, 
          [clientId]: matterId
        }));

        // Find the selected matter from search results
        // const selectedMatter = searchResults.find(matter => matter.id === matterId);

        if (selectedMatter) {
          // Store the client_name for this matter
          setSelectedMatterClientNames((prev: Record<string, string>) => ({
            ...prev,
            [matterId]: selectedMatter.client_name || '',
          }));

          setSelectedMatter(selectedMatter);

          // Store selectedMatter.my_matter_id in localStorage
          if (selectedMatter.my_case_matter_id) {
            localStorage.setItem('selectedMatterMyMatterId', selectedMatter.my_case_matter_id);
          }

          if (setClients) {
            // Add the selected matter to the client's matters array if it's not already there
            setClients((prevClients: ClientMatter[]) => {
              const updatedClients = prevClients.map(client => {
                if (client.id === clientId) {
                  // Check if this matter is already in the client's matters array
                  const matterExists = client.matters.some(
                    matter =>
                      matter.id === matterId || matter._id === matterId || matter.matter_id === matterId
                  );

                  if (!matterExists) {
                    // Add the matter to the client's matters array
                    const updatedClient = {
                      ...client,
                      matters: [...client.matters, selectedMatter],
                    };

                    return updatedClient;
                  }
                }
                return client;
              });

              // Call onClientsUpdate to notify parent of the change
              if (onClientsUpdate) {
                onClientsUpdate(updatedClients);
              }

              return updatedClients;
            });

            // Initialize events for this matter if not already present
            if (setEvents) {
              setEvents((prevEvents: Record<string, EventType[]>) => {
                if (!prevEvents[matterId]) {
                  return {
                    ...prevEvents,
                    [matterId]: [],
                  };
                }
                return prevEvents;
              });
            }

            // Update AdvisorPanel event count for the NEW client/matter
            if (typeof window !== 'undefined' && (window as unknown as { updateAdvisorPanelEventCount?: (clientName: string, count: number) => void }).updateAdvisorPanelEventCount) {
              const newClientName = selectedMatter.client_name || selectedMatter.name || 'Unknown Client';
              
              // Get the count of events for the new matter from the updated events state
              // We need to use a callback to get the most recent events state
              setEvents((currentEvents: Record<string, EventType[]>) => {
                const newEventCount = currentEvents[matterId]?.length || 0;
                
                // Always call updateAdvisorPanelEventCount to ensure the client appears in the summary
                // Even if they have 0 events, they should be in the workflow name
                (window as unknown as { updateAdvisorPanelEventCount: (clientName: string, count: number) => void }).updateAdvisorPanelEventCount(newClientName, newEventCount);
                
                return currentEvents; // Return unchanged events
              });
            }
          }
        }

        // Reset events UI for this client if needed
        // This ensures that when a different matter is selected,
        // the UI updates to show events for that matter

        // Update any other relevant state or UI elements
        // For example, you might want to clear any edit modal state
        if (currentClientId === clientId && currentMatterId !== matterId) {
          setCurrentMatterId(matterId);
          setCurrentEditEvent(null);
          setEditModalOpen(false);
        }
      }
    },
    [
      allMatters,
      searchResults,
      setClients,
      setEvents,
      onClientsUpdate,
      onDeleteMatter,
      selectedMatters,
      currentClientId,
      currentMatterId,
      clients,
      events,
    ]
  );

  // Mark an event as completed/reviewed
  const toggleEventStatus = async (matterId: string, eventId: string) => {
    try {
      const matterEvents = [...(events[matterId] || [])];
      const eventIndex = matterEvents.findIndex(event => event.id === eventId);

      if (eventIndex !== -1) {
        const updatedEvent = {
          ...matterEvents[eventIndex],
          isCompleted: !matterEvents[eventIndex].isCompleted,
        };

        // Count TBD fields and add to the event
        updatedEvent.tbdFieldsCount = countTbdFields(updatedEvent);

        matterEvents[eventIndex] = updatedEvent;

        // Update local state first for immediate UI feedback
        setEvents((prev: Record<string, EventType[]>) => ({
          ...prev,
          [matterId]: matterEvents,
        }));

        // Call API to update event status if fieldId and workflowId are available
        if (fieldId && workflowId) {
          await apiPut(`/workflow/court-notice/event`, {
            field_id: fieldId,
            workflow_id: workflowId,
            matter_id: matterId,
            event_id: eventId,
            event: updatedEvent,
          });
        }

        // Call onSave if provided
        if (onSave) {
          onSave(matterId, matterEvents);
        }
      }
    } catch (error) {
      console.error('Error updating event status:', error);
      // Revert to previous state on error
      setEvents((prev: Record<string, EventType[]>) => ({ ...prev }));
      setError('Failed to update event status. Please try again.');
    }
  };

  // Remove an event
  const removeEvent = async (matterId: string, eventId: string) => {
    // Set pending delete data and show confirmation modal instead of using confirm()
    // console.log('🚀 ~ removeEvent ~ matterId:', matterId,eventId);
    setPendingDeleteEventData({ matterId, eventId });
    setConfirmDeleteEventModalOpen(true);
  };

  // Handle confirmed event deletion
  const handleConfirmEventDelete = async () => {
    if (!pendingDeleteEventData) return;

    const { matterId, eventId } = pendingDeleteEventData;

    try {
      // Update local state first for immediate UI feedback
      // console.log('🚀 ~ handleConfirmEventDelete ~ dhyey matterId:', matterId, eventId);
      const matterEvents = events[matterId]
        ? events[matterId].filter(event => event.id !== eventId)
        : [];

      // console.log('🚀 ~ handleConfirmEventDelete ~ dhyey fieldId:', matterEvents);

      setEvents((prev: Record<string, EventType[]>) => ({
        ...prev,
        [matterId]: matterEvents,
      }));

      // Call API to delete event if fieldId and workflowId are available
      if (fieldId && workflowId) {
        await apiDelete(`/workflow/court-notice/event`, {
          data: {
            field_id: fieldId,
            workflow_id: workflowId,
            matter_id: matterId,
            event_id: eventId,
          },
        });
      }

      // Call onDeleteEvent if provided
      if (onDeleteEvent) {
        onDeleteEvent(matterId, eventId);
      }

      // Call onSave if provided to persist changes
      if (onSave) {
        onSave(matterId, matterEvents);
      }

      // Update AdvisorPanel event count if the global function is available
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if (typeof window !== 'undefined' && (window as any).updateAdvisorPanelEventCount) {
        // Find the deleted event to get its client name
        const deletedEvent = events[matterId]?.find(event => event.id === eventId);
        if (deletedEvent) {
          // Use clientName first, fallback to subject if needed
          const eventClientName = deletedEvent.clientName || deletedEvent.subject || 'Unknown Client';
          
          // Check if this was the last event for this client
          const remainingEventsCount = matterEvents.length;
          
          if (remainingEventsCount === 0) {
            // Set count to 0 to remove client from notice summary
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (window as any).updateAdvisorPanelEventCount(eventClientName, 0, true); // true indicates absolute set
            console.log('🔄 CourtNotice: Set event count to 0 for client (last event deleted):', eventClientName);
          } else {
            // Decrement the count by 1 (using -1)
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (window as any).updateAdvisorPanelEventCount(eventClientName, -1);
            console.log('🔄 CourtNotice: Decremented AdvisorPanel event count for:', eventClientName);
          }
        }
      }
    } catch (error) {
      console.error('Error deleting event:', error);
      // Revert to previous state on error
      setEvents((prev: Record<string, EventType[]>) => ({ ...prev }));
      setError('Failed to delete event. Please try again.');
    } finally {
      // Reset pending delete data and close modal
      setPendingDeleteEventData(null);
      setConfirmDeleteEventModalOpen(false);
    }
  };

  // Open the edit modal for an event
  const handleEditEvent = (matterId: string, eventId: string) => {
    console.log("VIEW OPEN")
    const eventToEdit = events[matterId]?.find(e => e.id === eventId);
    if (eventToEdit) {
      // Find the client and matter that this event belongs to
      let foundMatter: Matter | undefined;

      // Search through all clients and their matters to find the one with this matterId
      for (const client of clients) {
        const matter = client.matters.find(m => m.id === matterId);
        if (matter) {
          foundMatter = matter;
          break;
        }
      }

      // If we found the matter, set it as selectedMatter
      if (foundMatter) {
        setSelectedMatter(foundMatter);
      }

      setCurrentEditEvent(eventToEdit);
      setIsEditMode(eventToEdit.eventStatus !== 'Synced');
      setCurrentMatterId(matterId);
      setEditModalOpen(true);
    }
  };

  // Save the edited event or add a new one
  const handleSaveEditedEvent = async (matterId: string, updatedEvent: EventType) => {
    console.log('🚀 ~ handleSaveEditedEvent ~ updatedEvent:', updatedEvent);
    if (isChildWorkflow) {
      localStorage.setItem("SelectedEventMatterId", updatedEvent.client_matter_id ?? "");
      localStorage.removeItem('isEventAdded');
    }
    try {
      const matterEvents = [...(events[matterId] || [])];
      const eventIndex = matterEvents.findIndex(event => event.id === updatedEvent.id);

      // If this event has an appointmentToReschedule, clear it from processed events
      // and immediately fetch the rescheduled data if needed
      if (updatedEvent.appointmentToReschedule && updatedEvent.appointmentAction === 'Cancel') {
        // Clear both the general appointment ID and the specific event instance
        processedRescheduledEvents.current.delete(updatedEvent.appointmentToReschedule);
        processedRescheduledEvents.current.delete(`${updatedEvent.id}-${updatedEvent.appointmentToReschedule}`);
        console.log('🔄 Cleared processed reschedule event for both general and specific instance:', updatedEvent.appointmentToReschedule);
        
        // Immediately fetch the rescheduled event data if this event doesn't have complete data
        if (!updatedEvent.startDate || !updatedEvent.startTime || !updatedEvent.subject || updatedEvent.subject === 'Save court notice only') {
          try {
            const response = await apiGet<{
              statusCode: number;
              data?: EventType;
            }>(`/workflow/event/${updatedEvent.appointmentToReschedule}`);
            
            if (response.data?.statusCode === 200 && response.data?.data) {
              const result = response.data?.data;
              
              // For Cancel events, always use the rescheduled appointment's date/time and details
              const originalId = updatedEvent.id;
              const originalAppointmentToReschedule = updatedEvent.appointmentToReschedule;
              const originalCourtNoticeType = updatedEvent.courtNoticeType;
              
              // Update with fetched data (this gives us the rescheduled appointment's date/time)
              updatedEvent = result;
              
              // Restore only the essential original values that should be preserved for Cancel events
              updatedEvent.appointmentAction = 'Cancel';
              updatedEvent.appointmentToReschedule = originalAppointmentToReschedule;
              updatedEvent.courtNoticeType = originalCourtNoticeType;
              updatedEvent.id = originalId;
              
              // For Cancel events, we want to show the rescheduled appointment's date/time
              // So we don't preserve any original date/time data - we use what we fetched
              
              console.log('✅ Immediately fetched rescheduled event data with preserved Cancel event date/time:', response.data.data);
            }
          } catch (error) {
            console.error('❌ Error immediately fetching rescheduled event:', error);
          }
        }
      }

      // Count TBD fields and add to the event
      updatedEvent.tbdFieldsCount = countTbdFields(updatedEvent);

      // Check if this is an update or a new event
      const isNewEvent = eventIndex === -1;

      // If it's an update, replace the existing event
      if (!isNewEvent) {
        matterEvents[eventIndex] = updatedEvent;
      }
      // If it's a new event, add it to the array
      else {
        matterEvents.push(updatedEvent);
      }

      // Update local state first for immediate UI feedback
      setEvents((prev: Record<string, EventType[]>) => ({
        ...prev,
        [matterId]: matterEvents,
      }));

      // Call API to update or add event if fieldId and workflowId are available
      if (fieldId && workflowId) {
        // Start loading state
        setLoading(true);

        // Determine the API endpoint based on whether this is a new event or an update
        const apiEndpoint = isNewEvent
          ? `/workflow/court-notice/event` // POST for new events
          : `/workflow/court-notice/event`; // PUT for updates

        // Make the appropriate API call
        const apiMethod = isNewEvent ? apiPost : apiPut;

        await apiMethod(apiEndpoint, {
          field_id: fieldId,
          workflow_id: workflowId,
          matter_id: matterId,
          event: updatedEvent,
          event_id: updatedEvent.id,
        });
      }

      // Call appropriate callback based on whether this is a new event or an update
      if (isNewEvent && onAddEvent) {
        // This was an add operation
        onAddEvent(matterId);
      } else if (!isNewEvent && onEditEvent) {
        // This was an edit operation
        onEditEvent(matterId, updatedEvent.id, updatedEvent);
      }

      // Call onSave if provided
      if (onSave) {
        onSave(matterId, matterEvents, updatedEvent);
      }

      // Update AdvisorPanel event count if the global function is available
      // Only increment for new events, not updates
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if (isNewEvent && typeof window !== 'undefined' && (window as any).updateAdvisorPanelEventCount) {
        // Use clientName first, fallback to subject if needed
        const eventClientName = updatedEvent.clientName || updatedEvent.subject || 'Unknown Event';
        if (eventClientName && eventClientName !== 'Unknown Event') {
          // Increment the count by 1 for new events
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (window as any).updateAdvisorPanelEventCount(eventClientName, 1);
          console.log('🔄 Incremented AdvisorPanel event count for new event:', eventClientName);
        }
      }
    } catch (error) {
      console.error('Error saving event:', error);
      // Revert to previous state on error
      setEvents((prev: Record<string, EventType[]>) => ({ ...prev }));
      setError('Failed to save event. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handler for adding a new event button click
  const handleAddEvent = (clientId: string, matterId: string) => {
    if (onAddEvent) {
      onAddEvent(matterId);
    } else {
      // Find the client for this matter
      const client = clients.find(c => c.id === clientId);

      if (client) {
        // If no matter is selected (empty matterId), alert the user to select one first
        if (!matterId) {
          // Show an alert or some notification that a matter must be selected
          setError('Please select a Client/Matter first');
          return;
        }

        // Find the selected matter
        const selectedMatter = client.matters.find(m => m.id === matterId);

        if (selectedMatter) {
          // Set current client and matter for the modal
          setCurrentClientId(clientId);
          setCurrentMatterId(matterId);

          // Get the client_matter_id from the selected matter (prioritize matter_id, then _id)
          const clientMatterId = selectedMatter.matter_id || selectedMatter._id || matterId;

          // Create a new event template with selected client and matter
          const newEventTemplate: EventType = {
            id: `evt-${Date.now()}`,
            caseNumber: selectedMatter?.case_number || '',
            clientName: formatClientMatterName(`${client.name} | ${selectedMatter.name}`),
            description: 'Client MUST appear',
            subject: `JT; ${client.name} (${selectedMatter.caseDescription || ''}-) ${''} `,
            courtNoticeType: '',
            appointmentAction: '',
            charge: selectedMatter.caseDescription || '',
            county: selectedMatter?.ex_county_of_arrest || '',
            courtLocation: '',
            startDate: getCurrentDateInUserTimezone(),
            endDate: getCurrentDateInUserTimezone(),
            // Convert default times from local timezone to UTC for storage
            startTime: convertTimeToUTCReliable('09:00', getCurrentDateInUserTimezone()),
            endTime: convertTimeToUTCReliable('10:00', getCurrentDateInUserTimezone()),
            allDay: false,
            requiredAttendees: '',
            optionalAttendees: '',
            clientAttendance: '',
            meetingLocation: '',
            // Add client_matter_id for attendees search to work
            client_matter_id: clientMatterId,
            selectedMatterId: matterId,
            _id: '',
          };

          // Set the event to edit and open the edit modal
          setCurrentEditEvent(newEventTemplate);
          setIsEditMode(true); // New events are always editable
          setEditModalOpen(true);
        }
      }
    }
  };

  const formatDate = (dateStr: string | undefined, timeStr?: string, userTimezone?: string) => {
    if (!dateStr) return "";

    try {
      const timezone = userTimezone || getUserTimezone();
      if (timeStr) {
        const utcMoment = moment.tz(`${dateStr} ${timeStr}`, 'YYYY-MM-DD HH:mm', 'UTC');
        const localMoment = utcMoment.clone().tz(timezone);
        return localMoment.format('MM/DD/YYYY');
      } else {
        const m = moment.tz(dateStr, ["YYYY-MM-DD", "DD-MM-YYYY"], true, timezone);
        if (!m.isValid()) return dateStr;
        return m.format("MM/DD/YYYY");
      }
    } catch (error) {
      console.error('Error formatting date:', error);
      const m = moment.tz(dateStr, ["YYYY-MM-DD", "DD-MM-YYYY"], true, userTimezone || getUserTimezone());
      if (!m.isValid()) return dateStr;
      return m.format("MM/DD/YYYY");
    }
  };

  const formatTime = (timeStr: string | undefined, dateStr?: string) => {
    if (!timeStr) return '';

    try {
      let localTimeStr = timeStr;
      if (dateStr) {
        localTimeStr = convertTimeFromUTCReliable(timeStr, dateStr);
      }
      const [hours, minutes] = localTimeStr.split(':');
      const hour24 = parseInt(hours, 10);
      const minute = parseInt(minutes, 10);

      if (isNaN(hour24) || isNaN(minute)) {
        return localTimeStr;
      }

      let hour12 = hour24;
      let ampm = 'AM';

      if (hour24 === 0) {
        hour12 = 12;
      } else if (hour24 === 12) {
        ampm = 'PM';
      } else if (hour24 > 12) {
        hour12 = hour24 - 12;
        ampm = 'PM';
      }

      const formattedMinutes = minute.toString().padStart(2, '0');

      return `${hour12}:${formattedMinutes} ${ampm}`;
    } catch (e) {
      console.error('Error formatting time:', e);
      return timeStr;
    }
  };

  // Handle keyboard navigation for dropdown
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, client: ClientMatter) => {
    const isDropdownOpen = openDropdownClientId === client.id;

    // Only use searchResults when dropdown is open, which now contains all matters initially or filtered results
    const matters = isDropdownOpen ? searchResults : [];

    if (!isDropdownOpen) {
      // Open dropdown on Enter, Space, or Arrow keys
      if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown' || e.key === 'ArrowUp') {
        e.preventDefault();
        setOpenDropdownClientId(client.id);
        setClientSearchTerms(prev => ({
          ...prev,
          [client.id]: '',
        }));
        setFocusedIndex(0); // Start with first item highlighted
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev => {
          const nextIndex = prev < matters.length - 1 ? prev + 1 : 0;
          // Scroll highlighted option into view
          setTimeout(() => {
            const highlightedElement = document.querySelector(
              `[data-court-notice-option-index="${nextIndex}"]`
            );
            if (highlightedElement) {
              highlightedElement.scrollIntoView({
                block: 'nearest',
                behavior: 'smooth',
              });
            }
          }, 0);
          return nextIndex;
        });
        break;

      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => {
          const nextIndex = prev > 0 ? prev - 1 : matters.length - 1;
          // Scroll highlighted option into view
          setTimeout(() => {
            const highlightedElement = document.querySelector(
              `[data-court-notice-option-index="${nextIndex}"]`
            );
            if (highlightedElement) {
              highlightedElement.scrollIntoView({
                block: 'nearest',
                behavior: 'smooth',
              });
            }
          }, 0);
          return nextIndex;
        });
        break;

      case 'Enter':
        e.preventDefault();
        if (focusedIndex >= 0 && focusedIndex < matters.length) {
          const selectedMatter = matters[focusedIndex];
          handleMatterChange(client.id, selectedMatter.id);
          setOpenDropdownClientId(null);
          setClientSearchTerms(prev => ({
            ...prev,
            [client.id]: '',
          }));
          setSearchResults([]);
          setFocusedIndex(-1);
        }
        break;

      case 'Escape':
        e.preventDefault();
        setOpenDropdownClientId(null);
        setClientSearchTerms(prev => ({
          ...prev,
          [client.id]: '',
        }));
        setFocusedIndex(-1);
        break;
    }
  };

  // Reset focused index when dropdown opens/closes or search results change
  useEffect(() => {
    if (openDropdownClientId) {
      // Set focus to first item when dropdown opens and has results
      if (searchResults.length > 0) {
        setFocusedIndex(0);
      } else {
        setFocusedIndex(-1);
      }
    } else {
      setFocusedIndex(-1);
    }
  }, [openDropdownClientId, searchResults.length]);

  // Update focused index when search results change but dropdown remains open
  useEffect(() => {
    if (openDropdownClientId && searchResults.length > 0) {
      // If we have results and no item is focused, focus the first one
      if (focusedIndex === -1 || focusedIndex >= searchResults.length) {
        setFocusedIndex(0);
      }
    } else if (searchResults.length === 0) {
      setFocusedIndex(-1);
    }
  }, [searchResults, openDropdownClientId, focusedIndex]);

  // Add at the top-level of the component (inside CourtNotice):
  const displayedEventIdsByMatter: Record<string, Set<string>> = {};

  // Render events for a matter
  const renderMatterEvents = (clientId: string, matter: Matter) => {
    let matterEvents = events[matter.id] || [];
    // Filter out events already displayed for this matter
    if (displayedEventIdsByMatter[matter.id]) {
      matterEvents = matterEvents.filter(
        event => !displayedEventIdsByMatter[matter.id].has(event.id)
      );
      // Mark these events as displayed for subsequent boxes
      matterEvents.forEach(event => displayedEventIdsByMatter[matter.id].add(event.id));
    }
    console.log('🚀 ~ renderMatterEvents ~ matterEvents:', matterEvents);

    // Check if any events in this matter are scheduled
    const hasScheduledEvents = matterEvents.some(
      event => event.eventStatus === 'Synced' || event.eventStatus === 'SyncFailed'
    );
    const shouldDisableButtons =
      isChildWorkflow ? false : isDisabled || (isUpdateMyCase && hasScheduledEvents) || hasScheduledEvents;
    // Define shouldDisableButtons for this scope
    const shouldDisableAddEvent = isChildWorkflow ? false : hasScheduledEvents;

    return (
      <div key={matter.id} className={workflowStyles.courtNoticeEventsContainer}>
        {matterEvents.length > 0 && <div className="font-medium text-[14px] leading-[20px] text-[#2A2E34] mb-2">Events</div>}

        {/* Events list */}
        <div>
          {matterEvents.length > 0 ? (
            matterEvents.map(event => {
              // Handle rescheduled events - this should be done outside of render
              // We'll create a separate effect to handle this

              // Calculate TBD fields count if not already set
              const tbdCount = event.tbdFieldsCount ?? countTbdFields(event);
              console.log('🚀 ~ renderMatterEvents ~ event:', event);

              // Check if this event should have delete button disabled
              const shouldDisableDelete = isChildWorkflow ? false :
                isDisabled ||
                (isUpdateMyCase && event.eventStatus === 'Synced') ||
                event.eventStatus === 'Synced' ||
                event.eventStatus === 'SyncFailed';

              const shouldDisableDeleteNew = isChildWorkflow ? false :
                isDisabled ||
                (isUpdateMyCase && event.eventStatus === 'Synced') ||
                event.eventStatus === 'Synced' ||
                event.eventStatus === 'SyncFailed';

              const shouldDisableEdit = isChildWorkflow ? false :
                isTaskReviewed ||
                event.eventStatus === 'Synced' ||
                event.eventStatus === 'SyncFailed';

                // Helper function to format 24-hour time to AM/PM format for cancelled appointments
  const formatTimeToAMPM = (timeStr: string | undefined) => {
    if (!timeStr) return '';

    try {
      const [hours, minutes] = timeStr.split(':');
      const hour24 = parseInt(hours, 10);
      const minute = parseInt(minutes, 10);

      if (isNaN(hour24) || isNaN(minute)) {
        return timeStr;
      }

      let hour12 = hour24;
      let ampm = 'AM';

      if (hour24 === 0) {
        hour12 = 12;
      } else if (hour24 === 12) {
        ampm = 'PM';
      } else if (hour24 > 12) {
        hour12 = hour24 - 12;
        ampm = 'PM';
      }

      const formattedMinutes = minute.toString().padStart(2, '0');

      return `${hour12}:${formattedMinutes} ${ampm}`;
    } catch (e) {
      console.error('Error formatting time to AM/PM:', e);
      return timeStr;
    }
  };

              // setIsEditPage(shouldDisableEdit)

              console.log('EVENT_DATA', event);

              return (
                <div key={event.id} className={workflowStyles.courtNoticeEvent}>
                  <div className="flex items-center justify-between">
                    {event?.subject !== 'Save court notice only' ? (
                      <div className={workflowStyles.courtNoticeEventDetails}>
                        <div className={workflowStyles.courtNoticeEventClientName}>
                          {/* {event?.appointmentAction && (
                          <span className="mr-2 px-2 py-1 text-xs font-medium bg-blue-100 text-[#3F73F6] rounded-[12px]">
                            {event.appointmentAction}
                          </span>
                        )} */}
                          {/* {event?.appointmentAction && (
                          <span className="ml-2 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                            {event.appointmentAction}
                          </span>
                        )} */}
                          {event?.subject}
                        </div>
                        {/* <div className={workflowStyles.courtNoticeEventCaseNumber}>{event.caseNumber}</div> */}
                        {
                          <div className={`${workflowStyles.courtNoticeEventTiming} flex items-start`}>
                            {event?.appointmentAction && (
                              <span className="px-[12px] py-[4px] mr-2 text-[12px] leading-[16px] bg-[#C7D1DF] text-[#2A2E34] rounded-[100px] flex-shrink-0">
                                {event.appointmentAction}
                              </span>
                            )}
                            <div className="flex-1">
                              {/* {formatDate(event.startDate || event.date)} {formatTime(event.startTime, event.startDate || event.date)}{' '}
                            - {formatTime(event.endTime, event.endDate || event.startDate || event.date)} */}
                              {formatDate(event.startDate || event.date, event.startTime, getUserTimezone())} {event.appointmentAction === 'Cancel' ? formatTimeToAMPM(event.startTime) : formatTime(event.startTime, event.startDate || event.date)}{' '}
                              {' to '}
                              {formatDate(event.endDate || event.startDate || event.date, event.endTime, getUserTimezone())} {event.appointmentAction === 'Cancel' ? formatTimeToAMPM(event.endTime) : formatTime(event.endTime, event.endDate || event.startDate || event.date)}
                            </div>
                          </div>
                        }
                      </div>
                    ) : (
                      <>
                        <div className={workflowStyles.courtNoticeEventDetails}>
                          <div className={workflowStyles.courtNoticeEventClientName}>
                            {event?.subject}{' '}
                          </div>
                        </div>
                      </>
                    )}

                    <div className={workflowStyles.courtNoticeActionButtons}>
                      <button
                        onClick={() => toggleEventStatus(matter.id, event.id)}
                        className={
                          event.isCompleted
                            ? workflowStyles.courtNoticeButtonCompleted
                            : workflowStyles.courtNoticeButtonPending
                        }
                        title={event.isCompleted ? '' : `${tbdCount > 0 ? ` ` : ''}`}
                        disabled={shouldDisableDelete}
                      >
                        {tbdCount > 0 ? (
                          <div className="bg-[#F6B175] text-white rounded-full w-[20px] h-[20px] flex items-center justify-center text-xs font-medium leading-none">
                            {tbdCount}
                          </div>
                        ) : (
                          <Image
                            src="/assets/check-circle-filled.svg"
                            alt="Check"
                            width={20}
                            height={20}
                          />
                        )}
                      </button>
                      {/* Delete button visibility logic */}
                      {(!isChildWorkflow ||
                        (isChildWorkflow &&
                          // For child workflow, show delete button only if:
                          // 1. eventStatus is "New" AND (isAddSecondary is undefined OR false)
                          event.eventStatus === 'New' &&
                          (!event.isAddSecondary || event.isAddSecondary === undefined))) && (
                          <button
                            onClick={() => removeEvent(matter.id, event.id)}
                            className={workflowStyles.courtNoticeButtonPending + ' hover:text-red-500'}
                            title="Delete event"
                            disabled={
                              !isChildWorkflow
                                ? shouldDisableDeleteNew
                                : event.eventStatus === 'Synced' ||
                                event.eventStatus === 'SyncFailed' ||
                                (event.eventStatus === 'New' && event.isAddSecondary === true)
                            }
                          >
                            {(
                              !isChildWorkflow
                                ? shouldDisableDeleteNew
                                : event.eventStatus === 'Synced' ||
                                event.eventStatus === 'SyncFailed' ||
                                (event.eventStatus === 'New' && event.isAddSecondary === true)
                            ) ? (
                              <Image
                                src="/assets/trash-03-gray.svg"
                                alt="Delete"
                                width={20}
                                height={20}
                              />
                            ) : (
                              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" className="hover:[&_path]:stroke-[#3F73F6]">
                                <g id="trash-03">
                                  <path id="Icon" d="M7.5 2.5H12.5M2.5 5H17.5M15.8333 5L15.2489 13.7661C15.1612 15.0813 15.1174 15.7389 14.8333 16.2375C14.5833 16.6765 14.206 17.0294 13.7514 17.2497C13.235 17.5 12.5759 17.5 11.2578 17.5H8.74221C7.42409 17.5 6.76503 17.5 6.24861 17.2497C5.79396 17.0294 5.41674 16.6765 5.16665 16.2375C4.88259 15.7389 4.83875 15.0813 4.75107 13.7661L4.16667 5M8.33333 8.75V12.9167M11.6667 8.75V12.9167" stroke="#5F6F84" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                </g>
                              </svg>
                              // <Image src="/assets/trash-03.svg" alt="Delete" width={20} height={20} />
                            )}
                          </button>
                        )}
                      {!shouldDisableEdit || isTaskReviewed ? (
                        <button
                          onClick={() => handleEditEvent(matter.id, event.id)}
                          className={workflowStyles.courtNoticeButtonPending + ' hover:text-blue-500'}
                          title={isTaskReviewed ? "View event" : "Edit event"}
                          disabled={shouldDisableEdit && !isTaskReviewed}
                        >
                          {isTaskReviewed ? (
                            <svg width="20" height="20" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg" className="hover:[&_path]:stroke-[#3F73F6]">
                              <path d="M2.42012 12.7132C2.28394 12.4975 2.21584 12.3897 2.17772 12.2234C2.14909 12.0985 2.14909 11.9015 2.17772 11.7766C2.21584 11.6103 2.28394 11.5025 2.42012 11.2868C3.54553 9.50484 6.8954 5 12.0004 5C17.1054 5 20.4553 9.50484 21.5807 11.2868C21.7169 11.5025 21.785 11.6103 21.8231 11.7766C21.8517 11.9015 21.8517 12.0985 21.8231 12.2234C21.785 12.3897 21.7169 12.4975 21.5807 12.7132C20.4553 14.4952 17.1054 19 12.0004 19C6.8954 19 3.54553 14.4952 2.42012 12.7132Z" stroke="#2A2E34" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                              <path d="M12.0004 15C13.6573 15 15.0004 13.6569 15.0004 12C15.0004 10.3431 13.6573 9 12.0004 9C10.3435 9 9.0004 10.3431 9.0004 12C9.0004 13.6569 10.3435 15 12.0004 15Z" stroke="#5f6f84" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            // <Image src="/assets/eyeview.svg" alt="View" width={20} height={20} />
                          ) : shouldDisableEdit ? (
                            <Image src="/assets/edit-gray.svg" alt="Edit" width={20} height={20} />
                          ) : (
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" className="hover:[&_path]:stroke-[#3F73F6]">
                              <g id="edit" clipPath="url(#clip0_5993_11350)">
                                <path id="Icon" d="M9.16699 3.33332H5.66699C4.26686 3.33332 3.5668 3.33332 3.03202 3.6058C2.56161 3.84549 2.17916 4.22794 1.93948 4.69834C1.66699 5.23312 1.66699 5.93319 1.66699 7.33332V14.3333C1.66699 15.7335 1.66699 16.4335 1.93948 16.9683C2.17916 17.4387 2.56161 17.8212 3.03202 18.0608C3.5668 18.3333 4.26686 18.3333 5.66699 18.3333H12.667C14.0671 18.3333 14.7672 18.3333 15.302 18.0608C15.7724 17.8212 16.1548 17.4387 16.3945 16.9683C16.667 16.4335 16.667 15.7335 16.667 14.3333V10.8333M6.66697 13.3333H8.06242C8.47007 13.3333 8.6739 13.3333 8.86571 13.2873C9.03577 13.2464 9.19835 13.1791 9.34747 13.0877C9.51566 12.9847 9.65979 12.8405 9.94804 12.5523L17.917 4.58332C18.6073 3.89296 18.6073 2.77368 17.917 2.08332C17.2266 1.39296 16.1073 1.39296 15.417 2.08332L7.44802 10.0523C7.15977 10.3405 7.01564 10.4847 6.91257 10.6528C6.82119 10.802 6.75385 10.9645 6.71302 11.1346C6.66697 11.3264 6.66697 11.5302 6.66697 11.9379V13.3333Z" stroke="#5F6F84" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                              </g>
                              <defs>
                                <clipPath id="clip0_5993_11350">
                                  <rect width="20" height="20" fill="white" />
                                </clipPath>
                              </defs>
                            </svg>

                            // <Image src="/assets/edit.svg" alt="Edit" width={20} height={20} />
                          )}
                        </button>
                      ) : (
                        <button
                          onClick={() => handleEditEvent(matter.id, event.id)}
                          title="View event"
                        >
                          <Image src="/assets/eyeview.svg" alt="View" width={20} height={20} />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })
          ) : (
            <></>
          )}
        </div>

        {/* Action buttons */}
        <div className="flex w-full gap-2 mt-[20px]">
          <button
            className={`flex items-center font-medium justify-center h-[36px] text-[#3F73F6] text-[14px] leading-[20px] border border-[#3F73F6] rounded-[4px] py-[12px] px-[16px] w-full hover:bg-[#3F73F6] hover:text-white transition-colors duration-200 disabled:hover:bg-transparent disabled:hover:text-[#A2AFC2] disabled:hover:border-[#DCE2EB] ${conditionalDisabled(isTaskReviewed || isChildWorkflow || shouldDisableAddEvent, 'button', 'cursor-pointer')}`}
            onClick={() => handleAddEvent(clientId, matter.id)}
            disabled={isTaskReviewed || shouldDisableAddEvent || isChildWorkflow}
          >
            <span className="mr-2 text-[18px]">+</span>
            Add a new event
          </button>
          <button
            className={`flex items-center justify-center h-[36px] text-[14px] gap-2 font-medium text-[#3F73F6] border border-[#3F73F6] rounded-md py-3 px-[16px] w-full ${conditionalDisabled(allDisableButtons, 'button', 'cursor-pointer')}`}
            disabled={allDisableButtons}
          >
            All events
            {allDisableButtons ? (
              <Image
                src="/assets/Vector-right-gray.svg"
                alt="View all"
                className={conditionalDisabled(shouldDisableButtons, 'icon')}
                width={12}
                height={12}
              />
            ) : (
              <Image
                src="/assets/Vector-right.svg"
                alt="View all"
                className={conditionalDisabled(shouldDisableButtons, 'icon')}
                width={12}
                height={12}
              />
            )}
          </button>
        </div>
      </div>
    );
  };

  // Render a client section - updated for client-specific search terms
  const renderClientSection = (client: ClientMatter) => {

    const selectedMatterId = selectedMatters[client.id] || '';
    const selectedMatter = client.matters.find(m => m.id === selectedMatterId);
    console.log('🚀 ~ renderClientSection ~ selectedMatter:', selectedMatter);
    const isDropdownOpen = openDropdownClientId === client.id;
    const clientSearchTerm = clientSearchTerms[client.id] || '';

    // Show search results when available, otherwise show empty array
    // The search results will contain all matters initially or filtered results when searching
    let displayedMatters = isDropdownOpen ? [...searchResults] : [];

    // Remove duplicates based on matter ID
    displayedMatters = displayedMatters.filter((matter, index, self) =>
      index === self.findIndex(m => m.id === matter.id)
    );

    // Apply filtering to exclude matters selected by OTHER clients
    // but allow the current client's selected matter to remain available
    if (isDropdownOpen) {
      // Get all selected matter IDs from OTHER clients (not current client)
      const otherClientsSelectedMatters = Object.entries(selectedMatters)
        .filter(([clientId]) => clientId !== client.id)
        .map(([, matterId]) => matterId);

      // Filter out matters selected by other clients
      displayedMatters = displayedMatters.filter(
        matter => !otherClientsSelectedMatters.includes(matter.id)
      );

      // If the current client has a selected matter, ensure it's available in the dropdown
      // even if it was filtered out globally (to allow reselection within the same dropdown)
      if (selectedMatter && selectedMatterId) {
        const matterAlreadyInList = displayedMatters.some(matter => matter.id === selectedMatterId);
        if (!matterAlreadyInList) {
          // Add the currently selected matter to the dropdown options
          displayedMatters = [selectedMatter, ...displayedMatters];
        }
      }
    }

    // Check if any events in this client's matters are scheduled
    const hasScheduledEvents = client.matters.some(matter => {
      const matterEvents = events[matter.id] || [];
      return matterEvents.some(
        event => event.eventStatus === 'Synced' || event.eventStatus === 'SyncFailed'
      );
    });

    const shouldDisableDelete = isChildWorkflow ? false :
      isDisabled || (isUpdateMyCase && hasScheduledEvents) || hasScheduledEvents;

    // Define shouldDisableButtons for this scope
    const shouldDisableButtons = isChildWorkflow ? false :
      isDisabled || (isUpdateMyCase && hasScheduledEvents) || hasScheduledEvents;

    if (!displayedEventIdsByMatter[selectedMatterId]) {
      displayedEventIdsByMatter[selectedMatterId] = new Set();
    }

    return (
      <div key={client.id} className={workflowStyles.courtNoticeCard}>
        {/* Client Header */}
        {!isChildWorkflow && (
          <div className={workflowStyles.courtNoticeClientHeader}>
            <div className="flex justify-between absolute top-[10px] right-[10px] items-center">
              {/* <button className="flex items-center font-medium text-left w-full"> */}
              {/* Client name is shown in the dropdown now */}
              {/* </button> */}
              <button
                onClick={() => removeClientCard(client.id)}
                className="text-[#5F6F84] cursor-pointer hover:text-[#2a2e34]"
                disabled={shouldDisableDelete}
              >
                {shouldDisableDelete ? (
                  <Image src="/assets/x-close.svg" alt="Delete" width={20} height={20} />
                ) : (
                  <X size={20} />
                )}
              </button>
            </div>

            {/* Client's matters dropdown - Searchable version */}
            <div>
              <label className="block text-[14px] leading-[20px] font-medium text-[#2A2E34] mb-[4px]">
                Client/Matter
              </label>
              <div className="relative">
                <input
                  type="text"
                  className={`
                    w-full py-[8px] px-[12px] h-[36px] border font-normal rounded-[4px] text-[#2A2E34] text-[14px] leading-[20px] bg-white flex items-center flex-wrap gap-1
                    focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-200 focus-within:border-[#3F73F6]
                    disabled:cursor-not-allowed disabled:bg-[#F3F5F9] disabled:text-[#A2AFC2] disabled:border-[#DCE2EB]
                    ${isDropdownOpen ? 'border-[#3F73F6] ring-1 ring-[#3F73F6]' : 'border-[#C7D1DF]'}
                  `}
                  placeholder="Select Client/Matter"
                  value={
                    clientSearchTerm ||
                    (selectedMatter && !isDropdownOpen
                      ? formatString(selectedMatter.name)
                      : '')
                  }
                  onChange={e => {
                    setClientSearchTerms(prev => ({
                      ...prev,
                      [client.id]: e.target.value,
                    }));
                    if (openDropdownClientId !== client.id) {
                      setOpenDropdownClientId(client.id);
                    }
                  }}
                  onFocus={() => {
                    if (!isDisabled) {
                      setOpenDropdownClientId(client.id);
                      setClientSearchTerms(prev => ({
                        ...prev,
                        [client.id]: '',
                      }));
                    }
                  }}
                  onClick={() => {
                    if (!isDisabled) {
                      setOpenDropdownClientId(client.id);
                      setClientSearchTerms(prev => ({
                        ...prev,
                        [client.id]: '',
                      }));
                    }
                  }}
                  onKeyDown={e => handleKeyDown(e, client)}
                  disabled={shouldDisableDelete}
                  autoComplete="off"
                  role="combobox"
                  aria-haspopup="listbox"
                  aria-expanded={isDropdownOpen}
                  aria-activedescendant={
                    isDropdownOpen && focusedIndex >= 0
                      ? `court-notice-option-${client.id}-${focusedIndex}`
                      : undefined
                  }
                />

                {/* Chevron icon with rotation */}
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
                  <ChevronDown
                    size={20}
                    className={`transition-transform duration-300 ${isDropdownOpen ? 'rotate-180 text-[#3F73F6]' : 'rotate-0'} ${conditionalDisabled(shouldDisableDelete, 'icon', 'text-[#5F6F84]')}`}
                  />
                </div>

                {/* Dropdown styling */}
                {isDropdownOpen && (
                  <div
                    ref={dropdownRef}
                    className="absolute z-50 w-full mt-1 max-h-[150px] overflow-y-scroll bg-white border border-[#C7D1DF] rounded-[8px] shadow-lg animate-fadeIn overflow-hidden"
                    role="listbox"
                  >
                    <div className="max-h-60">
                      {isSearching ? (
                        <div className="p-4 text-center text-gray-500">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#3F73F6] mx-auto mb-2"></div>
                          Searching...
                        </div>
                      ) : displayedMatters.length > 0 ? (
                        displayedMatters.map((matter, index) => {
                          const formattedName = formatClientMatterName(matter.name);
                          const normalizedFormattedName = formattedName.replace(/\s+/g, ' '); // Normalize spaces
                          console.log('Client/Matter/names', normalizedFormattedName);
                          const isInClientMatterNames = clientMatterNames.includes(normalizedFormattedName);

                          if (isInClientMatterNames) return null; // Don't render if already in the array

                          return (
                            <div
                              key={`${client.id}-${matter.id}`}
                              className={`p-3 hover:bg-[#F8F9FC] cursor-pointer transition-colors duration-150 ${focusedIndex === index
                                ? 'bg-[#F1F5F9] text-[#2A2E34]'
                                : 'text-[#2A2E34] hover:bg-[#F8F9FC]'
                                }`}
                              onClick={() => {
                                handleMatterChange(client.id, matter.id);
                                setOpenDropdownClientId(null);
                                setClientSearchTerms(prev => ({
                                  ...prev,
                                  [client.id]: '',
                                }));
                                setSearchResults([]);
                              }}
                              onMouseEnter={() => setFocusedIndex(index)}
                            >
                              {normalizedFormattedName}
                            </div>
                          );
                        })
                      ) : clientSearchTerm && clientSearchTerm.length >= 2 ? (
                        <div className="p-4 text-center text-[#5F6F84]">No matches found</div>
                      ) : (
                        <div className="p-4 text-center text-[#5F6F84]">No matters available</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Only show Add New Event buttons if a matter is selected, otherwise show prompt */}
        {selectedMatter ? (
          renderMatterEvents(client.id, selectedMatter)
        ) : (
          <div className="px-[20px] mt-[16px] pb-[20px] text-center">
            <div className="flex w-full gap-4">
              <button
                className={`flex items-center font-medium justify-center h-[36px] text-[#3F73F6] text-[14px] border border-[#3F73F6] rounded-[4px] py-[12px] px-[16px] w-full hover:bg-[#3F73F6] hover:text-white transition-colors duration-200 disabled:hover:bg-transparent disabled:hover:text-[#A2AFC2] disabled:hover:border-[#DCE2EB] ${conditionalDisabled(shouldDisableButtons, 'button', 'cursor-pointer')}`}
                onClick={() => setError('Please select a Client/Matter first')}
                disabled={isChildWorkflow ? false : isDisabled}
              >
                <span className="mr-2 text-xl">+</span>
                Add a new event
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Remove a client's contact card
  const removeClientCard = async (clientId: string) => {
    // Log the client being removed for debugging
    // console.log('Attempting to remove client with ID dhyey:', clientId);
    // console.log('Current clients before removal: dhyey', clients);

    // Set pending delete client ID and show confirmation modal instead of using confirm()
    setPendingDeleteClientId(clientId);
    setConfirmDeleteClientModalOpen(true);
  };

  // Handle confirmed client deletion
  const handleConfirmClientDelete = async () => {
    if (!pendingDeleteClientId) return;

    const clientId = pendingDeleteClientId;
    // console.log('Confirming deletion of client with dhyey ID:', clientId);

    try {
      const clientToRemove = clients.find(c => c.id === clientId);

      // console.log('🚀 ~ handleConfirmClientDelete ~ dhyey clientToRemove:', clientToRemove);

      if (!clientToRemove) {
        console.error('Client to remove not found:', clientId);
        return;
      }

      console.log('Found client to remove:', clientToRemove);

      // Update local state to remove only the specific client with this ID
      setClients((prevClients: ClientMatter[]) => {
        const remainingClients = prevClients.filter(client => client.id !== clientId);
        console.log('Clients after removal:', remainingClients);
        return remainingClients;
      });

      // Only remove events related to this specific client's matters
      const matterIdsToRemove = new Set(clientToRemove.matters.map(matter => matter.id));
      console.log('Matter IDs to remove events for:', Array.from(matterIdsToRemove));

      setEvents((prevEvents: Record<string, EventType[]>) => {
        const updatedEvents = { ...prevEvents };

        // Only remove events for matters belonging to this client
        Array.from(matterIdsToRemove).forEach(matterId => {
          delete updatedEvents[matterId];
        });

        return updatedEvents;
      });

      // Clean up related state for this specific client
      setSelectedMatters(prev => {
        const newState = { ...prev };
        delete newState[clientId];
        return newState;
      });

      // Also clean up client-specific search terms
      setClientSearchTerms(prev => {
        const newState = { ...prev };
        delete newState[clientId];
        return newState;
      });

      // Update AdvisorPanel event count for workflow name update
      if (typeof window !== 'undefined' && (window as unknown as { updateAdvisorPanelEventCount?: (clientName: string, count: number, absoluteSet?: boolean) => void }).updateAdvisorPanelEventCount) {
        const clientName = clientToRemove.name || 'Unknown Client';
        
        // Set client event count to 0 (absolute set) when client is deleted
        (window as unknown as { updateAdvisorPanelEventCount: (clientName: string, count: number, absoluteSet?: boolean) => void }).updateAdvisorPanelEventCount(clientName, 0, true);
        console.log('🔄 Set AdvisorPanel event count to 0 for deleted client:', clientName);
      }

      // console.log('🚀 ~ handleConfirmClientDelete ~ dhyey clientId:', clientId);
      // Call the parent component's onDeleteClient handler if provided
      // console.log('🚀 ~ handleConfirmClientDelete ~ dhyey onDeleteClient:', clientId);
      if (onDeleteClient) {
        onDeleteClient(clientId);
      }
      // Otherwise handle API call directly if fieldId and workflowId are available
      else if (fieldId && workflowId) {
        await apiDelete(`/workflow/court-notice/client`, {
          data: {
            field_id: fieldId,
            workflow_id: workflowId,
            client_id: clientId,
          },
        });
      }

      // Force re-render to ensure UI is up-to-date
      setLastContactAdded(Date.now());
    } catch (error) {
      console.error('Error removing client:', error);
      // Revert to previous state on error
      setError('Failed to remove client. Please try again.');
    } finally {
      // Reset pending delete client ID and close modal
      setPendingDeleteClientId(null);
      setConfirmDeleteClientModalOpen(false);
    }
  };

  // Add event to another contact button
  const renderAddContactButton = () => {
    // Check if any events across all clients are scheduled
    const hasAnyScheduledEvents = Object.values(events).some(matterEvents =>
      matterEvents.some(
        event => event.eventStatus === 'Synced' || event.eventStatus === 'SyncFailed'
      )
    );

    const shouldDisableAddContact = isChildWorkflow ? false :
      isDisabled ||
      (isUpdateMyCase && hasAnyScheduledEvents) ||
      hasAnyScheduledEvents;

    return (
      <button
        className={`${workflowStyles.courtNoticeAddButton} ${conditionalDisabled(shouldDisableAddContact, 'button', 'cursor-pointer')}`}
        disabled={shouldDisableAddContact}
        onClick={async () => {
          if (onAddContact) {
            console.log('🚀 ~ renderAddContactButton ~ dhyey onAddContact:', onAddContact);
            // First call the onAddContact function to open modal or handle UI
            onAddContact(true);
            // If we're in a workflow context, also make an API call
            try {
              if (fieldId && workflowId) {
                // Start loading state
                setLoading(true);

                // Add the newClient flag to indicate this client was added via "Add event to another contact"
                const response = await apiPost<{
                  statusCode: number;
                  data?: {
                    clients?: ClientMatter[];
                    events?: Record<string, EventType[]>;
                  };
                }>(`/workflow/court-notice/add-contact`, {
                  field_id: fieldId,
                  workflow_id: workflowId,
                  newClient: true, // Add this flag to mark as a new client from "Add event to another contact"
                });

                // If the API returns new client data, update the local state
                if (response.data?.data?.clients) {
                  // Mark any new clients with newClient: true if not already set
                  const newClients = response.data.data.clients.map((client: ClientMatter) => ({
                    ...client,
                    newClient: client.newClient === undefined ? true : client.newClient,
                  }));

                  // Special handling for the first contact being added
                  if (!firstContactAdded) {
                    // First time adding a contact, just set the clients directly
                    setClients(newClients);
                    setFirstContactAdded(true);
                    console.log('First contact added, setting clients directly:', newClients);
                  } else {
                    // For subsequent additions, merge with existing clients
                    setClients((prevClients: ClientMatter[]) => {
                      // Create map of existing client IDs to avoid duplicates
                      const existingClientIds = new Set(prevClients.map((c: ClientMatter) => c.id));

                      // Add only new clients that don't already exist
                      const clientsToAdd = newClients.filter(
                        (client: ClientMatter) => !existingClientIds.has(client.id)
                      );

                      console.log('Subsequent contact added, merging with existing clients:', {
                        existingCount: prevClients.length,
                        newCount: clientsToAdd.length,
                      });

                      // Return combined array with existing clients first, followed by new ones
                      return [...prevClients, ...clientsToAdd];
                    });
                  }
                }

                if (response.data?.data?.events) {
                  const newEvents = response.data?.data?.events || {};

                  // Special handling for first contact being added
                  if (!firstContactAdded) {
                    // First time adding a contact, directly set the events
                    setEvents(newEvents);
                    console.log('First contact events directly set:', newEvents);
                  } else {
                    // For subsequent additions, merge with existing events
                    setEvents((prevEvents: Record<string, EventType[]>) => {
                      // Create a merged events object
                      const mergedEvents = { ...prevEvents };

                      // Add or merge events for each matter ID
                      Object.keys(newEvents).forEach((matterId: string) => {
                        if (!mergedEvents[matterId]) {
                          // If no events exist for this matter, add them all
                          mergedEvents[matterId] = [...newEvents[matterId]];
                        } else {
                          // If events exist, add only new ones (avoiding duplicates by ID)
                          const existingEventIds = new Set(
                            mergedEvents[matterId].map((event: EventType) => event.id)
                          );
                          const eventsToAdd = newEvents[matterId].filter(
                            (event: EventType) => !existingEventIds.has(event.id)
                          );
                          mergedEvents[matterId] = [...mergedEvents[matterId], ...eventsToAdd];
                        }
                      });

                      console.log('Subsequent contact events merged:', {
                        prevMatterCount: Object.keys(prevEvents).length,
                        newMatterCount: Object.keys(newEvents).length,
                        resultMatterCount: Object.keys(mergedEvents).length,
                      });

                      return mergedEvents;
                    });
                  }
                }

                // Update lastContactAdded to force rerender
                setLastContactAdded(Date.now());
              }
            } catch (error) {
              console.error('Error adding contact:', error);
              setError('Failed to add new contact. Please try again.');
            } finally {
              setLoading(false);
            }
          }
        }}
      >
        <Plus
          size={20}
          className={`mr-2 ${conditionalDisabled(shouldDisableAddContact, 'icon')}`}
        />
        <p
          className={`text-[14px] font-medium leading-[20px] text-[#3F73F6] ${conditionalDisabled(shouldDisableAddContact, 'banner')}`}
        >
          Add event to another contact
        </p>
      </button>
    );
  };

  // Confirm and execute matter deletion
  const handleConfirmMatterDelete = async () => {
    if (!pendingDeleteMatterId) return;

    try {
      setLoading(true);

      // Find the client containing this matter to get client name for notice summary update
      const client = clients.find(client =>
        client.matters.some(
          matter => matter.id === pendingDeleteMatterId || matter._id === pendingDeleteMatterId || matter.matter_id === pendingDeleteMatterId
        )
      );

      // Update AdvisorPanel to remove this client from notice summary
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if (typeof window !== 'undefined' && (window as any).updateAdvisorPanelEventCount && client) {
        const clientName = client.name || 'Unknown Client';
        if (clientName && clientName !== 'Unknown Client') {
          // Set count to 0 to remove client from notice summary
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (window as any).updateAdvisorPanelEventCount(clientName, 0, true); // true indicates absolute set
          console.log('🔄 CourtNotice: Removed client from notice summary (matter deleted):', clientName);
        }
      }

      // Use the provided callback to handle deletion in parent
      if (onDeleteMatter) {
        onDeleteMatter(pendingDeleteMatterId);
      } else {
        // Handle deletion locally if no callback provided
        const updatedEvents = { ...events };
        delete updatedEvents[pendingDeleteMatterId];
        setEvents(updatedEvents);
      }

      // Reset state
      setPendingDeleteMatterId(null);
      setConfirmDeleteMatterModalOpen(false);
    } catch (error) {
      console.error('Error deleting matter:', error);
      setError('Failed to delete matter. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className={workflowStyles.courtNoticeContainer}>
        {/* <div className={workflowStyles.courtNoticeHeader}>
          <span className={workflowStyles.courtNoticeHeaderText}>{label}</span>
        </div> */}
        <div className="text-center p-8 border border-gray-200 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <div className="text-gray-500">Loading court notice data...</div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={workflowStyles.courtNoticeContainer}>
        {/* <div className={workflowStyles.courtNoticeHeader}>
          <span className={workflowStyles.courtNoticeHeaderText}>{label}</span>
        </div> */}
        <div className="text-center p-8 border border-gray-200 rounded-lg bg-red-50">
          <AlertTriangle size={24} className="text-red-500 mx-auto mb-2" />
          <div className="text-red-500 mb-2">{error}</div>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md"
            onClick={() => setError(null)}
          >
            Dismiss
          </button>
        </div>
      </div>
    );
  }

  // If no clients are available
  if (clients.length === 0) {
    return (
      <div className={workflowStyles.courtNoticeContainer}>
        {/* <div className={workflowStyles.courtNoticeHeader}>
          <span className={workflowStyles.courtNoticeHeaderText}>{label}</span>
        </div> */}
        <div className="text-center p-8 border border-gray-200 rounded-[12px]">
          <Calendar size={24} className="text-gray-400 mx-auto mb-2" />
          <div className="text-gray-500 mb-4">Start by clicking on the button below</div>
          {onAddContact && renderAddContactButton()}
        </div>
      </div>
    );
  }

  // Main component render (using lastContactAdded to help with forcing rerenders)
  return (
    <div className={workflowStyles.courtNoticeContainer} key={`container-${lastContactAdded}`}>
      {/* Client sections */}
      {clients.map(client => renderClientSection(client))}

      {/* Add event to another contact button - only show if onAddContact is provided */}
      {onAddContact && renderAddContactButton()}

      {/* Edit/Add modal */}
      <CourtNoticeEditModal
        isOpen={editModalOpen}
        onClose={() => {
          if (isPdfBeingViewed) {
            console.log('Preventing modal close - PDF is being viewed');
            return;
          }
          setEditModalOpen(false);
        }}
        event={currentEditEvent}
        clientId={currentMatterId}
        onSave={handleSaveEditedEvent}
        options={localOptions}
        isDisabled={isDisabled}
        workflowId={workflowId}
        titleOfEvent={'Edit event'}
        isEditMode={isEditMode}
        isChildWorkflow={isChildWorkflow}
        clientName={selectedMatterClientNames[currentMatterId] || ''}
        isTaskReviewed={isTaskReviewed}
        selectedMatter={selectedMatter}
        onPdfView={() => setIsPdfBeingViewed(true)}

      />

      {/* Confirm Delete Event Modal */}
      <ReusableAlertModal
        isOpen={confirmDeleteEventModalOpen}
        title="Confirm Delete"
        message="Are you sure you want to remove this event?"
        onOk={handleConfirmEventDelete}
        onCancel={() => {
          setPendingDeleteEventData(null);
          setConfirmDeleteEventModalOpen(false);
        }}
        okText="Delete"
        cancelText="Cancel"
      />

      {/* Confirm Delete Client Modal */}
      <ReusableAlertModal
        isOpen={confirmDeleteClientModalOpen}
        title="Confirm Delete"
        message="Are you sure you want to remove this client?"
        onOk={handleConfirmClientDelete}
        onCancel={() => {
          setPendingDeleteClientId(null);
          setConfirmDeleteClientModalOpen(false);
        }}
        okText="Delete"
        cancelText="Cancel"
      />

      {/* Confirm Delete Matter Modal */}
      <ReusableAlertModal
        isOpen={confirmDeleteMatterModalOpen}
        title="Confirm Delete"
        message="Are you sure you want to remove this matter?"
        onOk={handleConfirmMatterDelete}
        onCancel={() => {
          setPendingDeleteMatterId(null);
          setConfirmDeleteMatterModalOpen(false);
        }}
        okText="Delete"
        cancelText="Cancel"
      />
    </div>
  );
};

export default CourtNotice;

// Timezone utility functions
const getUserTimezone = (): string => {
  if (typeof window === 'undefined') return 'UTC';
  return localStorage.getItem('timezone') || Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
};


const convertTimeFromUTCReliable = (utcTimeString: string, dateString: string, userTimezone?: string): string => {
  try {
    if (!utcTimeString || !dateString) return utcTimeString;

    const timezone = userTimezone || getUserTimezone();
    const utcMoment = moment.tz(`${dateString} ${utcTimeString}`, 'YYYY-MM-DD HH:mm', 'UTC');
    const localMoment = utcMoment.clone().tz(timezone);
    console.log(localMoment, 'localMoment in convertTimeFromUTCReliable');
    return localMoment.format('HH:mm');
  } catch (error) {
    console.error('Error converting time from UTC (reliable):', error);
    return utcTimeString;
  }
};

const getCurrentDateInUserTimezone = (timezone?: string): string => {
  const userTimezone = timezone || getUserTimezone();

  console.log(userTimezone, 'userTimezone in getCurrentDateInUserTimezone');
  return moment.tz(userTimezone).format('YYYY-MM-DD');
};

// Convert time from user timezone to UTC for storage
const convertTimeToUTCReliable = (timeString: string, dateString: string, userTimezone?: string): string => {
  try {
    if (!timeString || !dateString) return timeString;

    const timezone = userTimezone || getUserTimezone();
    const localMoment = moment.tz(`${dateString} ${timeString}`, 'YYYY-MM-DD HH:mm', timezone);
    const utcMoment = localMoment.clone().utc();
    console.log(utcMoment, 'utcMoment in convertTimeToUTCReliable');
    return utcMoment.format('HH:mm');
  } catch (error) {
    console.error('Error converting time to UTC (reliable):', error);
    return timeString;
  }
};
