import React from 'react';
import { EventType } from '../CourtNotice';
import CourtNoticeFormModal from './CourtNoticeFormModal';

interface Matter {
  _id: string;
  name: string;
  client_id: string;
  is_active: boolean;
  client_name: string;
  text: string;
  id: string;
  ex_county_of_arrest: string;
  case_number: string;
}

interface CourtNoticeEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: EventType | null;
  clientId: string;
  onSave: (clientId: string, updatedEvent: EventType) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options?: any;
  isDisabled?: boolean;
  workflowId?: string;
  isChildWorkflow?: boolean;
  titleOfEvent?: string;
  clientName?: string;
  isEditMode?: boolean;
  isTaskReviewed?: boolean;
  selectedMatter?: Matter;
  onPdfView?: () => void;
}

const CourtNoticeEditModal: React.FC<CourtNoticeEditModalProps> = ({
  isOpen,
  onClose,
  event,
  clientId,
  onSave,
  options,
  isDisabled,
  workflowId,
  isChildWorkflow,
  titleOfEvent,
  clientName,
  isEditMode,
  isTaskReviewed,
  selectedMatter,
  onPdfView,

}) => {
  // Determine if this is a new event or editing an existing one
  // If the event has an ID that starts with "evt-" and it doesn't have much data,
  // it's likely a new event template
  const isNewEvent = event?.id?.startsWith('evt-') && !event?.courtNoticeType;
  const title = isNewEvent ? 'Add a new event' : 'Edit event';

  let baseEvent = event;

  // If event exists and has courtNoticeType but doesn't have courtNoticeActions, add it
  // if (event && event.courtNoticeType && !event.courtNoticeActions) {
  //   baseEvent = {
  //     ...event,
  //     courtNoticeActions: event.courtNoticeType,
  //   };
  // }

  if (event && event.files && event.files.length > 0) {
    // If files are strings, convert them to objects with name and key
    if (typeof event.files[0] === 'string' && baseEvent) {
      const staticPrefix = 'event/court-notice/';
      const formattedFiles = (event.files as unknown as string[]).map(file => ({
        name: file.substring(staticPrefix.length) ?? file,
        key: file,
      }));

      baseEvent = {
        ...baseEvent,
        id: baseEvent.id || `evt-${Date.now()}`, // Ensure id is always present
        files: formattedFiles,
      } as EventType; // Type assertion to ensure compatibility
    }
    // If files are already objects with name and key, keep as is
  }

  // Then apply the existing formatting logic
  const preparedEvent =
    baseEvent && !baseEvent.clientName?.includes('|') && isNewEvent
      ? {
        ...baseEvent,
        // If client name doesn't include a matter separator, try to extract it from the subject
        clientName: baseEvent.subject?.includes('-')
          ? `${baseEvent.subject.split('-')[0].trim()} | ${baseEvent.subject?.split('-')[1]?.replace('New Appointment', '').trim()}`
          : baseEvent.clientName || '',
      }
      : baseEvent;

  return (
    <CourtNoticeFormModal
      key={`${event?.id || 'new'}-${isNewEvent ? 'new' : 'edit'}`}
      isOpen={isOpen}
      onClose={onClose}
      event={preparedEvent}
      clientId={clientId}
      onSave={onSave}
      options={options}
      isNew={isNewEvent}
      title={title}
      isDisabled={isDisabled}
      workflow_id={workflowId}
      isChildWorkflow={isChildWorkflow}
      titleOfEvent={titleOfEvent}
      clientName={clientName}
      isEditMode={isEditMode}
      isTaskReviewed={isTaskReviewed}
      selectedMatter={selectedMatter}
      onPdfView={onPdfView}

    />
  );
};

export default CourtNoticeEditModal;
