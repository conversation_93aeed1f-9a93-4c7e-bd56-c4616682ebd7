import React, { useState, useEffect, useRef } from 'react';
import { courtNoticeService } from '@/services/api';
import { CourtNoticeItem, CourtNoticeListParams, mapStatusVariant } from '@/types/courtNotice';
import { useRouter } from 'next/router';
import Image from 'next/image';
import PageHeader from '@/components/common/PageHeader';
import DataTable from '@/components/common/DataTable';
import Pagination from '@/components/common/Pagination';
import SaveViewModal from '@/components/common/SaveViewModal';
import StatusBadge from '@/components/common/StatusBadge';
import { UserView, UserViewService } from '@/services/userViewService';

interface CourtNoticeFollowUpProps {
  savedView?: UserView;
  viewTitle?: string;
}

interface Column {
  id: string;
  header: string;
  width?: string;
  sortable?: boolean;
  sortField?: string;
  cell: (row: CourtNoticeItem) => React.ReactNode;
}

const CourtNoticeFollowUp: React.FC<CourtNoticeFollowUpProps> = ({
  savedView,
  viewTitle = 'Court Notice',
}) => {
  const router = useRouter();
  const [workflows, setWorkflows] = useState<CourtNoticeItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedWorkflows, setSelectedWorkflows] = useState<string[]>([]);
  const [showSaveViewModal, setShowSaveViewModal] = useState(false);
  const [savingView, setSavingView] = useState(false);
  const [currentFilters, setCurrentFilters] = useState<
    Array<{
      fieldName: string;
      filter: string;
      value: string[];
    }>
  >([]);
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Pagination state
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  // Add ref to track the latest request ID for deduplication
  const requestIdRef = useRef(0);

  useEffect(() => {
    // Apply saved view filters if available
    if (savedView?.filters) {
      const filters = savedView.filters;
      if (filters.search && typeof filters.search === 'string') setSearchTerm(filters.search);
      if (filters.sortBy && typeof filters.sortBy === 'string') setSortBy(filters.sortBy);
      if (
        filters.sortOrder &&
        typeof filters.sortOrder === 'string' &&
        (filters.sortOrder === 'desc' || filters.sortOrder === 'asc')
      )
        setSortOrder(filters.sortOrder);
    }
  }, [savedView]);

  useEffect(() => {
    fetchCourtNoticeList();
  }, [pagination.page, pagination.limit, sortBy, sortOrder, searchTerm]);

  const fetchCourtNoticeList = async () => {
    // Generate a unique request ID for this call
    const currentRequestId = ++requestIdRef.current;

    try {
      setLoading(true);
      setError(null);
      localStorage.setItem('workflowShowAllToggle', String(false));

      const params: CourtNoticeListParams = {
        page: pagination.page,
        limit: pagination.limit,
        type: 'follow_up',
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (sortBy) {
        params.sortBy = sortBy;
        params.sortOrder = sortOrder;
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const response: any = await courtNoticeService.getCourtNoticeList(params);

      // Check if this is still the latest request - if not, ignore the response
      if (currentRequestId !== requestIdRef.current) {
        console.log('Ignoring outdated API response, request ID:', currentRequestId);
        return;
      }

      // Handle cancellation (response is null)
      if (response === null) {
        console.log('Request was cancelled, ignoring response');
        return;
      }

      if (response.statusCode === 200 || response.statusCode === 201) {
        // Double-check this is still the latest request before updating state
        if (currentRequestId === requestIdRef.current) {
          setWorkflows(response.data.fields);
          setPagination({
            ...pagination,
            total: response.data.total,
          });
        } else {
          console.log(
            'Ignoring outdated API response during state update, request ID:',
            currentRequestId
          );
        }
      } else {
        // Only set error if this is still the latest request
        if (currentRequestId === requestIdRef.current) {
          setError('Failed to fetch court notice data');
        }
      }
    } catch (error: unknown) {
      // Only log error and set error state if this is still the latest request
      if (currentRequestId === requestIdRef.current) {
        // Check if this is a cancellation error - don't show it as an error to the user
        if (
          (error as Error).name === 'CanceledError' ||
          (error as Error).name === 'AbortError' ||
          (error as Error).message === 'canceled'
        ) {
          console.log('Request was cancelled, ignoring error');
          return;
        }

        console.error('Error fetching court notice list:', error);
        setError('Failed to fetch court notice data');
      } else {
        console.log('Ignoring error from outdated request, request ID:', currentRequestId);
      }
    } finally {
      // Only update loading state if this is still the latest request
      if (currentRequestId === requestIdRef.current) {
        setLoading(false);
      }
    }
  };

  const handleRowClick = (row: CourtNoticeItem) => {
    router.push(`/workflowrun?taskId=${row.last_task_id}&work_flow_id=${row._id}`);
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleItemsPerPageChange = (limit: number) => {
    setPagination(prev => ({ ...prev, limit, page: 1 }));
  };

  const handleSelectWorkflow = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedWorkflows(prev => [...prev, id]);
    } else {
      setSelectedWorkflows(prev => prev.filter(wfId => wfId !== id));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedWorkflows(workflows.map(item => item.id.toString()));
    } else {
      setSelectedWorkflows([]);
    }
  };

  const handleFilter = () => {
    console.log('Filter button clicked');
  };

  const handleColumns = () => {
    console.log('Columns button clicked');
  };

  // Helper function to extract matter name from full value (part after '|')
  const extractMatterName = (value: string): string => {
    if (typeof value === 'string' && value.includes('|')) {
      return value.split('|')[1]?.trim() || value;
    }
    return value;
  };

  // Helper function to check if field is a matter field
  const isMatterField = (field: string) => {
    const f = (field || '').toLowerCase();
    return f === 'matter' || f === 'matter_name' || f === 'matters';
  };

  const handleFilters = (
    filters: Array<{
      id: string;
      field: string;
      operator: string;
      value: string;
      selectedValues?: string[];
    }>
  ) => {
    console.log('Filters changed', filters);

    // Convert FilterRow format to backend format
    const backendFilters = filters
      .filter(
        f =>
          f.field &&
          f.operator &&
          (f.value || f.selectedValues?.length || f.operator === 'unassigned')
      )
      .map(f => {
        // Map frontend field names to backend field names
        const fieldMap: Record<string, string> = {
          'Workflow Name': 'template_name',
          Name: 'template_name', // Keep for backward compatibility
          Assignee: 'assigned_users',
          Status: 'status',
          Contact: 'contact',
          Matter: 'matter',
          Templates: 'templates',
          'Due date': 'due_date',
          'Create date': 'created_date',
          Attorney: 'attorney',
        };

        // Map frontend operators to backend operators
        const operatorMap: Record<string, string> = {
          contains: 'contains',
          equals: 'equal',
          not_contains: 'not_contains',
          not_equals: 'not_equal',
          unassigned: 'unassigned',
          before: 'before',
          after: 'after',
          between: 'between',
        };

        const fieldName = fieldMap[f.field] || f.field;
        const operator = operatorMap[f.operator] || f.operator;

        // Handle unassigned filter - no values needed
        let value: string[];
        if (f.operator === 'unassigned') {
          value = [];
        } else {
          const rawValues =
            f.selectedValues && f.selectedValues.length > 0
              ? f.selectedValues
              : f.value.split(', ').filter(v => v.trim());

          // Extract matter names for matter fields
          if (isMatterField(f.field)) {
            value = rawValues.map(extractMatterName);
          } else {
            value = rawValues;
          }
        }

        return {
          fieldName,
          filter: operator,
          value,
        };
      });

    // Final sanitization: ensure all matter field values are properly extracted
    const sanitizedBackendFilters = backendFilters.map(filter => {
      if (isMatterField(filter.fieldName) && filter.value && Array.isArray(filter.value)) {
        return {
          ...filter,
          value: filter.value.map(v => extractMatterName(String(v))),
        };
      }
      return filter;
    });

    setCurrentFilters(sanitizedBackendFilters);
  };

  const handleSaveView = () => {
    setShowSaveViewModal(true);
  };

  const handleSaveViewSubmit = async (viewName: string) => {
    try {
      setSavingView(true);

      // Get current filters and columns state for Court Notice Follow-up
      const viewData = {
        name: viewName,
        type: 'follow_up', // enum: [my_work_flow, completed, archive, new_court_notice, follow_up]
        userId: 1, // TODO: Get from auth context
        filters: {
          search: searchTerm,
          sortBy,
          sortOrder,
        },
        columns: columns.map(col => ({
          FieldName: col.header,
          searchable: true,
          sortable: col.sortable || false,
          visible: true,
        })),
        filter: currentFilters,
      };

      const response = await UserViewService.saveUserView(viewData);

      if (response.data.success) {
        console.log('View saved successfully:', response.data.view);
        // Refresh the sidebar views
        const refreshSidebarViews = (window as unknown as { refreshSidebarViews?: () => void })
          .refreshSidebarViews;
        if (refreshSidebarViews) {
          refreshSidebarViews();
        }
        // You could show a success toast here
      } else {
        console.error('Failed to save view:', response.data.message);
        // You could show an error toast here
      }
    } catch (error) {
      console.error('Error saving view:', error);
      // You could show an error toast here
    } finally {
      setSavingView(false);
      setShowSaveViewModal(false);
    }
  };

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const columns: Column[] = [
    {
      id: 'workflowRun',
      header: 'WORKFLOW RUN',
      width: '200px',
      sortable: true,
      sortField: 'work_flow_runner',
      cell: (row: CourtNoticeItem) => (
        <div
          className="text-[14px] text-[#2A2E34] cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          {row.work_flow_runner.split(',').length > 2
            ? row.work_flow_runner.split(',').slice(0, 2).join('   &') + '...'
            : row.work_flow_runner.split(',').join('   &')}
        </div>
      ),
    },
    {
      id: 'matter',
      header: 'MATTER',
      sortable: true,
      sortField: 'matter',
      cell: (row: CourtNoticeItem) => (
        <div className="text-[14px] font-normal cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.matter}
        </div>
      ),
    },
    {
      id: 'attorney',
      header: 'ATTORNEY',
      sortable: true,
      sortField: 'attorney',
      cell: (row: CourtNoticeItem) => (
        <div className="text-[14px] font-normal cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.attorney}
        </div>
      ),
    },
    {
      id: 'started',
      header: 'STARTED',
      sortable: true,
      sortField: 'start_date',
      cell: (row: CourtNoticeItem) => (
        <div className="text-[14px] font-normal cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.start_date}
        </div>
      ),
    },
    {
      id: 'due',
      header: 'DUE',
      sortable: true,
      sortField: 'end_date',
      cell: (row: CourtNoticeItem) => (
        <div className="text-[14px] font-normal cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.end_date}
        </div>
      ),
    },
    {
      id: 'status',
      header: 'STATUS',
      sortable: false,
      sortField: 'status',
      cell: (row: CourtNoticeItem) => (
        <div onClick={() => handleRowClick(row)} className="cursor-pointer">
          <StatusBadge status={mapStatusVariant(row.status)} variant="fill" />
        </div>
      ),
    },
    {
      id: 'activity',
      header: 'ACTIVITY',
      sortable: true,
      sortField: 'last_activity',
      cell: (row: CourtNoticeItem) => (
        <div className="text-[14px] font-normal cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.last_activity}
        </div>
      ),
    },
    {
      id: 'assignee',
      header: 'ASSIGNEE',
      sortable: false,
      sortField: 'assigned_users',
      cell: (row: CourtNoticeItem) => {
        const assignedUsers =
          row.assigned_users &&
          typeof row.assigned_users === 'string' &&
          row.assigned_users.trim() !== ''
            ? row.assigned_users
                .split(',')
                .map(s => s.trim())
                .filter(s => s.length > 0)
            : [];

        const userCount = assignedUsers.length || 0;
        const firstUser = assignedUsers[0];

        const roleKeywords = ['Paralegal', 'Attorney', 'Intake', 'Billing'];
        const hasRoleKeyword = assignedUsers.some(user =>
          roleKeywords.some(keyword => user.toLowerCase().includes(keyword.toLowerCase()))
        );

        return (
          <div
            className="relative w-[24px] h-[24px] cursor-pointer"
            onClick={() => handleRowClick(row)}
          >
            {hasRoleKeyword ? (
              <>
                <div className="w-[24px] h-[24px] rounded-full bg-[#3f73f65e] flex items-center justify-center">
                  <Image src="/assets/Group-icon.svg" alt="Group icon" width={25} height={25} />
                </div>
                <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                  {userCount}
                </span>
              </>
            ) : firstUser ? (
              <>
                <div className="w-[24px] h-[24px] rounded-full bg-[#5F6F84] text-white flex items-center justify-center font-semibold text-[10px]">
                  {getInitials(firstUser)}
                </div>
                <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                  {userCount}
                </span>
              </>
            ) : (
              <>
                <div className="w-[24px] h-[24px] rounded-full bg-[#C5D5FC] flex items-center justify-center font-semibold">
                  <Image
                    src="/assets/ai-robot-new-2.svg"
                    alt="AI assistant"
                    width={25}
                    height={25}
                  />
                </div>
                <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                  {userCount}
                </span>
              </>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <div className="p-6">
      <PageHeader
        title={viewTitle}
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        onFilter={handleFilter}
        onColumns={handleColumns}
        onSaveView={handleSaveView}
        showSaveView={true} // Always show save button
        disableSaveView={!!savedView} // Disable save button if this is a saved view
        savedFilters={currentFilters}
        onFiltersChange={handleFilters}
      />

      {error && <div className="mt-4 p-3 bg-red-100 text-red-700 rounded">{error}</div>}

      <DataTable
        columns={columns}
        data={workflows}
        selectedIds={selectedWorkflows}
        onSelectRow={handleSelectWorkflow}
        onSelectAll={handleSelectAll}
        isAllSelected={selectedWorkflows.length === workflows.length && workflows.length > 0}
        className="mt-4"
        showHeader={true}
        isLoading={loading}
        currentSortBy={sortBy}
        currentSortOrder={sortOrder}
        onSort={handleSort}
        idField="id"
      />

      {/* Pagination */}
      {!loading && workflows.length > 0 && (
        <Pagination
          currentPage={pagination.page}
          totalPages={totalPages}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
          className="mt-6"
        />
      )}

      <SaveViewModal
        isOpen={showSaveViewModal}
        onClose={() => setShowSaveViewModal(false)}
        onSave={handleSaveViewSubmit}
        isLoading={savingView}
      />
    </div>
  );
};

export default CourtNoticeFollowUp;
