import React from 'react';
// import { useSelector } from 'react-redux';
// import { RootState } from '@/redux/types';
import Link from 'next/link';
// import { useRouter } from 'next/router';
import Image from 'next/image';
import { usePathname } from 'next/navigation';

const Sidebar: React.FC = () => {
  // const sidebar = useSelector((state: RootState) => state.sidebar);
  // const router = useRouter();
  const pathname = usePathname();

  // Define navigation items to match the exact appearance in the image
  const navItems = [
    {
      title: 'Document',
      icon: '/assets/sidebar-hamburger.svg',
      path: '#',
    },
  ];
  const sidebarIcon = [
    {
      title: 'Home',
      icon: '/assets/home-02.svg',
      path: '#',
    },
    {
      title: 'Workflows',
      icon: '/assets/eye.svg',
      path: '/all-work-flow?type=new-court-notice',
    },
    {
      title: 'Contacts',
      icon: '/assets/contacts-1.svg',
      path: '#',
    },
    {
      title: 'Home',
      icon: '/assets/bag.svg',
      path: '#',
    },
    {
      title: 'Home',
      icon: '/assets/billing.svg',
      path: '#',
    },
    // patel
    {
      title: 'Home',
      icon: '/assets/pie-chart-report.svg',
      path: '#',
    },
    {
      title: 'Calendar',
      icon: '/assets/calendar.svg',
      path: '/events',
    },
    {
      title: 'Home',
      icon: '/assets/user-admin.svg',
      path: '#',
    },
  ];

  const isActive = (itemPath: string) => {
    if (itemPath === '#') return false;

    // Special case for workflows - check if we're on the new-court-notice page specifically
    if (itemPath === '/all-work-flow?type=new-court-notice') {
      // Check if we're on the new-court-notice workflow page
      return (
        pathname === '/all-work-flow' &&
        typeof window !== 'undefined' &&
        window.location.search === '?type=new-court-notice'
      );
    }

    // For other workflow pages, check the full path and query params
    if (itemPath.startsWith('/all-work-flow')) {
      const url = new URL(itemPath, 'http://localhost');
      const queryType = url.searchParams.get('type');
      if (typeof window !== 'undefined') {
        const currentUrl = new URL(window.location.href);
        const currentType = currentUrl.searchParams.get('type');
        return pathname === '/all-work-flow' && currentType === queryType;
      }
    }

    return pathname === itemPath;
  };

  return (
    <aside className="h-screen bg-[#233B5B] text-white flex flex-col w-[60px]">
      <div className="flex flex-col items-center h-[436px] flex-grow">
        {navItems.map((item, index) => (
          <Link
            key={index}
            href={item.path}
            className="flex justify-center border-b-[1px] border-[#ffffff20] w-[60px] h-[60px] items-center p-[20px] gap-[10px]"
          >
            <div className={`flex justify-center items-center rounded-[12px]`}>
              <Image src={item.icon} alt="Sidebar" width={20} height={20} />
            </div>
          </Link>
        ))}
        {sidebarIcon.map((item, index) => (
          <Link
            key={index}
            href={item.path}
            className="flex justify-center w-[60px] h-[60px] items-center"
          >
            <div
              className={`flex justify-center mt-[12px] items-center rounded-[12px] w-[44px] h-[44px]
                ${isActive(item.path) ? 'bg-[#3F73F6] hover:bg-[#3F73F6]' : 'hover:bg-white/10'}`}
            >
              <Image 
                src={isActive(item.path) ? item.icon.replace('.svg', '-active.svg') : item.icon} 
                alt="Sidebar" 
                width={20} 
                height={20} 
              />
            </div>
          </Link>
        ))}
      </div>
    </aside>
  );
};

export default Sidebar;
