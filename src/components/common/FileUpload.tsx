/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * FileUpload Component - Reset and Fixed Version
 *
 * This component handles file uploads to S3 with proper renaming and access functionality.
 *
 * Key Features:
 * 1. Proper S3 key tracking for upload and rename operations
 * 2. Clean separation between display names and S3 filenames
 * 3. Reliable file access via presigned URLs
 * 4. Robust rename functionality
 * 5. Multiple file format support
 */
import React, {
  useState,
  useCallback,
  useRef,
  forwardRef,
  useImperativeHandle,
  useEffect,
} from 'react';
import ReactDOM from 'react-dom';
import { X, FileText, Image as ImageIcon } from 'lucide-react';
import { VALIDATIONS } from '@/constants/workflow';
import { useRouter } from 'next/router';
import apiClient from '@/services/api/config';

export interface UploadedFile {
  id: string;
  name?: string; // Display name shown to user
  s3Key?: string; // Current S3 key (path + filename)
  originalS3Key?: string; // Original S3 key when first uploaded
  s3FileName?: string; // S3 filename with unique ID
  displayName?: string; // Display name for UI
  size?: number;
  type?: string;
  url?: string;
  progress?: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
  originalName?: string; // Original file name from user's computer
  isDeleted?: boolean;
  isEditing?: boolean;
  isNameChanged?: boolean; // Whether this file has been renamed
  path?: string;
  editingName?: string;
  uniqueId?: string;
  key?: string; // For backward compatibility
  oldS3Key?: string; // For rename operations
  oldFileName?: string; // For rename operations
  pendingNewName?: string; // New name that will be applied on save
  originalDisplayName?: string; // Original display name before rename
  isRenamed?: boolean; // Flag to indicate this file has been renamed in UI
  // Story requirement: File versioning fields
  isRenamedVersion?: boolean; // Is this a renamed version?
  originalFileId?: string; // Link to original file
  hasRenamedVersion?: boolean; // Does original have renamed version?
  renamedVersionId?: string; // Link to renamed version
  renamedAt?: Date; // When was it renamed?
  mycaseDocumentId?: string; // MyCase document ID for integration
}

// Interface for available files that can be selected (from task execution)
interface AvailableFile {
  id?: string;
  uniqueId?: string;
  name: string;
  originalName: string;
  size: number;
  type: string;
  url?: string;
  s3Key?: string;
  uploadDate?: string;
  // Story requirement: File versioning fields
  isRenamedVersion?: boolean;
  originalFileId?: string;
  hasRenamedVersion?: boolean;
  renamedVersionId?: string;
  renamedAt?: Date;
  isNameChanged?: boolean;
  mycaseDocumentId?: string; // MyCase document ID for integration
}

interface FileUploadProps {
  value: UploadedFile[];
  onChange: React.Dispatch<React.SetStateAction<UploadedFile[]>>;
  maxFileSize?: number;
  allowedFileTypes?: string[];
  maxFiles?: number;
  path?: string;
  courtNoticeType?: string;
  clientLastName?: string;
  actionType?: string;
  startDate?: string;
  disabled?: boolean;
  workflowId?: string;
  taskExecutionId?: string; // New prop for task execution ID
  eventId?: string; // New prop for event ID
  isViewMode?: boolean; // New prop to indicate if in view mode (disable edit/delete)
  isTaskReviewed?: boolean; // New prop to indicate if task is reviewed (also disable edit/delete)
  isNewEvent?: boolean; // New prop to indicate if this is a new event being created (prevents fetching existing files)
  onPdfView?: () => void; // Callback when PDF is being viewed
  isChildWorkflow?: boolean; // New prop to indicate if this is a child workflow
}



export interface FileUploadRef {
  getFilesForEvent: () => Array<{
    uniqueId: string | undefined;
    name: string | undefined;
    s3FileName: string | undefined;
    url: string | undefined;
    key: string | undefined;
    originalName: string | undefined;
    size: number | undefined;
    type: string | undefined;
  }>;
  getRenamedFiles: () => Array<{
    fileId: string;
    originalName: string;
    newName: string;
  }>;
  clearRenamesFromStorage: () => void;
  saveRenamestoStorage: () => void;
  removeFileFromStorage: (fileId: string) => void;
  cleanupDeletedFilesFromStorage: (deletedFileIds: string[]) => void;
}

const FileUpload = forwardRef<FileUploadRef, FileUploadProps>(
  (
    {
      value,
      onChange,
      // maxFileSize = 5 * 1024 * 1024, // 5MB default as requested
      // allowedFileTypes = [
      //   'application/pdf',
      //   'application/msword',
      //   'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      //   'application/vnd.ms-excel',
      //   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      //   'image/jpeg',
      //   'image/jpg',
      //   'image/png',
      //   'image/svg+xml',
      //   'image/tiff',
      //   'image/bmp',
      //   'image/gif',
      // ],
      maxFiles = 50,
      // path = '',
      courtNoticeType = '',
      // clientLastName = '',
      actionType,
      startDate,
      // disabled = false,
      taskExecutionId,
      workflowId,
      eventId,
      isViewMode = false,
      isTaskReviewed = false,
      isNewEvent = false,
      onPdfView,
      isChildWorkflow = false,
    },
    ref
  ) => {
    const router = useRouter();

    // Helper variable for disabled condition - disable when in view mode OR task is reviewed
    const isDisabled = isViewMode || isTaskReviewed;


    // const [isDragging, setIsDragging] = useState(false);
    const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
    const [availableFiles, setAvailableFiles] = useState<AvailableFile[]>([]);
    const [loadingFiles, setLoadingFiles] = useState(false);
    // const [isGeneratingUrl, setIsGeneratingUrl] = useState<Record<string, boolean>>({});
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [inputWidths, setInputWidths] = useState<Record<string, number>>({});
    const [showFileLimitAlert, setShowFileLimitAlert] = useState(false);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [editingFileId, setEditingFileId] = useState<string | null>(null);
    // const [selectedForBulkAdd, setSelectedForBulkAdd] = useState<Set<string>>(new Set());
    const [editingFileName, setEditingFileName] = useState('');
    const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number; width: number; isAbove: boolean } | null>(null);
    const measureRef = useRef<HTMLSpanElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const triggerRef = useRef<HTMLDivElement>(null);

    const fetchAvailableFiles = useCallback(async () => {

      const workflowId = router.query.work_flow_id
      if (!workflowId) {
        console.log("No workflowId provided, setting empty files");
        setAvailableFiles([]);
        return;
      }

      setLoadingFiles(true);
      try {

        const response = await apiClient.get(`/workflow/files/${workflowId}`);

        console.log("📂 Available files response:", response.data);
        const result = response.data;

        if (result.data?.success && result.data?.files) {
          setAvailableFiles(result.data.files);
          console.log("📂 Available files loaded:", result.data.files.length);
        } else {
          setAvailableFiles([]);
          console.log("📂 No available files found or invalid response structure");
        }
      } catch (error: any) {
        console.error('📂 Error fetching available files:', error);

        // Enhanced error logging for debugging
        if (error.response) {
          console.error('📂 Available Files API Error:', {
            status: error.response.status,
            statusText: error.response.statusText,
            data: error.response.data
          });
        } else if (error.request) {
          console.error('📂 Available Files Network Error:', error.request);
        } else {
          console.error('📂 Available Files Request Error:', error.message);
        }

        setAvailableFiles([]);
      } finally {
        setLoadingFiles(false);
      }
    }, [router.query.work_flow_id]);

    useEffect(() => {
      if (workflowId) {
        fetchAvailableFiles();
      }
    }, [router.query.work_flow_id, fetchAvailableFiles]);

    // Fetch event files when eventId is provided
    const fetchEventFiles = useCallback(async () => {
      // Use props instead of router query
      const currentTaskExecutionId = taskExecutionId || router.query.taskId;
      console.log('workflowId', workflowId)
      const currentWorkflowId = workflowId;



      if (!currentTaskExecutionId || !currentWorkflowId || !eventId) {
        console.log("Missing required IDs:", {
          currentTaskExecutionId: !!currentTaskExecutionId,
          currentWorkflowId: !!currentWorkflowId,
          eventId: !!eventId
        });
        return;
      }

      console.log("📂 Fetching event files for:", {
        taskExecutionId: currentTaskExecutionId,
        workflowId: currentWorkflowId,
        eventId
      });

      try {
        // Use apiClient which handles authentication automatically
        const timestamp = new Date().getTime();
        const endpoint = `/workflow/event/${currentTaskExecutionId}/${eventId}/files?t=${timestamp}&workflow_id=${currentWorkflowId}`;

        console.log("📂 Making authenticated API call:", {
          endpoint,
          taskExecutionId: currentTaskExecutionId,
          workflowId: currentWorkflowId,
          eventId,
          timestamp
        });

        const response = await apiClient.get(endpoint);

        console.log("📂 Response status:", response.status);
        console.log("📂 Response data:", response.data);

        // axios response data is already parsed JSON
        const result = response.data;

        // Process files if available
        if (result.data?.files && Array.isArray(result.data.files)) {
          // Map API response to UploadedFile format with proper name handling
          const eventFiles: UploadedFile[] = result.data.files.map((file: any, index: number) => {
            const fileId = file.id || file.uniqueId || `event-file-${index}`;

            // Use newName if available (renamed file), otherwise use name, fallback to originalName
            const displayName = file.newName || file.name || file.originalName || `file-${index}`;
            const originalName = file.originalName || file.name || displayName;

            console.log(`📂 Processing file ${index}:`, {
              id: fileId,
              name: file.name,
              newName: file.newName,
              originalName: file.originalName,
              displayName,
              isNameChanged: file.isNameChanged
            });

            return {
              id: fileId,
              name: displayName, // Display name (newName if renamed, otherwise original name)
              originalName: originalName, // Always preserve original name
              s3Key: file.s3Key || '',
              s3FileName: file.name || displayName, // S3 stored name
              displayName: displayName, // What user sees
              size: file.size || 0,
              type: file.type || file.contentType || 'application/pdf',
              url: file.url || '',
              status: 'completed' as const,
              progress: 100,
              isDeleted: false,
              uniqueId: file.uniqueId || fileId,
              key: file.s3Key || '',
              isNameChanged: Boolean(file.isNameChanged || !!file.newName),
              isRenamed: Boolean(file.isNameChanged || !!file.newName),
              mycaseDocumentId: file.mycaseDocumentId || '',
              contentType: file.contentType || file.type || 'application/pdf',
              uploadDate: file.uploadDate || new Date().toISOString(),
            };
          });

          console.log("📂 Mapped event files:", eventFiles);

          // Replace existing files with event files (don't merge - event files are authoritative)
          onChange(() => {
            console.log("📂 Setting event files count:", eventFiles.length);
            return eventFiles;
          });
        } else {
          // No files for this event - clear the file list
          console.log("📂 No files found for event:", eventId, "- clearing file list");
          onChange(() => []);
        }
      } catch (error: any) {
        console.error('📂 Error fetching event files:', error);

        // Enhanced error logging for debugging
        if (error.response) {
          // The request was made and the server responded with a status code
          console.error('📂 API Error Response:', {
            status: error.response.status,
            statusText: error.response.statusText,
            data: error.response.data,
            headers: error.response.headers
          });
        } else if (error.request) {
          // The request was made but no response was received
          console.error('📂 Network Error - No response received:', error.request);
        } else {
          // Something happened in setting up the request
          console.error('📂 Request Setup Error:', error.message);
        }

        // On error, don't change the current state
      }
    }, [eventId, taskExecutionId, workflowId, router.query.taskId]);

    useEffect(() => {
      const taskId = router.query.taskId;
      const shouldFetchEventFiles = taskId && eventId && !isNewEvent && eventId.trim() !== '';

      if (shouldFetchEventFiles) {
        console.log("📂 Fetching event files for existing event:", { eventId, isViewMode, isTaskReviewed, isNewEvent });
        fetchEventFiles();
      } else {
        console.log("📂 Skipping event files fetch:", {
          taskId: !!taskId,
          eventId,
          isViewMode,
          isTaskReviewed,
          isNewEvent,
          reason: !taskId ? 'no taskId' : !eventId ? 'no eventId' : isNewEvent ? 'new event (add mode)' : 'empty eventId'
        });
      }
    }, [eventId, fetchEventFiles, router.query.taskId, isViewMode, isTaskReviewed, isNewEvent, taskExecutionId, workflowId]);

    // Generate fresh presigned URL for file viewing
    const getPresignedViewUrl = async (s3Key: string) => {
      try {
        const response = await apiClient.get(
          `/workflow/get-presigned-url?key=${encodeURIComponent(s3Key)}&operation=get`
        );

        const data = response.data;
        if (!data.data?.url) {
          throw new Error('Invalid view URL response');
        }
        return data.data.url;
      } catch (error) {
        console.error('❌ Error getting view URL for key:', s3Key, error);
        throw error;
      }
    };

    // Handle file viewing with fresh presigned URL
    const handleFileView = async (file: any) => {
      try {
        console.log('📂 Attempting to view file:', file);
        const s3Key = file.s3Key || file.key;

        if (!s3Key) {
          console.error('❌ No S3 key found for file:', file);
          if (file.url) {
            console.log('🔄 Falling back to stored URL');
            onPdfView?.();
            window.open(file.url, '_blank');
            return;
          }
          throw new Error('No S3 key or URL available for file viewing');
        }

        console.log('🔗 Getting fresh presigned URL for S3 key:', s3Key);
        const freshUrl = await getPresignedViewUrl(s3Key);
        console.log(freshUrl, "freshUrl")
        console.log('✅ Opening file with fresh URL');
        onPdfView?.(); // Notify parent that PDF is being viewed
        window.open(freshUrl, '_blank');

      } catch (error) {
        console.error('❌ Error viewing file:', error);

        // Final fallback to stored URL if available
        if (file.url) {
          console.log('🔄 Using stored URL as final fallback');
          onPdfView?.();
          window.open(file.url, '_blank');
        }
      }
    };

    useEffect(() => {
      if (eventId) {
        console.log("📂 EventId changed to:", eventId, "- preparing to load event-specific files");
      }
    }, [eventId]);

    // const mimeTypeToExtensions: Record<string, string[]> = {
    //   'application/pdf': ['PDF'],
    //   'application/msword': ['DOC'],
    //   'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['DOCX'],
    //   'application/vnd.ms-excel': ['XLS'],
    //   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['XLSX'],
    //   'image/jpeg': ['JPEG'],
    //   'image/jpg': ['JPG'],
    //   'image/png': ['PNG'],
    //   'image/svg+xml': ['SVG'],
    //   'image/tiff': ['TIFF'],
    //   'image/bmp': ['BMP'],
    //   'image/gif': ['GIF'],
    // };

    // const supportedFormats = Array.from(
    //   new Set(allowedFileTypes.flatMap(type => mimeTypeToExtensions[type] || []))
    // ).join(', ');

    // Expose the getFilesForEvent function to parent components
    useImperativeHandle(ref, () => ({
      getFilesForEvent: () => {
        return value
          .filter(file => file.status === 'completed' && !file.isDeleted)
          .map(file => ({
            uniqueId: file.uniqueId,
            name: file.name,
            s3FileName: file.s3FileName,
            url: file.url,
            key: file.s3Key,
            originalName: file.originalName,
            size: file.size,
            type: file.type,
            isNameChanged: file.isRenamed || (file.name !== file.originalName),
          }));
      },
      getRenamedFiles: () => {
        // Get all renamed files from UI state - ensure we always include both original and new names
        const uiRenamedFiles = value
          .filter(f => f.isRenamed && f.pendingNewName)
          .map(f => {
            // Always use the originalName field as the true original name
            // This ensures the original name is never lost or modified
            const trueOriginalName = f.originalName || f.originalDisplayName || f.name || '';
            const newName = f.pendingNewName || f.name || '';

            return {
              fileId: f.id || f.uniqueId || '',
              originalName: trueOriginalName,
              newName: newName
            };
          });

        if (uiRenamedFiles.length > 0) {
          console.log('📤 Payload - Renamed files from UI state:', uiRenamedFiles);
          return uiRenamedFiles;
        }

        // Fallback to localStorage if no UI renames
        if (!taskExecutionId || !eventId) return [];

        const storageKey = `file_renames_${taskExecutionId}_${eventId}`;
        const storedRenames = JSON.parse(localStorage.getItem(storageKey) || '{}');

        const storedRenamedFiles = Object.entries(storedRenames).map(([fileId, rename]: [string, any]) => ({
          fileId,
          originalName: rename.originalName,
          newName: rename.newName
        }));

        console.log('📤 Payload - Renamed files from localStorage:', storedRenamedFiles);
        return storedRenamedFiles;
      },
      clearRenamesFromStorage: () => {
        if (!taskExecutionId || !eventId) return;

        const storageKey = `file_renames_${taskExecutionId}_${eventId}`;
        localStorage.removeItem(storageKey);
        console.log('🗑️ Cleared rename data from localStorage');
      },
      saveRenamestoStorage: () => {
        if (!taskExecutionId || !eventId) return;

        const storageKey = `file_renames_${taskExecutionId}_${eventId}`;
        const existingRenames = JSON.parse(localStorage.getItem(storageKey) || '{}');

        // Get files that have been renamed in UI
        const renamedFiles = value.filter(f => f.isRenamed && f.pendingNewName && f.pendingNewName !== f.originalDisplayName);

        renamedFiles.forEach(file => {
          const fileId = file.id || file.uniqueId;
          if (fileId) {
            existingRenames[fileId] = {
              originalName: file.originalDisplayName || file.originalName || file.name,
              newName: file.pendingNewName,
              timestamp: Date.now()
            };
          }
        });

        localStorage.setItem(storageKey, JSON.stringify(existingRenames));
        console.log('💾 Saved rename data to localStorage on Save Changes');
      },
      removeFileFromStorage: (fileId: string) => {
        if (!taskExecutionId || !eventId) return;

        const storageKey = `file_renames_${taskExecutionId}_${eventId}`;
        const existingRenames = JSON.parse(localStorage.getItem(storageKey) || '{}');

        if (existingRenames[fileId]) {
          delete existingRenames[fileId];
          localStorage.setItem(storageKey, JSON.stringify(existingRenames));
          console.log(`🗑️ Removed file ${fileId} from localStorage`);
        }
      },
      cleanupDeletedFilesFromStorage: (deletedFileIds: string[]) => {
        if (!taskExecutionId || !eventId || deletedFileIds.length === 0) return;

        const storageKey = `file_renames_${taskExecutionId}_${eventId}`;
        const existingRenames = JSON.parse(localStorage.getItem(storageKey) || '{}');

        let hasChanges = false;
        deletedFileIds.forEach(fileId => {
          if (existingRenames[fileId]) {
            delete existingRenames[fileId];
            hasChanges = true;
          }
        });

        if (hasChanges) {
          localStorage.setItem(storageKey, JSON.stringify(existingRenames));
          console.log(`🗑️ Cleaned up ${deletedFileIds.length} deleted files from localStorage after remove API call`);
        }
      },
    }));

    // Function to measure text width for input fields
    // const measureTextWidth = (text: string): number => {
    //   if (!measureRef.current) return 200; // fallback width

    //   measureRef.current.textContent = text;
    //   const width = measureRef.current.offsetWidth;

    //   // Add some padding and ensure minimum width
    //   const minWidth = 150;
    //   const maxWidth = 400; // Maximum width to prevent overflow
    //   const calculatedWidth = Math.max(minWidth, Math.min(maxWidth, width + 20));

    //   return calculatedWidth;
    // };

    // Update input width when file name changes
    // const updateInputWidth = (fileId: string, text: string) => {
    //   const width = measureTextWidth(text);
    //   setInputWidths(prev => ({ ...prev, [fileId]: width }));
    // };

    useEffect(() => {
      // Check if we now have all required fields
      const hasRequiredFields = () => {
        if (!courtNoticeType || courtNoticeType.trim() === '') return false;

        return true;
      };

      if (hasRequiredFields()) {
        // Remove error files that were caused by validation issues
        onChange(prev =>
          prev.filter(
            file => !(file.status === 'error' && file.error === VALIDATIONS.FILE_UPLOAD_VALIDATION)
          )
        );
      }
    }, [courtNoticeType, actionType, startDate, onChange]);

    // Generate UUID for file identification


    // Generate S3 filename with UUID-based naming strategy
    // const generateS3FileName = (
    //   originalName: string,
    //   customName?: string,
    //   existingUniqueId?: string
    // ): { s3FileName: string; displayName: string; uniqueId: string } => {
    //   const extension = originalName.split('.').pop();
    //   const uniqueId = existingUniqueId || generateUniqueId();

    //   let s3FileName: string;
    //   let displayName: string;

    //   // Determine if this is an email body file or regular attachment
    //   const isEmailBodyFile = originalName.toLowerCase().includes('emailbody') ||
    //     (customName && customName.toLowerCase().includes('emailbody'));

    //   if (isEmailBodyFile) {
    //     // Email body files: show as "emailbody.pdf", store as "emailbody_<UUID>.pdf"
    //     s3FileName = `emailbody_${uniqueId}.${extension}`;
    //     displayName = `emailbody.${extension}`;
    //   } else {
    //     // Other attachments: show original name, store with UUID suffix
    //     const baseFileName = customName || originalName.replace(`.${extension}`, '');
    //     s3FileName = `${baseFileName}_${uniqueId}.${extension}`;
    //     displayName = `${baseFileName}.${extension}`;
    //   }

    //   console.log('📁 Generated file names:', {
    //     originalName,
    //     customName,
    //     s3FileName,
    //     displayName,
    //     uniqueId,
    //     isEmailBodyFile
    //   });

    //   return { s3FileName, displayName, uniqueId };
    // };

    const getFileTypeFromMultipleSources = (file: UploadedFile): string | undefined => {
      const getExtension = (filename: string): string | undefined => {
        return filename.split('.').pop()?.toLowerCase();
      };
      const getTypeFromExtension = (extension: string | undefined): string | undefined => {
        if (!extension) return undefined;

        switch (extension) {
          case 'pdf':
            return 'application/pdf';
          case 'jpg':
          case 'jpeg':
            return 'image/jpeg';
          case 'png':
            return 'image/png';
          case 'gif':
            return 'image/gif';
          case 'bmp':
            return 'image/bmp';
          case 'svg':
            return 'image/svg+xml';
          case 'tiff':
          case 'tif':
            return 'image/tiff';
          case 'doc':
          case 'docx':
            return 'application/msword';
          case 'xls':
          case 'xlsx':
            return 'application/vnd.ms-excel';
          default:
            return undefined;
        }
      };
      if (file.s3FileName) {
        const extension = getExtension(file.s3FileName);
        const determinedType = getTypeFromExtension(extension);
        if (determinedType) {
          return determinedType;
        }
      }

      if (file.originalName) {
        const extension = getExtension(file.originalName);
        const determinedType = getTypeFromExtension(extension);
        if (determinedType) {
          return determinedType;
        }
      }

      if (file.name) {
        const extension = getExtension(file.name);
        const determinedType = getTypeFromExtension(extension);
        if (determinedType) {
          return determinedType;
        }
      }

      if (file.type) {
        const hasExtension = file.originalName || file.name || file.s3FileName;

        if (!hasExtension) {
          return file.type;
        } else {
          return undefined;
        }
      }

      return undefined;
    };

    // Validate file before upload
    // const validateFile = (file: File): string | null => {
    //   if (!courtNoticeType || courtNoticeType.trim() === '') {
    //     return VALIDATIONS.FILE_UPLOAD_VALIDATION;
    //   }

    //   if (!allowedFileTypes.includes(file.type)) {
    //     return 'File type not allowed';
    //   }
    //   if (file.size > maxFileSize) {
    //     return 'File size is too large';
    //   }

    //   // Count only non-deleted files for the limit check
    //   const activeFileCount = value.filter(f => !f.isDeleted).length;
    //   if (activeFileCount >= maxFiles) {
    //     return `Maximum ${maxFiles} files allowed`;
    //   }
    //   return null;
    // };

    // Improved validation function that waits for state updates
    // const handleFileValidation = (file: File): Promise<string | null> => {
    //   return new Promise(resolve => {
    //     // First, try immediate validation
    //     const immediateValidation = validateFile(file);

    //     if (immediateValidation === null) {
    //       // If validation passes immediately, resolve
    //       resolve(null);
    //       return;
    //     }

    //     // If validation fails due to missing required fields, wait longer for state updates
    //     if (immediateValidation === VALIDATIONS.FILE_UPLOAD_VALIDATION) {
    //       // Wait longer for React state updates to propagate
    //       setTimeout(() => {
    //         const delayedValidation = validateFile(file);
    //         resolve(delayedValidation);
    //       }, 300); // Increased delay to ensure state propagation
    //     } else {
    //       // For other validation errors (file type, size, etc.), resolve immediately
    //       resolve(immediateValidation);
    //     }
    //   });
    // };

    // Upload file to S3
    // const uploadToS3 = async (
    //   file: File,
    //   presignedUrl: string,
    //   onProgress: (progress: number) => void
    // ): Promise<string> => {
    //   return new Promise((resolve, reject) => {
    //     const xhr = new XMLHttpRequest();
    //     xhr.open('PUT', presignedUrl, true);
    //     xhr.setRequestHeader('Content-Type', file.type);

    //     xhr.upload.onprogress = event => {
    //       if (event.lengthComputable) {
    //         const percent = Math.round((event.loaded / event.total) * 100);
    //         onProgress(percent);
    //       }
    //     };

    //     xhr.onload = () => {
    //       if (xhr.status >= 200 && xhr.status < 300) {
    //         resolve(presignedUrl.split('?')[0]);
    //       } else {
    //         reject(new Error(`Upload failed with status: ${xhr.status}`));
    //       }
    //     };

    //     xhr.onerror = () => {
    //       reject(new Error('Failed to upload file to S3'));
    //     };

    //     xhr.send(file);
    //   });
    // };

    // Get presigned URL for viewing files
    // const getPresignedViewUrl = async (s3Key: string) => {
    //   try {
    //     console.log('🔗 Requesting presigned URL for:', s3Key);
    //     const response = await fetch(
    //       `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/get-presigned-url?key=${encodeURIComponent(s3Key)}&operation=get`
    //     );

    //     if (!response.ok) {
    //       console.error('❌ Presigned URL request failed:', response.status, response.statusText);
    //       throw new Error(`Failed to get view URL: ${response.status}`);
    //     }

    //     const data = await response.json();
    //     console.log('✅ Presigned URL response:', data);

    //     if (!data.data?.url) {
    //       console.error('❌ Invalid presigned URL response structure:', data);
    //       throw new Error('Invalid view URL response');
    //     }

    //     console.log('🎯 Generated presigned URL successfully');
    //     return data.data.url;
    //   } catch (error) {
    //     console.error('❌ Error getting view URL for key:', s3Key, error);
    //     throw error;
    //   }
    // };

    // Handle file upload
    // const handleFileUpload = async (file: File) => {
    //   const validationError = await handleFileValidation(file);
    //   if (validationError) {
    //     onChange(prev => [
    //       ...prev,
    //       {
    //         id: `error-${Date.now()}`,
    //         name: file.name,
    //         type: file.type,
    //         size: file.size,
    //         status: 'error' as const,
    //         progress: 0,
    //         error: validationError,
    //         isDeleted: false,
    //       },
    //     ]);
    //     return;
    //   }

    //   const fileId = `${file.name}-${Date.now()}`;
    //   const { s3FileName, displayName, uniqueId } = generateS3FileName(file.name);
    //   const s3Key = path + s3FileName;

    //   // Add file to state with uploading status
    //   onChange(prev => [
    //     ...prev,
    //     {
    //       id: fileId,
    //       name: displayName,
    //       s3Key: s3Key,
    //       originalS3Key: s3Key,
    //       s3FileName: s3FileName,
    //       displayName: displayName,
    //       originalName: file.name,
    //       size: file.size,
    //       type: file.type,
    //       url: '',
    //       progress: 0,
    //       status: 'uploading',
    //       path: path,
    //       uniqueId,
    //       key: s3Key, // For backward compatibility
    //     },
    //   ]);

    //   try {
    //     // Get presigned URL for upload
    //     const uploadResponse = await fetch(
    //       `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/get-presigned-url?key=${encodeURIComponent(s3Key)}&operation=put`
    //     );

    //     if (!uploadResponse.ok) {
    //       throw new Error('Failed to get presigned URL');
    //     }

    //     const uploadData = await uploadResponse.json();
    //     if (!uploadData.data?.url) {
    //       throw new Error('Invalid presigned URL response');
    //     }

    //     // Upload the file
    //     await uploadToS3(file, uploadData.data.url, progress => {
    //       onChange(prev => prev.map(f => (f.id === fileId ? { ...f, progress } : f)));
    //     });

    //     // Get view URL
    //     const viewUrl = await getPresignedViewUrl(s3Key);

    //     // Update file with completed status and view URL
    //     onChange(prev =>
    //       prev.map(f =>
    //         f.id === fileId
    //           ? {
    //             ...f,
    //             url: viewUrl,
    //             progress: 100,
    //             status: 'completed' as const,
    //           }
    //           : f
    //       )
    //     );
    //   } catch (error) {
    //     console.error('Error uploading file:', error);
    //     onChange(prev =>
    //       prev.map(f =>
    //         f.id === fileId
    //           ? {
    //             ...f,
    //             progress: 100,
    //             status: 'error' as const,
    //             error: error instanceof Error ? error.message : 'Upload failed',
    //           }
    //           : f
    //       )
    //     );
    //   }
    // };

    // Handle file name update during editing
    // const handleFileNameUpdate = (fileId: string, newName: string) => {
    //   updateInputWidth(fileId, newName);

    //   onChange(prev =>
    //     prev.map(file => {
    //       if (file.id === fileId) {
    //         return {
    //           ...file,
    //           editingName: newName,
    //         };
    //       }
    //       return file;
    //     })
    //   );
    // };

    // Get filename without extension for editing
    // const getFileNameForEditing = (file: UploadedFile): string => {
    //   if (file.editingName !== undefined) {
    //     return file.editingName;
    //   }

    //   // Check localStorage for current renamed value first
    //   const storageKey = `file_renames_${taskExecutionId}_${eventId}`;
    //   const storedRenames = JSON.parse(localStorage.getItem(storageKey) || '{}');
    //   const rename = storedRenames[file.id || file.uniqueId || ''];
    //   const nameToUse = rename?.newName || file.name;

    //   if (nameToUse && nameToUse.includes('.')) {
    //     return nameToUse.substring(0, nameToUse.lastIndexOf('.'));
    //   }

    //   return nameToUse || '';
    // };

    // Toggle edit mode for a file
    // const toggleEditMode = async (fileId: string) => {
    //   const file = value.find(f => f.id === fileId);
    //   if (!file) return;

    //   if (file.isEditing) {
    //     // Save the edited name
    //     const editingName = file.editingName || getFileNameForEditing(file);

    //     if (!editingName.trim()) {
    //       onChange(prev =>
    //         prev.map(f => {
    //           if (f.id === fileId) {
    //             return {
    //               ...f,
    //               error: 'File name cannot be empty',
    //               status: 'error' as const,
    //             };
    //           }
    //           return f;
    //         })
    //       );
    //       return;
    //     }

    //     const originalExtension = file.originalName
    //       ? file.originalName.split('.').pop()
    //       : file.name?.split('.').pop();

    //     const fullDisplayName = originalExtension
    //       ? `${editingName.trim()}.${originalExtension}`
    //       : editingName.trim();

    //     // Check if name actually changed
    //     const hasNameChanged = file.name !== fullDisplayName;

    //     if (hasNameChanged) {
    //       // Simple rename: just update the name, keep everything else the same
    //       onChange(prev =>
    //         prev.map(f => {
    //           if (f.id === fileId) {
    //             return {
    //               ...f,
    //               name: fullDisplayName,
    //               displayName: fullDisplayName,
    //               originalName: f.originalName || f.name, // Ensure originalName is preserved
    //               editingName: undefined,
    //               isEditing: false,
    //               error: undefined,
    //             };
    //           }
    //           return f;
    //         })
    //       );
    //     } else {
    //       // No name change, just exit edit mode
    //       onChange(prev =>
    //         prev.map(f => {
    //           if (f.id === fileId) {
    //             return {
    //               ...f,
    //               editingName: undefined,
    //               isEditing: false,
    //               error: undefined,
    //               status: 'completed' as const,
    //             };
    //           }
    //           return f;
    //         })
    //       );
    //     }
    //   } else {
    //     // Enter edit mode
    //     onChange(prev =>
    //       prev.map(f => {
    //         if (f.id === fileId) {
    //           const nameForEditing = getFileNameForEditing(f);
    //           setTimeout(() => updateInputWidth(fileId, nameForEditing), 0);

    //           return {
    //             ...f,
    //             isEditing: true,
    //             editingName: nameForEditing,
    //             error: undefined,
    //             status: 'completed' as const,
    //           };
    //         }
    //         return f;
    //       })
    //     );
    //   }
    // };

    // Handle file click to open/view
    // const handleFileClick = async (e: React.MouseEvent<HTMLAnchorElement>, file: UploadedFile) => {
    //   e.preventDefault();
    //   if (!file.s3Key) return;

    //   // If we have a valid URL, use it
    //   if (file.url) {
    //     window.open(file.url, '_blank');
    //     return;
    //   }

    //   // Generate new presigned URL
    //   try {
    //     setIsGeneratingUrl(prev => ({ ...prev, [file.id]: true }));
    //     const viewUrl = await getPresignedViewUrl(file.s3Key);

    //     // Update file with new URL
    //     onChange(prev => prev.map(f => (f.id === file.id ? { ...f, url: viewUrl } : f)));

    //     window.open(viewUrl, '_blank');
    //   } catch (error) {
    //     console.error('Error opening file:', error);
    //   } finally {
    //     setIsGeneratingUrl(prev => ({ ...prev, [file.id]: false }));
    //   }
    // };

    // Handle file deletion
    const handleDelete = (fileId: string) => {
      setDeleteConfirmId(fileId);
    };

    const confirmDelete = () => {
      if (deleteConfirmId) {
        // Instead of removing the file completely, mark it as deleted
        // This ensures it appears in the deleteFiles array when form is submitted
        onChange(prev => prev.map(f => (f.id === deleteConfirmId ? { ...f, isDeleted: true } : f)));
        setDeleteConfirmId(null);
      }
    };

    const cancelDelete = () => {
      setDeleteConfirmId(null);
    };

    const handleDirectDelete = (fileId: string) => {
      // console.log('handleDirectDelete called for fileId:', fileId);
      // console.log('Current files before delete:', value);

      onChange(prev => {
        const updatedFiles = prev.map(f => (f.id === fileId ? { ...f, isDeleted: true } : f));
        // console.log('Updated files after delete:', updatedFiles);
        return updatedFiles;
      });
    };

    // Handle selecting files from available files dropdown
    // const handleSelectAvailableFiles = () => {
    //   const selectedFileIds = Array.from(selectedForBulkAdd);
    //   if (selectedFileIds.length === 0) return;

    //   // Count existing non-deleted files
    //   const existingFileCount = value.filter(f => !f.isDeleted).length;

    //   // Check if total files would exceed the limit
    //   if (existingFileCount + selectedFileIds.length > maxFiles) {
    //     setShowFileLimitAlert(true);
    //     return;
    //   }

    //   // Convert selected available files to UploadedFile format
    //   const newFiles: UploadedFile[] = selectedFileIds.map(fileId => {
    //     const availableFile = availableFiles.find(f => (f.id || f.uniqueId) === fileId);
    //     if (!availableFile) return null;

    //     // Check if file is already in the list to prevent duplicates
    //     const existingFile = value.find(f =>
    //       (f.id || f.uniqueId) === (availableFile.id || availableFile.uniqueId) ||
    //       f.name === availableFile.name ||
    //       f.s3Key === availableFile.s3Key
    //     );

    //     if (existingFile) {
    //       console.log('⚠️ File already exists in bulk add, skipping:', availableFile.name);
    //       return null;
    //     }

    //     return {
    //       id: `selected_${fileId}_${Date.now()}`,
    //       name: availableFile.name,
    //       s3Key: availableFile.s3Key,
    //       originalS3Key: availableFile.s3Key,
    //       s3FileName: availableFile.name,
    //       displayName: availableFile.name,
    //       size: availableFile.size,
    //       type: availableFile.type,
    //       url: availableFile.url,
    //       status: 'completed' as const,
    //       originalName: availableFile.originalName,
    //       isDeleted: false,
    //       isEditing: false,
    //       uniqueId: availableFile.id,
    //     };
    //   }).filter(Boolean) as UploadedFile[];

    //   // Add new files to existing files (only if we have new files to add)
    //   if (newFiles.length > 0) {
    //     onChange(prev => [...prev, ...newFiles]);
    //   }

    //   // Clear selection and close dropdown
    //   setSelectedForBulkAdd(new Set());
    //   setIsDropdownOpen(false);
    // };

    // Drag and drop handlers
    // const handleDrop = useCallback(
    //   async (e: React.DragEvent<HTMLDivElement>) => {
    //     e.preventDefault();
    //     setIsDragging(false);

    //     const files = Array.from(e.dataTransfer.files);

    //     // Count existing non-deleted files
    //     const existingFileCount = value.filter(f => !f.isDeleted).length;

    //     // Check if total files (existing + new) would exceed the limit
    //     if (existingFileCount + files.length > maxFiles) {
    //       setShowFileLimitAlert(true);
    //       return;
    //     }

    //     await Promise.all(files.map(file => handleFileUpload(file)));
    //   },
    //   [value, maxFiles, handleFileUpload]
    // );

    // const handleFileInput = useCallback(
    //   async (e: React.ChangeEvent<HTMLInputElement>) => {
    //     const files = Array.from(e.target.files || []);

    //     // Count existing non-deleted files
    //     const existingFileCount = value.filter(f => !f.isDeleted).length;

    //     // Check if total files (existing + new) would exceed the limit
    //     if (existingFileCount + files.length > maxFiles) {
    //       setShowFileLimitAlert(true);
    //       e.target.value = '';
    //       return;
    //     }

    //     await Promise.all(files.map(file => handleFileUpload(file)));
    //     e.target.value = '';
    //   },
    //   [value, maxFiles, handleFileUpload]
    // );

    // Use available files from props (includes files from task execution)


    // SIMPLIFIED: Count unique files that are not deleted
    const actualSelectedFiles = value.filter(f => !f.isDeleted);

    console.log('🔍 Files after removing deleted:', actualSelectedFiles.length);

    // Enhanced deduplication - handle both ID and name-based duplicates
    const seenIds = new Set();
    const seenNames = new Set();
    const seenS3Keys = new Set();

    const deduplicatedFiles = actualSelectedFiles.filter(f => {
      const fileId = f.id || f.uniqueId;
      const fileName = f.name;
      const s3Key = f.s3Key;

      // Create a unique identifier for this file
      const identifiers = [fileId, fileName, s3Key].filter(Boolean);

      // Check if we've seen any of these identifiers before
      const isDuplicate = identifiers.some(id =>
        seenIds.has(id) || seenNames.has(id) || seenS3Keys.has(id)
      );

      if (isDuplicate) {
        console.log(`🔍 Skipping duplicate file: id=${fileId}, name=${fileName}, s3Key=${s3Key}`);
        return false;
      }

      // Add all identifiers to our sets
      if (fileId) seenIds.add(fileId);
      if (fileName) seenNames.add(fileName);
      if (s3Key) seenS3Keys.add(s3Key);

      return true;
    });

    console.log('🔍 Files after deduplication:', deduplicatedFiles.length);

    const selectedFileIds = deduplicatedFiles.map(f => f.id || f.uniqueId || f.name);

    // For backwards compatibility, also calculate available files intersection
    // const availableFileIds = new Set(availableFiles?.map(f => f.id || f.uniqueId) || []);
    // const selectedFromAvailable = value.filter(f => !f.isDeleted && availableFileIds.has(f.id || f.uniqueId));

    // Filter available files based on search term - include both current name and original name in search
    const filteredAvailableFiles = availableFiles?.filter(file => {
      const searchLower = searchTerm.toLowerCase();

      // Search in current file name
      const matchesCurrentName = file.name.toLowerCase().includes(searchLower);

      // Search in original name if it exists
      const matchesOriginalName = file.originalName && file.originalName.toLowerCase().includes(searchLower);

      // Also check if this file has been renamed in the current session
      const renamedFile = value.find(f => (f.id || f.uniqueId) === (file.id || file.uniqueId));
      const matchesRenamedName = renamedFile?.name && renamedFile.name.toLowerCase().includes(searchLower);
      const matchesRenamedOriginal = renamedFile?.originalName && renamedFile.originalName.toLowerCase().includes(searchLower);

      return matchesCurrentName || matchesOriginalName || matchesRenamedName || matchesRenamedOriginal;
    });

    // For counting: only non-renamed files (handled above in deduplicatedFiles)
    // Include files from both availableFiles and files fetched from database (in value array)
    const selectedFilesFromAvailable = availableFiles?.filter(file => selectedFileIds.includes(file.id || file.uniqueId)) || [];
    const selectedFilesFromValue = deduplicatedFiles.filter(file =>
      !availableFiles?.find(af => (af.id || af.uniqueId) === (file.id || file.uniqueId))
    ).map(file => ({
      id: file.id || file.uniqueId,
      uniqueId: file.uniqueId || file.id,
      name: file.name || '',
      originalName: file.originalName || file.name || '',
      size: file.size || 0,
      type: file.type || '',
      url: file.url || '',
      s3Key: file.s3Key || '',
      uploadDate: '',
      isRenamedVersion: file.isRenamedVersion || false,
      originalFileId: file.originalFileId,
      hasRenamedVersion: file.hasRenamedVersion || false,
      renamedVersionId: file.renamedVersionId,
      renamedAt: file.renamedAt,
      isNameChanged: file.isNameChanged || false,
      mycaseDocumentId: file.mycaseDocumentId || '',
    }));
    const selectedFiles = [...selectedFilesFromAvailable, ...selectedFilesFromValue];
    console.log("📂 Selected files computation:", {
      fromAvailable: selectedFilesFromAvailable.length,
      fromValue: selectedFilesFromValue.length,
      total: selectedFiles.length,
      selectedFileIds: selectedFileIds.length
    });

    // FIXED: For display, use the actual selected files from value prop (includes API files)
    const allSelectedFiles = actualSelectedFiles;

    // Create comprehensive list of selected file identifiers
    const selectedIdentifiers = new Set();
    actualSelectedFiles.forEach(file => {
      // Add all possible identifiers for this file
      if (file.id) selectedIdentifiers.add(file.id);
      if (file.uniqueId) selectedIdentifiers.add(file.uniqueId);
      if (file.s3Key) selectedIdentifiers.add(file.s3Key);
      if (file.name) selectedIdentifiers.add(file.name);
    });

    // FIXED: Enhanced filtering to properly exclude ALL selected files from suggestions
    const suggestedFiles = filteredAvailableFiles?.filter(file => {
      // Check if this available file matches any selected file by any identifier
      const fileIdentifiers = [
        file.id,
        file.uniqueId,
        file.s3Key,
        file.name
      ].filter(Boolean);

      // If any identifier matches a selected file, exclude from suggestions
      const isSelected = fileIdentifiers.some(identifier => selectedIdentifiers.has(identifier));

      if (isSelected) {
        console.log(`🚫 Excluding file from suggestions: ${file.name} (already selected)`);
      }

      return !isSelected;
    }) || [];

    const handleFileSelect = async (file: AvailableFile) => {
      console.log(file, "SELECTED_FILE");
      const fileIdentifier = file.id || file.uniqueId;
      if (!fileIdentifier) {
        console.error('File has no valid identifier (id or uniqueId):', file);
        return;
      }
      const isSelected = selectedFileIds.includes(fileIdentifier);
      console.log(isSelected, "isSelected");

      if (isSelected) {
        // Remove from selected files - only remove from UI state immediately
        console.log('🗑️ Removing file from UI state (will be removed from DB on save):', fileIdentifier);

        // Immediately remove from component state
        onChange(prev => prev.filter(f => (f.id || f.uniqueId) !== fileIdentifier));
        console.log('✅ File removed from UI state');
      } else {
        // Add to selected files (only to component state for now)
        console.log('➕ Adding file to selected state:', fileIdentifier);

        // Check if file is already in the list to prevent duplicates
        const existingFile = value.find(f =>
          (f.id || f.uniqueId) === fileIdentifier ||
          f.name === file.name ||
          f.s3Key === file.s3Key
        );

        if (existingFile) {
          console.log('⚠️ File already exists in list, skipping:', fileIdentifier);
          return;
        }

        const newUploadedFile: UploadedFile = {
          id: file.id || fileIdentifier,
          name: file.name,
          originalName: file.originalName || file.name,
          s3Key: file.s3Key,
          size: file.size || 0,
          type: file.type || 'application/pdf',
          url: file.url,
          status: 'completed',
          isDeleted: false,
          uniqueId: file.uniqueId || file.id || fileIdentifier,
          key: file.s3Key,
        };
        onChange(prev => [...prev, newUploadedFile]);
      }
    };


    const handleFileRename = (fileId: string, newName: string) => {
      setEditingFileId(null);
      setEditingFileName('');

      // Find the file to get its extension
      const file = value.find(f => f.id === fileId) || availableFiles.find(f => f.id === fileId);
      if (!file) return;

      // Get the original extension from the original file name
      const originalExtension = file.originalName
        ? file.originalName.split('.').pop()
        : file.name?.split('.').pop();

      // Create the new filename with extension
      let newFileName = originalExtension ? `${newName}.${originalExtension}` : newName;

      // Check for duplicate names and add count if needed
      const existingNames = value
        .filter(f => f.id !== fileId && !f.isDeleted) // Exclude current file and deleted files
        .map(f => f.name || '')
        .filter(name => name.toLowerCase().startsWith(newName.toLowerCase()));

      if (existingNames.length > 0) {
        // Find the highest count for this base name
        let maxCount = 0;
        existingNames.forEach(existingName => {
          const match = existingName.match(new RegExp(`^${newName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(?:\\s*\\((\\d+)\\))?\\.${originalExtension}$`, 'i'));
          if (match) {
            const count = match[1] ? parseInt(match[1]) : 0;
            maxCount = Math.max(maxCount, count);
          }
        });

        // Add count to new name
        const countSuffix = maxCount === 0 ? ' (1)' : ` (${maxCount + 1})`;
        newFileName = originalExtension ? `${newName}${countSuffix}.${originalExtension}` : `${newName}${countSuffix}`;
      }

      // Simple update - just update the file name and mark as renamed
      onChange(prev => prev.map(f =>
        f.id === fileId
          ? {
            ...f,
            name: newFileName, // Update to new name
            // Preserve originalName - set it only if not already set
            originalName: f.originalName || f.name || file.originalName || file.name,
            isRenamed: true, // Flag to indicate this file has been renamed
            isNameChanged: true // Additional flag for name change tracking
          }
          : f
      ));

      console.log(`📝 File ${fileId} renamed in UI: "${file.name}" → "${newFileName}"`);
      console.log(`📝 Original name preserved: "${file.originalName || file.name}"`);
      console.log(`📤 Payload will include: originalName="${file.originalName || file.name}", newName="${newFileName}"`);
    };


    // Handle dropdown click outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          triggerRef.current && !triggerRef.current.contains(event.target as Node)) {
          setIsDropdownOpen(false);
          setSearchTerm('');
          setDropdownPosition(null);
        }
      };

      if (isDropdownOpen) {
        document.addEventListener('mousedown', handleClickOutside);
      }

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [isDropdownOpen]);

    // Handle window resize and scroll to update dropdown position
    useEffect(() => {
      if (!isDropdownOpen || !dropdownPosition) return;

      const updatePosition = () => {
        if (!triggerRef.current || !dropdownRef.current) return;

        const triggerRect = triggerRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - triggerRect.bottom;
        const spaceAbove = triggerRect.top;

        // Get actual dropdown height if available, otherwise use conservative estimate
        const dropdownHeight = dropdownRef.current?.offsetHeight || Math.min(150, spaceAbove - 8);

        // Default to opening below
        let isAbove = false;
        let top = triggerRect.bottom + 4;

        // Only position above if there's not enough space below AND there's more space above
        if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
          isAbove = true;
          // Use actual dropdown height for precise positioning
          top = Math.max(4, triggerRect.top - dropdownHeight - 4);
        }

        setDropdownPosition({
          top,
          left: triggerRect.left,
          width: triggerRect.width,
          isAbove
        });
      };

      window.addEventListener('resize', updatePosition);
      window.addEventListener('scroll', updatePosition, true);

      return () => {
        window.removeEventListener('resize', updatePosition);
        window.removeEventListener('scroll', updatePosition, true);
      };
    }, [isDropdownOpen, dropdownPosition]);

    // Recalculate position after dropdown is rendered to get actual height
    useEffect(() => {
      if (!isDropdownOpen || !dropdownPosition || !dropdownRef.current || !triggerRef.current) return;

      const dropdownElement = dropdownRef.current;
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;

      // Get actual dropdown height
      const dropdownHeight = dropdownElement.offsetHeight;
      const spaceBelow = viewportHeight - triggerRect.bottom;
      const spaceAbove = triggerRect.top;

      // Check if we need to reposition
      let shouldBeAbove = spaceBelow < dropdownHeight && spaceAbove > spaceBelow;
      const currentIsAbove = dropdownPosition.isAbove;

      if (shouldBeAbove !== currentIsAbove) {
        // Need to reposition
        let newTop;
        if (shouldBeAbove) {
          // Position above: trigger top - dropdown height - gap
          newTop = triggerRect.top - dropdownHeight - 4;
          // Ensure it doesn't go above viewport
          newTop = Math.max(4, newTop);

          // If there's still not enough space above, force it below
          if (newTop < 4) {
            shouldBeAbove = false;
            newTop = triggerRect.bottom + 4;
          }
        } else {
          // Position below: trigger bottom + gap
          newTop = triggerRect.bottom + 4;
        }

        setDropdownPosition(prev => prev ? {
          ...prev,
          top: newTop,
          isAbove: shouldBeAbove
        } : null);
      }
    }, [isDropdownOpen, dropdownPosition, loadingFiles, selectedFiles, suggestedFiles, searchTerm]);

    // Helper functions
    const getFileIcon = (fileType: string | undefined, fileName?: string) => {
      if (fileType === 'application/pdf') {
        return (
          <img
            alt="pdf"
            loading="lazy"
            width="22"
            height="22"
            decoding="async"
            data-nimg="1"
            src="/assets/pdf-filled.svg"
          />
        );
      }
      if (fileType?.startsWith('image/')) {
        return <ImageIcon className="w-5 h-5 text-[#3F73F6]" />;
      }
      if (
        fileType === 'application/msword' ||
        fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ) {
        return (
          <img
            alt="word"
            loading="lazy"
            width="22"
            height="22"
            decoding="async"
            data-nimg="1"
            src="/assets/word.svg"
          />
        );
      }
      if (
        fileType === 'application/vnd.ms-excel' ||
        fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        return (
          <img
            alt="excel"
            loading="lazy"
            width="22"
            height="22"
            decoding="async"
            data-nimg="1"
            src="/assets/excel.svg"
          />
        );
      }

      if (fileName) {
        const extension = fileName.split('.').pop()?.toLowerCase();

        if (extension === 'pdf') {
          return (
            <img
              alt="pdf"
              loading="lazy"
              width="22"
              height="22"
              decoding="async"
              data-nimg="1"
              src="/assets/pdf-filled.svg"
            />
          );
        }
        if (
          ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'tiff', 'tif', 'webp'].includes(
            extension || ''
          )
        ) {
          return <ImageIcon className="w-5 h-5 text-[#3F73F6]" />;
        }
        if (['doc', 'docx'].includes(extension || '')) {
          return <FileText className="w-5 h-5 text-[#2563EB]" />;
        }
        if (['xls', 'xlsx'].includes(extension || '')) {
          return <FileText className="w-5 h-5 text-[#16A34A]" />;
        }
      }

      return <FileText className="w-5 h-5 text-[#5F6F84]" />;
    };

    // const formatFileSize = (bytes: number) => {
    //   if (bytes === 0) return '0 Bytes';
    //   const k = 1024;
    //   const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    //   const i = Math.floor(Math.log(bytes) / Math.log(k));
    //   return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    // };

    const getProgressBarColor = (status: string) => {
      switch (status) {
        case 'uploading':
          return 'bg-blue-500';
        case 'completed':
          return 'bg-green-500';
        case 'error':
          return 'bg-red-500';
        default:
          return 'bg-gray-300';
      }
    };

    const getTextColor = (status: string) => {
      switch (status) {
        case 'uploading':
          return 'text-blue-500';
        case 'completed':
          return 'text-green-500';
        case 'error':
          return 'text-red-500';
        default:
          return 'text-gray-500';
      }
    };

    const getStatusText = (file: UploadedFile) => {
      switch (file.status) {
        case 'uploading':
          return `${file.progress}%`;
        case 'completed':
          return '100%';
        case 'error':
          return file.error || '';
        default:
          return '';
      }
    };

    const truncateFileName = (fileName: string, maxLength: number = 38) => {
      if (!fileName) return '';
      if (fileName.length <= maxLength) return fileName;

      const extension = fileName.split('.').pop() || '';
      const nameWithoutExt = fileName.slice(0, fileName.lastIndexOf('.'));

      // Calculate how much of the name we can show
      const truncatedLength = maxLength - extension.length - 4; // 4 accounts for "..." and "."
      const truncatedName = nameWithoutExt.slice(0, truncatedLength);

      return `${truncatedName}...${extension ? `.${extension}` : ''}`;
    };

    // const isUploadDisabled = () => {
    //   // Check if component is explicitly disabled
    //   if (disabled) {
    //     return true;
    //   }

    //   // Check if court notice type is missing
    //   if (!courtNoticeType || courtNoticeType.trim() === '') {
    //     return true;
    //   }

    //   // Count only non-deleted files for the limit check
    //   const activeFileCount = value.filter(f => !f.isDeleted).length;
    //   return activeFileCount >= maxFiles;
    // };

    // const getUploadMessage = () => {
    //   if (disabled) {
    //     return 'Upload is disabled';
    //   }

    //   if (!courtNoticeType || courtNoticeType.trim() === '') {
    //     return 'Please add the Court Notice Type and Court notice action/Start date before uploading a file';
    //   }

    //   // Count only non-deleted files for the limit check
    //   const activeFileCount = value.filter(f => !f.isDeleted).length;
    //   if (activeFileCount >= maxFiles) {
    //     return `Maximum ${maxFiles} files reached`;
    //   }

    //   return 'Upload file';
    // };

    // const canUploadFile = () => {
    //   // Check if component is explicitly disabled
    //   if (disabled) {
    //     return false;
    //   }

    //   // Check if court notice type is provided
    //   if (!courtNoticeType || courtNoticeType.trim() === '') {
    //     return false;
    //   }

    //   // For all action types, allow initial file uploads if basic conditions are met
    //   return !!courtNoticeType;
    // };

    // const canEditDeleteFiles = (fileId?: string) => {
    //   // Check if component is explicitly disabled
    //   if (disabled) {
    //     return false;
    //   }

    //   // Check if court notice type is provided
    //   if (!courtNoticeType || courtNoticeType.trim() === '') {
    //     return false;
    //   }

    //   // Check if "update my case" has been completed (stored in localStorage)
    //   const isCaseUpdated = localStorage.getItem('caseUpdateCompleted') === 'true';

    //   // If case is updated and we have a fileId, check if this is an existing file
    //   if (isCaseUpdated && fileId) {
    //     const file = value.find(f => f.id === fileId);
    //     // Disable edit/delete only for pre-existing files (files that existed before case update)
    //     // Pre-existing files are identified by ID starting with "existing-"
    //     // Newly uploaded files have timestamp-based IDs and should remain editable
    //     if (file && file.id.startsWith('existing-')) {
    //       return false;
    //     }
    //   }

    //   // For all other cases, just check court notice type
    //   return !!courtNoticeType;
    // };

    return (
      <div className="space-y-4">
        {/* Hidden span for measuring text width */}
        <span
          ref={measureRef}
          className="absolute invisible text-sm font-medium"
          style={{ whiteSpace: 'nowrap', fontSize: '14px', fontWeight: '500' }}
          aria-hidden="true"
        />

        {/* Delete Confirmation Modal */}
        {deleteConfirmId &&
          ReactDOM.createPortal(
            <div
              className="fixed inset-0 z-[9999] flex items-center justify-center"
              style={{ backgroundColor: 'rgba(128, 128, 128, 0.3)' }}
            >
              <div className="bg-white rounded-xl shadow-xl p-8 w-full max-w-md flex flex-col items-center relative animate-fade-in">
                <div className="flex flex-col items-center">
                  <div className="rounded-full p-3 mb-3">
                    <img
                      alt="Delete"
                      loading="lazy"
                      width="50"
                      height="50"
                      decoding="async"
                      src="assets/info-circle.svg"
                    />
                  </div>
                  <span className="text-base font-semibold text-gray-900 mb-2">
                    Delete this file?
                  </span>
                  <p className="text-gray-600 text-sm text-center mb-6">
                    Are you sure you want to delete this file? <br />
                    This process can&apos;t be undone.
                  </p>
                </div>
                <div className="flex gap-3 w-full justify-center">
                  <button
                    className="w-[162px] h-[40px] rounded-lg bg-red-200 text-white hover:bg-red-400 transition-colors"
                    onClick={cancelDelete}
                  >
                    No
                  </button>
                  <button
                    type="button"
                    onClick={confirmDelete}
                    className="w-[162px] h-[40px] border border-[#3F73F6] cursor-pointer text-[#3F73F6] rounded-[12px] bg-white hover:bg-gray-50"
                  >
                    Yes, delete
                  </button>
                </div>
              </div>
            </div>,
            document.body
          )}
        {showFileLimitAlert &&
          ReactDOM.createPortal(
            <div
              className="fixed inset-0 z-[9999] flex items-center justify-center"
              style={{ backgroundColor: 'rgba(128, 128, 128, 0.3)' }}
            >
              <div className="bg-white rounded-xl shadow-xl p-8 w-full max-w-md flex flex-col items-center relative animate-fade-in">
                <div className="flex flex-col items-center">
                  <div className="rounded-full p-3 mb-3">
                    <img
                      alt="Warning"
                      loading="lazy"
                      width="50"
                      height="50"
                      decoding="async"
                      src="assets/info-circle.svg"
                    />
                  </div>
                  <span className="text-lg font-semibold text-gray-900 mb-2 font-[Poppins]">
                    File Limit Exceeded!
                  </span>
                  <p className="text-[#5F6F84] text-center mb-6 font-[Poppins]">
                    The maximum allowed is {maxFiles} files. <br />
                    Please remove some files or select fewer files to upload.
                  </p>
                </div>
                <div className="flex gap-3 w-full justify-center">
                  <button
                    type="button"
                    onClick={() => setShowFileLimitAlert(false)}
                    className="w-full h-[40px] border border-[#3F73F6] cursor-pointer text-[#3F73F6] rounded-[12px] bg-white hover:bg-gray-50"
                  >
                    Okay
                  </button>
                </div>
              </div>
            </div>,
            document.body
          )}
        {value.length > 0 && (
          <div className="space-y-2">
            {value
              .filter(file => !file.isDeleted)
              .map(file => (
                <div key={file.id}>
                  {file.status !== 'completed' && !file.isDeleted && (
                    <div className="flex flex-col p-4 bg-white border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getFileIcon(
                            getFileTypeFromMultipleSources(file),
                            file.name || file.originalName
                          )}
                          <p className="text-gray-900 font-medium" title={file.name}>
                            {truncateFileName(file.name || '')}
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={() =>
                            file.status === 'error'
                              ? handleDirectDelete(file.id)
                              : handleDelete(file.id)
                          }
                          className="text-gray-500 cursor-pointer"
                        >
                          <X className="w-5 h-5" />
                        </button>
                      </div>
                      <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                        <div
                          className={`h-full ${getProgressBarColor(file.status)} transition-all duration-300`}
                          style={{ width: `${file.progress}%` }}
                        />
                      </div>
                      <div
                        className={`flex ${file.status === 'error' ? 'justify-start' : 'justify-end'} mt-1`}
                      >
                        <span
                          className={`text-sm ${getTextColor(file.status)} ${file.status === 'error' ? 'font-medium' : ''}`}
                        >
                          {file.status === 'error' ? file.error : getStatusText(file)}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Completed File */}
                  {/* {(file.status === 'completed' || (file.status === 'error' && file.isEditing)) &&
                    !file.isDeleted && (
                      <div>
                        <div
                          className={`flex items-center justify-between py-[8px] px-[12px] border rounded-[12px] ${file.status === 'error' && file.isEditing
                            ? 'border-red-500'
                            : file.isEditing
                              ? 'border-[#3F73F6]'
                              : 'border-[#DCE2EB]'
                            }`}
                          style={
                            file.status === 'error' && file.isEditing
                              ? { boxShadow: '0px 0px 0px 2px rgba(239, 68, 68, 0.20)' }
                              : file.isEditing
                                ? { boxShadow: '0px 0px 0px 2px rgba(63, 115, 246, 0.20)' }
                                : {}
                          }
                        >
                          <div className="flex items-center gap-1 flex-1 min-w-0">
                            <div className="w-8 h-8 flex items-center justify-center flex-shrink-0">
                              {getFileIcon(
                                getFileTypeFromMultipleSources(file),
                                file.name || file.originalName
                              )}
                            </div>
                            <div className="flex flex-col flex-1 min-w-0">
                              {file.isEditing ? (
                                <div className="flex items-center gap-2">
                                  <input
                                    type="text"
                                    value={getFileNameForEditing(file)}
                                    onChange={e => handleFileNameUpdate(file.id, e.target.value)}
                                    onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                                      if (e.key === 'Enter') {
                                        e.preventDefault();
                                        if ((e.target as HTMLInputElement).value.trim()) {
                                          toggleEditMode(file.id);
                                        }
                                      } else if (e.key === 'Escape') {
                                        toggleEditMode(file.id);
                                      }
                                    }}
                                    className="text-[14px] font-normal text-[#2A2E34] bg-transparent focus:outline-none min-w-0"
                                    style={{
                                      width: `${inputWidths[file.id] || 200}px`,
                                      maxWidth: '100%',
                                    }}
                                    autoFocus
                                    title={getFileNameForEditing(file)}
                                  />

                                  <span className="text-[14px] text-gray-500 flex-shrink-0">
                                    .
                                    {file.originalName
                                      ? file.originalName.split('.').pop()
                                      : file.name?.split('.').pop()}
                                  </span>
                                </div>
                              ) : (
                                <a
                                  href="#"
                                  onClick={e => handleFileClick(e, file)}
                                  className={`text-[14px] leading-[20px] text-[#3F73F6] hover:underline cursor-pointer truncate ${isGeneratingUrl[file.id] ? 'opacity-50' : ''}`}
                                  title={file.name}
                                >
                                  {truncateFileName(file.name?.replaceAll('/', '_') || '')}
                                  {isGeneratingUrl[file.id] && (
                                    <span className="ml-2 text-xs text-gray-500">Loading...</span>
                                  )}
                                </a>
                              )}
                              {file.isRenamed && file.originalName && file.originalName !== file.name && !file.isEditing && (
                                <div className="text-xs text-gray-500 truncate mt-1">
                                  Originally: <span className="font-medium">{file.originalName}</span>
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2 flex-shrink-0">
                            {file.isEditing ? (
                              <button
                                type="button"
                                onClick={() => {
                                  if (file.name?.trim()) {
                                    toggleEditMode(file.id);
                                  }
                                }}
                                className="p-1 text-green-600 hover:text-green-700 transition-colors cursor-pointer"
                              >
                                <img
                                  alt="Save"
                                  loading="lazy"
                                  width="20"
                                  height="20"
                                  decoding="async"
                                  src="/assets/tick-check.svg"
                                />
                              </button>
                            ) : (
                              <>
                                <button
                                  type="button"
                                  disabled={!canEditDeleteFiles(file.id)}
                                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                                  onClick={() => toggleEditMode(file.id)}
                                >
                                  <img
                                    alt="Edit"
                                    loading="lazy"
                                    width="20"
                                    height="20"
                                    decoding="async"
                                    data-nimg="1"
                                    src="/assets/edit.svg"
                                  />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => handleDelete(file.id)}
                                  disabled={!canEditDeleteFiles(file.id)}
                                  className="p-1 text-gray-400 hover:text-red-500 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                  <img
                                    alt="Delete"
                                    loading="lazy"
                                    width="20"
                                    height="20"
                                    decoding="async"
                                    data-nimg="1"
                                    src="/assets/trash-03.svg"
                                  />
                                </button>
                              </>
                            )}
                          </div>
                        </div>
                        {file.status === 'error' && file.error && file.isEditing && (
                          <div className="mt-1 px-3 py-2 bg-red-50 border border-red-200 rounded-lg">
                            <p className="text-sm text-red-600 font-medium">{file.error}</p>
                          </div>
                        )}
                      </div>
                    )} */}
                </div>
              ))}
          </div>
        )}

        {/* Files Section Container with Border */}
        <div className="border border-[#DCE2EB] rounded-[12px] p-4 bg-white relative">
          {/* Select files label */}
          <div className="text-[14px] font-medium text-[#2A2E34] mb-3">Select files</div>

          {/* File Selection Dropdown */}
          <div className="relative">
            {/* Dropdown Trigger */}
            <div
              ref={triggerRef}
              className={`border rounded-[4px] px-[8px] py-[6px] h-[36px] text-[14px] leading-[20px] cursor-pointer hover:bg-gray-50 transition-all duration-200 bg-white ${isDropdownOpen
                ? 'border-[#3F73F6] ring-2 ring-[#3F73F6]/20'
                : 'border-[#DCE2EB]'
                }`}
              onClick={() => {
                if (!isDropdownOpen && workflowId) {
                  fetchAvailableFiles();
                }

                if (!isDropdownOpen) {
                  // Start with below positioning, will be adjusted after render
                  const triggerRect = triggerRef.current?.getBoundingClientRect();
                  if (triggerRect) {
                    const viewportHeight = window.innerHeight;
                    const spaceBelow = viewportHeight - triggerRect.bottom;
                    const spaceAbove = triggerRect.top;

                    // Default to opening below
                    let isAbove = false;
                    let top = triggerRect.bottom + 4;

                    // Only position above if there's not enough space below AND there's more space above
                    const minSpaceRequired = 200;
                    if (spaceBelow < minSpaceRequired && spaceAbove > spaceBelow) {
                      isAbove = true;
                      // For initial positioning, use a more conservative height estimate
                      // Start with a smaller gap and let the 50ms delay fix it
                      const estimatedHeight = Math.min(150, spaceAbove - 8); // Use available space or 150px max
                      top = Math.max(4, triggerRect.top - estimatedHeight - 4);
                    }

                    setDropdownPosition({
                      top,
                      left: triggerRect.left,
                      width: triggerRect.width,
                      isAbove
                    });
                  }
                } else {
                  setDropdownPosition(null);
                }

                setIsDropdownOpen(!isDropdownOpen);
              }}
            >
              <div className="flex items-center justify-between">
                <div className={`flex items-center gap-2  ${selectedFileIds.length > 0 ? 'bg-[#C7D1DF] rounded-full px-[12px] py-[4px]' : ''}`}>
                  {selectedFileIds.length > 0 ? (
                    <span className="text-[12px] text-[#2A2E34] leading-[16px]">
                      {selectedFileIds.length} selected
                    </span>
                  ) : (
                    <span className="text-[14px] text-[#5F6F84]">Select</span>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <svg
                    className={`w-4 h-4 text-[#5F6F84] transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Dropdown Content - Portal */}
            {isDropdownOpen && dropdownPosition && ReactDOM.createPortal(
              <div
                ref={dropdownRef}
                className="fixed bg-white border border-[#DCE2EB] rounded-[12px] shadow-lg z-[9999] max-h-[500px] overflow-hidden flex flex-col"
                style={{
                  top: dropdownPosition.top,
                  left: dropdownPosition.left,
                  width: dropdownPosition.width,
                }}
              >
                {/* Search Input */}
                <div className="p-3 flex-shrink-0">
                  <div className="relative">
                    <svg
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#5F6F84]"
                      fill="none"
                      stroke="currentColor"
                      width="20" height="20" viewBox="0 0 20 20"
                    >
                      <path d="M18.3346 18.3327L14.3069 14.3049M16.4828 9.07342C16.4828 13.1644 13.1664 16.4808 9.07538 16.4808C4.98438 16.4808 1.66797 13.1644 1.66797 9.07342C1.66797 4.98242 4.98438 1.66602 9.07538 1.66602C13.1664 1.66602 16.4828 4.98242 16.4828 9.07342Z" stroke="#5F6F84" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                    <input
                      type="text"
                      placeholder="Search files"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-9 pr-10 py-2 border border-[#DCE2EB] rounded-[8px] text-[14px] focus:outline-none focus:border-[#3F73F6] focus:ring-2 ring-[#3F73F6]/20"
                    />
                    {searchTerm && (
                      <button
                        onClick={() => setSearchTerm('')}
                        className="absolute cursor-pointer right-3 top-1/2 transform -translate-y-1/2 p-1 text-[#5F6F84] hover:text-[#2A2E34] transition-colors"
                        title="Clear search"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                          <path d="M5.33203 15.5423L4.45703 14.6673L9.1237 10.0007L4.45703 5.33398L5.33203 4.45898L9.9987 9.12565L14.6654 4.45898L15.5404 5.33398L10.8737 10.0007L15.5404 14.6673L14.6654 15.5423L9.9987 10.8757L5.33203 15.5423Z" fill="#A2AFC2" />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>

                {/* Dropdown Content Scroll Area */}
                <div className="flex-1 overflow-y-auto [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100">
                  {/* Loading State */}
                  {loadingFiles && (
                    <div className="flex items-center justify-center py-8">
                      <div className="flex items-center gap-2 text-[#5F6F84]">
                        <div className="w-4 h-4 border-2 border-[#3F73F6] border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-sm">Loading files...</span>
                      </div>
                    </div>
                  )}
                  {selectedFiles.length > 0 && (
                    <div>
                      <div className="px-3 py-2">
                        <span className="text-sm font-medium text-[#2A2E34]">
                          Selected
                        </span>
                      </div>
                      <div className="overflow-y-auto [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100">
                        {allSelectedFiles.map((file) => (
                          <div
                            key={file.id}
                            className="flex items-center justify-between px-3 py-2 hover:bg-[#F8F9FA] transition-colors"
                          >
                            <div className="flex items-center gap-2 flex-1 min-w-0">
                              <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                {getFileIcon(file.type, file.name)}
                              </div>
                              {editingFileId === (file.id || file.uniqueId) ? (
                                <div className="flex-1 min-w-0">
                                  {/* <input
                                    type="text"
                                    value={editingFileName}
                                    onChange={(e) => setEditingFileName(e.target.value)}
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') {
                                        if (editingFileName.trim()) {
                                          handleFileRename(file.id || file.uniqueId || '', editingFileName.trim());
                                        }
                                      } else if (e.key === 'Escape') {
                                        setEditingFileId(null);
                                        setEditingFileName('');
                                      }
                                    }}
                                    className="w-full px-2 py-1 text-[12px] border border-[#DCE2EB] rounded focus:outline-none focus:border-[#3F73F6]"
                                    autoFocus
                                    placeholder="Enter filename"
                                  /> */}
                                </div>
                              ) : (
                                <div className="flex items-center gap-2 flex-1 min-w-0">
                                  <div className="flex-1 min-w-0">
                                    {(() => {
                                      const foundFile = value.find(f => f.id === file.id || f.uniqueId === file.uniqueId);
                                      const currentDisplayName = foundFile?.name || foundFile?.displayName || file.name;
                                      const originalDisplayName = foundFile?.originalName || file.originalName;
                                      const hasBeenRenamed = (currentDisplayName !== originalDisplayName);
                                      return (
                                        <>
                                          <a
                                            href="#"
                                            onClick={async (e) => {
                                              e.preventDefault();
                                              await handleFileView(file);
                                            }}
                                            className="text-sm text-[#2A2E34] cursor-pointer truncate block font-medium"
                                            title={currentDisplayName}
                                          >
                                            {currentDisplayName}
                                          </a>
                                          {hasBeenRenamed && originalDisplayName && originalDisplayName !== currentDisplayName && (
                                            <div className="text-[10px] text-[#5F6F84] truncate mt-0.5">
                                              Originally <span className="font-medium">{originalDisplayName}</span>
                                            </div>
                                          )}
                                        </>
                                      );
                                    })()}
                                  </div>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center gap-1 flex-shrink-0 ml-2">
                              {editingFileId !== (file.id || file.uniqueId) && (
                                <>
                                  {/* When dropdown is open: show Remove and View buttons */}
                                  {isDropdownOpen && (
                                    <>
                                      {/* Remove Button */}
                                      <button
                                        onClick={() => handleFileSelect(file as any)}
                                        disabled={isDisabled}
                                        className={`p-1 transition-colors ${isDisabled
                                          ? 'text-gray-300 cursor-not-allowed'
                                          : 'text-[#5F6F84] hover:text-red-500 cursor-pointer'
                                          }`}
                                        title={isDisabled ? (isViewMode ? "Cannot remove - in view mode" : "Cannot remove - task is reviewed") : "Remove file"}
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                          <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                        </svg>
                                      </button>
                                      {/* Open Button */}
                                      <button
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleFileView(file);
                                          // if (file.url) {
                                          //   onPdfView?.(); // Notify parent that PDF is being viewed
                                          //   window.open(file.url, '_blank');
                                          // }
                                        }}
                                        className="p-1 text-[#5F6F84] hover:text-[#3F73F6] transition-colors"
                                        title="Open file"
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                          <path d="M5.83203 14.1673L14.1654 5.83398M14.1654 5.83398H5.83203M14.1654 5.83398V14.1673" stroke="#3F73F6" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                        </svg>
                                      </button>
                                    </>
                                  )}
                                </>
                              )}

                              {editingFileId === (file.id || file.uniqueId) && (
                                <>
                                  {/* Save Button - Checkmark */}
                                  <button
                                    onClick={() => {
                                      if (editingFileName.trim()) {
                                        handleFileRename(file.id || file.uniqueId || '', editingFileName.trim());
                                      }
                                    }}
                                    className="p-1 text-[#3F73F6] hover:text-[#3F73F6] transition-colors cursor-pointer"
                                    title="Save changes"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                      <path d="M2.5 10L7.5 15L17.5 5" stroke="#5F6F84" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                  </button>
                                  {/* Cancel Button - X */}
                                  <button
                                    onClick={() => {
                                      setEditingFileId(null);
                                      setEditingFileName('');
                                    }}
                                    className="p-1 text-[#5F6F84] hover:text-red-500 transition-colors cursor-pointer"
                                    title="Cancel"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                      <path d="M15 5L5 15M5 5L15 15" stroke="#5F6F84" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                  </button>
                                </>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  {!loadingFiles && suggestedFiles.length > 0 && (
                    <div>
                      <div className="px-3 py-2">
                        <span className="text-sm font-medium text-[#2A2E34]">
                          Suggestions
                        </span>
                      </div>
                      <div className="overflow-y-auto [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100">
                        {suggestedFiles.map((file) => (
                          <div
                            key={file.id}
                            className="flex items-center justify-between px-3 py-2 hover:bg-[#F8F9FA] transition-colors cursor-pointer"
                            onClick={() => handleFileSelect(file)}
                          >
                            <div className="flex items-center gap-2 flex-1 min-w-0">
                              <div className="w-8 h-8 flex items-center justify-center flex-shrink-0">
                                {getFileIcon(file.type, file.name)}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="text-sm text-[#2A2E34] truncate" title={file.name}>
                                  {file.name}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-1 flex-shrink-0 ml-2">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (file.url) {
                                    onPdfView?.(); // Notify parent that PDF is being viewed
                                    window.open(file.url, '_blank');
                                  }
                                }}
                                className="p-1 text-[#5F6F84] hover:text-[#3F73F6] transition-colors"
                                title="Open file"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                  <path d="M5.83203 14.1673L14.1654 5.83398M14.1654 5.83398H5.83203M14.1654 5.83398V14.1673" stroke="#3F73F6" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  {!loadingFiles && filteredAvailableFiles.length === 0 && (
                    <div className="px-3 py-8 text-center">
                      <div className="text-[#5F6F84] text-[14px]">
                        {searchTerm ? 'No files found matching your search' : 'No files available'}
                      </div>
                    </div>
                  )}
                </div>
              </div>,
              document.body
            )}
          </div>
          <div className="mt-4">
            <div className="text-[14px] font-medium text-[#2A2E34] mb-2">Selected files</div>
            {selectedFileIds.length === 0 ? (
              <div className="text-[14px] text-[#5F6F84]">No files selected</div>
            ) : (
              <div className="space-y-2">
                {selectedFiles.map((file) => (
                  <div key={file.id} className="flex items-center justify-between p-2 border border-[#DCE2EB] rounded-[4px]">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <div className="w-5 h-5 flex items-center justify-center flex-shrink-0">
                        {getFileIcon(file.type, file.name)}
                      </div>
                      <div className="flex-1 min-w-0">
                        {editingFileId === (file.id || file.uniqueId) ? (
                          <input
                            type="text"
                            value={editingFileName}
                            onChange={(e) => setEditingFileName(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                if (editingFileName.trim()) {
                                  handleFileRename(file.id || file.uniqueId || '', editingFileName.trim());
                                }
                              } else if (e.key === 'Escape') {
                                setEditingFileId(null);
                                setEditingFileName('');
                              }
                            }}
                            className="w-full px-2 py-1 text-sm border-none outline-none bg-transparent"
                            autoFocus
                            placeholder="Enter filename"
                          />
                        ) : (
                          <div>
                            {(() => {
                              const foundFile = value.find(f => f.id === file.id || f.uniqueId === file.uniqueId);
                              console.log(foundFile, "foundFile");
                              const currentDisplayName = foundFile?.name || foundFile?.displayName || file.name;
                              const originalDisplayName = foundFile?.originalName || file.originalName;
                              const hasBeenRenamed = (currentDisplayName !== originalDisplayName);

                              return (
                                <>
                                  <div className="text-sm text-[#2A2E34] truncate font-medium" title={currentDisplayName}>
                                    {currentDisplayName}
                                  </div>
                                  {hasBeenRenamed && originalDisplayName && (
                                    <div className="text-xs text-[#5F6F84] truncate mt-0.5">
                                      Originally <span className="font-medium">{originalDisplayName}</span>
                                    </div>
                                  )}
                                </>
                              );
                            })()}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-1 flex-shrink-0">
                      {editingFileId !== (file.id || file.uniqueId) && (
                        <>
                          {/* Edit Button - always show in Selected files section */}
                          <button
                            onClick={() => {
                              if (isDisabled) return;
                              setEditingFileId(file.id || file.uniqueId || '');
                              // Get the current display name for editing
                              const foundFile = value.find(f => f.id === file.id || f.uniqueId === file.uniqueId);
                              const currentDisplayName = foundFile?.name || file.name;
                              setEditingFileName(currentDisplayName.replace(/\.[^/.]+$/, ""));
                            }}
                            disabled={isDisabled}
                            className={`p-1 transition-colors ${isDisabled
                              ? 'text-gray-300 cursor-not-allowed'
                              : 'text-[#5F6F84] hover:text-[#3F73F6] cursor-pointer'
                              }`}
                            title={isDisabled ? (isViewMode ? "Cannot edit - in view mode" : "Cannot edit - task is reviewed") : "Rename file"}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                              <path d="M9.16797 3.33417H5.66797C4.26784 3.33417 3.56777 3.33417 3.03299 3.60666C2.56259 3.84634 2.18014 4.22879 1.94045 4.6992C1.66797 5.23398 1.66797 5.93404 1.66797 7.33417V14.3342C1.66797 15.7343 1.66797 16.4344 1.94045 16.9691C2.18014 17.4396 2.56259 17.822 3.03299 18.0617C3.56777 18.3342 4.26784 18.3342 5.66797 18.3342H12.668C14.0681 18.3342 14.7682 18.3342 15.3029 18.0617C15.7734 17.822 16.1558 17.4396 16.3955 16.9691C16.668 16.4344 16.668 15.7343 16.668 14.3342V10.8342M6.66795 13.3342H8.0634C8.47105 13.3342 8.67488 13.3342 8.86669 13.2881C9.03675 13.2473 9.19932 13.18 9.34844 13.0886C9.51664 12.9855 9.66076 12.8414 9.94902 12.5531L17.918 4.58417C18.6083 3.89382 18.6083 2.77453 17.918 2.08417C17.2276 1.39382 16.1083 1.39382 15.418 2.08417L7.449 10.0531C7.16074 10.3414 7.01662 10.4855 6.91355 10.6537C6.82217 10.8028 6.75482 10.9654 6.714 11.1355C6.66795 11.3273 6.66795 11.5311 6.66795 11.9387V13.3342Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                          </button>
                          {/* Delete Button */}
                          <button
                            onClick={() => {
                              if (isDisabled) return;
                              handleFileSelect(file);
                            }}
                            disabled={
                              isDisabled || (isChildWorkflow && !!file.mycaseDocumentId && file.mycaseDocumentId !== "")
                            }
                            className={`p-1 transition-colors ${isDisabled
                              ? 'text-gray-300 cursor-not-allowed'
                              : 'text-[#5F6F84] hover:text-red-500 cursor-pointer'
                              }`}
                            title={
                              isDisabled
                                ? (isViewMode ? "Cannot remove - in view mode" : "Cannot remove - task is reviewed")
                                : (isChildWorkflow && !!file.mycaseDocumentId && file.mycaseDocumentId !== "")
                                  ? "Cannot remove - file is already uploaded to MyCase"
                                  : "Remove file"
                            }
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                              <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                          </button>
                        </>
                      )}

                      {editingFileId === (file.id || file.uniqueId) && (
                        <>
                          {/* Save Button - Checkmark */}
                          <button
                            onClick={() => {
                              if (editingFileName.trim()) {
                                handleFileRename(file.id || file.uniqueId || '', editingFileName.trim());
                              }
                            }}
                            className="p-1 text-[#3F73F6] hover:text-[#3F73F6] transition-colors cursor-pointer"
                            title="Save changes"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                              <path d="M2.5 10L7.5 15L17.5 5" stroke="#5F6F84" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
);

FileUpload.displayName = 'FileUpload';

export default FileUpload;