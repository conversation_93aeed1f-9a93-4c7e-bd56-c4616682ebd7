import React from 'react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  showItemsPerPage?: boolean;
  className?: string;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  onItemsPerPageChange,
  showItemsPerPage = true,
  className = '',
}) => {
  const getVisiblePages = () => {
    const delta = 2; // Number of pages to show on each side of current page
    const range = [];
    const rangeWithDots = [];

    // Calculate range of pages to show
    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    // Add first page
    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    // Add middle pages
    rangeWithDots.push(...range);

    // Add last page
    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  if (totalPages <= 1) {
    return null;
  }

  return (
    <div className={`flex items-center justify-between ${className}`}>
      {/* Items info */}
      <div className="flex items-center gap-4">
        <span className="text-sm text-[#5F6F84]">
          Showing {startItem} to {endItem} of {totalItems} results
        </span>

        {showItemsPerPage && onItemsPerPageChange && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-[#5F6F84]">Show:</span>
            <select
              value={itemsPerPage}
              onChange={(e) => onItemsPerPageChange(parseInt(e.target.value))}
              className="px-3 py-1 border border-[#E1E5E9] rounded-md text-sm bg-white focus:outline-none focus:ring-2 focus:ring-[#3F73F6] focus:border-transparent"
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
        )}
      </div>

      {/* Pagination controls */}
      <div className="flex items-center gap-1">
        {/* Previous button */}
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`w-10 h-10 flex items-center justify-center text-sm font-medium rounded-[12px] border border-[#DCE2EB] transition-colors ${currentPage === 1
            ? 'text-[#C7D1DF] cursor-not-allowed'
            : 'text-[#5F6F84] hover:text-[#3F73F6] hover:bg-[#F8F9FA]'
            }`}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.5 15L7.5 10L12.5 5" stroke={currentPage === 1 ? "#C7D1DF" : "#5F6F84"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </button>

        {/* Page numbers */}
        <div className="flex items-center gap-1">
          {getVisiblePages().map((page, index) => (
            <React.Fragment key={index}>
              {page === '...' ? (
                <button
                  className="w-10 h-10 flex items-center justify-center text-[14px] leading-[20px] font-medium rounded-[12px] border border-[#DCE2EB] text-[#C0C4CC]"
                >
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10.0002 10.8334C10.4604 10.8334 10.8335 10.4603 10.8335 10.0001C10.8335 9.53984 10.4604 9.16675 10.0002 9.16675C9.53993 9.16675 9.16683 9.53984 9.16683 10.0001C9.16683 10.4603 9.53993 10.8334 10.0002 10.8334Z" stroke="#5F6F84" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M15.8335 10.8334C16.2937 10.8334 16.6668 10.4603 16.6668 10.0001C16.6668 9.53984 16.2937 9.16675 15.8335 9.16675C15.3733 9.16675 15.0002 9.53984 15.0002 10.0001C15.0002 10.4603 15.3733 10.8334 15.8335 10.8334Z" stroke="#5F6F84" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M4.16683 10.8334C4.62707 10.8334 5.00016 10.4603 5.00016 10.0001C5.00016 9.53984 4.62707 9.16675 4.16683 9.16675C3.70659 9.16675 3.3335 9.53984 3.3335 10.0001C3.3335 10.4603 3.70659 10.8334 4.16683 10.8334Z" stroke="#5F6F84" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </button>
              ) : (
                <button
                  onClick={() => onPageChange(page as number)}
                  className={`w-10 h-10 text-[14px] leading-[20px] font-medium rounded-[12px] border border-[#DCE2EB] transition-colors text-[#5F6F84] ${currentPage === page
                    ? 'bg-[#3F73F6] text-white'
                    : 'hover:text-[#2A2E34] hover:bg-[#F8F9FA]'
                    }`}
                >
                  {page}
                </button>
              )}
            </React.Fragment>
          ))}
        </div>

        {/* Next button */}
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`w-10 h-10 flex items-center justify-center text-sm font-medium rounded-[12px] border border-[#DCE2EB] transition-colors ${currentPage === totalPages
            ? 'text-[#C7D1DF] cursor-not-allowed'
            : 'text-[#5F6F84] hover:text-[#3F73F6] hover:bg-[#F8F9FA]'
            }`}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.5 15L12.5 10L7.5 5" stroke="#5F6F84" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default Pagination;
