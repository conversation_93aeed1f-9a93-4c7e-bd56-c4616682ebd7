import React, { useEffect, useState, useRef } from 'react';
// import Image from 'next/image';
import 'react-datepicker/dist/react-datepicker.css';
import DatePicker from 'react-datepicker';
import apiClient from '@/services/api/config';
import moment from 'moment-timezone';

interface DateTimeModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialDate?: string;
  initialTime?: string;
  initializePeriod?: string;
  onSave?: (date: string, time: string) => void;
  workflowId?: string;
  taskId?: string;
  showTime?: boolean; // New prop to control time picker visibility
  isRangePicker?: boolean; // New prop for range picker mode
  _currentOperator?: string; // Current filter operator
  position?: { top: number; left: number; openAbove?: boolean }; // Position for absolute positioning
}

// Get user's timezone
const getUserTimezone = (): string => {
  if (typeof window === 'undefined') return 'UTC';
  return localStorage.getItem('timezone') || Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
};

// Month data for better rendering
const monthData = {
  names: [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ],
  shortNames: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
  days: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
};

// Custom time picker component
const CustomTimePicker = ({
  selected,
  onChange,
  hasError = false,
  disabled = false,
  minTime,
  maxTime,
}: {
  selected: Date | null;
  onChange: (time: Date | null) => void;
  name?: string;
  hasError?: boolean;
  disabled?: boolean;
  minTime?: Date;
  maxTime?: Date;
}) => {
  // Create default values if either min or max is missing to avoid the DatePicker error
  const safeMinTime =
    minTime ||
    (() => {
      const min = new Date();
      min.setHours(0, 0, 0, 0);
      return min;
    })();

  const safeMaxTime =
    maxTime ||
    (() => {
      const max = new Date();
      max.setHours(23, 59, 0, 0);
      return max;
    })();

  return (
    <div className="relative w-full">
      <DatePicker
        selected={selected}
        onChange={onChange}
        showTimeSelect
        showTimeSelectOnly
        timeIntervals={15}
        timeCaption="Time"
        dateFormat="h:mm aa"
        timeFormat="h:mm aa"
        className={`w-full border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer
          ${hasError ? 'border-red-500' : ''} ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
        placeholderText="Select"
        disabled={disabled}
        popperPlacement="bottom-start"
        popperClassName="time-picker-popper"
        minTime={safeMinTime}
        maxTime={safeMaxTime}
      />
      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z"
            stroke="#5F6F84"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M15.71 15.18L12.61 13.33C12.07 13.01 11.63 12.24 11.63 11.61V7.51"
            stroke="#5F6F84"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>
    </div>
  );
};

const DateTimeModal: React.FC<DateTimeModalProps> = ({
  isOpen,
  onClose,
  initialDate = '04/12/25',
  initialTime = '10:00',
  initializePeriod,
  onSave,
  workflowId,
  taskId,
  showTime = true,
  isRangePicker = false,
  _currentOperator = '',
  position,
}) => {
  // Parse initial date for default values
  const parseInitialDate = () => {
    if (!initialDate) return { month: 3, year: 2025 }; // Default: April 2025

    try {
      const parts = initialDate.split('/');
      if (parts.length === 3) {
        const month = parseInt(parts[0]) - 1; // 0-indexed month
        const year = parts[2].length === 2 ? 2000 + parseInt(parts[2]) : parseInt(parts[2]);
        return { month, year };
      }
    } catch (e) {
      console.error('Error parsing date:', e);
    }

    return { month: 3, year: 2025 }; // Fallback
  };

  // Parse initial time for default values
  const parseInitialTime = () => {
    if (!initialTime) return { hour: 10, minute: 0, period: 'AM' };

    try {
      // Handle formats like "10:00 AM" or "10:00AM" or just "10:00" with separate period
      const timePart = initialTime.replace(/\s+/g, ' ').trim();
      const periodMatch = initializePeriod;
      const period = periodMatch ? periodMatch.toUpperCase() : 'AM';

      // Extract time without period (in case it's already included)
      const timeValue = timePart.replace(/(AM|PM)$/i, '').trim();

      const [hourStr, minuteStr] = timeValue.split(':');
      const hour = parseInt(hourStr);
      const minute = parseInt(minuteStr || '0');

      return { hour, minute, period };
    } catch (e) {
      console.error('Error parsing time:', e);
    }

    return { hour: 10, minute: 0, period: 'AM' }; // Fallback
  };

  const { month: initialMonth, year: initialYear } = parseInitialDate();
  const { hour: initialHour, minute: initialMinute, period: initialPeriod } = parseInitialTime();

  // Calendar navigation state
  const [currentMonth, setCurrentMonth] = useState(initialMonth);
  const [currentYear, setCurrentYear] = useState(initialYear);
  const [calendarDays, setCalendarDays] = useState<
    { day: number; isCurrentMonth: boolean; date: Date }[]
  >([]);

  // Single date picker state
  const [selectedDate, setSelectedDate] = useState('');

  // Range picker state
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [isSelectingStart, setIsSelectingStart] = useState(true);

  // Time picker state (only used when showTime is true)
  const [showTimeDropdown, setShowTimeDropdown] = useState(false);

  // Time state
  const [selectedHour, setSelectedHour] = useState(initialHour);
  const [selectedMinute, setSelectedMinute] = useState(initialMinute);
  const [selectedPeriod, setSelectedPeriod] = useState(initialPeriod);
  const [selectedTimeObj, setSelectedTimeObj] = useState<Date | null>(null);

  // Derived formatted time
  const formattedTime = `${selectedHour}:${selectedMinute.toString().padStart(2, '0')} ${selectedPeriod}`;

  // Refs for click outside detection
  const timeDropdownRef = useRef<HTMLDivElement>(null);
  const timeInputRef = useRef<HTMLInputElement>(null);

  // Initialize calendar when component mounts or month/year changes
  useEffect(() => {
    generateCalendarDays(currentMonth, currentYear);
  }, [currentMonth, currentYear]);

  // Initialize component state when modal opens
  useEffect(() => {
    if (isOpen) {
      // Reset states
      setIsSelectingStart(true);

      if (isRangePicker) {
        // Initialize range picker - parse existing range value if available
        if (initialDate && initialDate.includes('|')) {
          // Parse existing range value (format: "start|end")
          const [start, end] = initialDate.split('|');
          setStartDate(start || '');
          setEndDate(end || '');

          // Set calendar to the start date's month if available
          if (start) {
            try {
              const [month, day, yearStr] = start.split('/');
              const year = yearStr.length === 2 ? `20${yearStr}` : yearStr;
              const dateObj = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
              if (!isNaN(dateObj.getTime())) {
                setCurrentMonth(dateObj.getMonth());
                setCurrentYear(dateObj.getFullYear());
              }
            } catch (e) {
              console.error('Error parsing start date:', e);
              // Fallback to current month
              const today = new Date();
              setCurrentMonth(today.getMonth());
              setCurrentYear(today.getFullYear());
            }
          } else {
            // No start date, use current month
            const today = new Date();
            setCurrentMonth(today.getMonth());
            setCurrentYear(today.getFullYear());
          }
        } else {
          // No existing range value, start fresh with today's date as default for start
          const todaysDate = getTodaysDate();
          const today = new Date();
          setStartDate(todaysDate);
          setEndDate('');

          // Set calendar to current month
          setCurrentMonth(today.getMonth());
          setCurrentYear(today.getFullYear());
        }

        setSelectedDate('');
      } else {
        // Initialize single date picker
        setStartDate('');
        setEndDate('');

        // Set initial date if provided, otherwise use today's date
        const dateToUse = initialDate || getTodaysDate();
        setSelectedDate(dateToUse);

        try {
          const [month, day, yearStr] = dateToUse.split('/');
          const year = yearStr.length === 2 ? `20${yearStr}` : yearStr;
          const dateObj = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
          if (!isNaN(dateObj.getTime())) {
            // Set calendar to the selected date's month
            setCurrentMonth(dateObj.getMonth());
            setCurrentYear(dateObj.getFullYear());
          }
        } catch (e) {
          console.error('Error parsing date:', e);
        }
      }

      // Initialize time picker if needed
      if (showTime) {
        // Update time state with initial values when modal opens
        setSelectedHour(initialHour);
        setSelectedMinute(initialMinute);
        setSelectedPeriod(initialPeriod);
        
        try {
          const timeObj = new Date();
          let hour = initialHour; // Use initialHour instead of selectedHour

          if (initialPeriod === 'PM' && hour < 12) {
            hour += 12;
          } else if (initialPeriod === 'AM' && hour === 12) {
            hour = 0;
          }

          timeObj.setHours(hour, initialMinute, 0, 0);
          setSelectedTimeObj(timeObj);
        } catch (e) {
          console.error('Error setting time object:', e);
        }
      }
    }
  }, [isOpen, isRangePicker, initialDate, showTime]);

  // Update timeObj when time components change
  useEffect(() => {
    if (selectedHour !== undefined && selectedMinute !== undefined) {
      const timeObj = new Date();
      let hour = selectedHour;

      // Convert to 24-hour format for Date object
      if (selectedPeriod === 'PM' && hour < 12) {
        hour += 12;
      } else if (selectedPeriod === 'AM' && hour === 12) {
        hour = 0;
      }

      timeObj.setHours(hour, selectedMinute, 0, 0);
      // console.log("🚀 ~ useEffect ~ timeObj ----2:", timeObj);

      setSelectedTimeObj(timeObj);
    }
  }, [selectedHour, selectedMinute, selectedPeriod]);

  // Handle click outside time dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        timeDropdownRef.current &&
        timeInputRef.current &&
        !timeDropdownRef.current.contains(event.target as Node) &&
        !timeInputRef.current.contains(event.target as Node)
      ) {
        setShowTimeDropdown(false);
      }
    }

    if (showTimeDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showTimeDropdown]);

  // Generate calendar days for the current month view
  const generateCalendarDays = (month: number, year: number) => {
    const firstDayOfMonth = new Date(year, month, 1);
    const lastDayOfMonth = new Date(year, month + 1, 0);
    const daysInMonth = lastDayOfMonth.getDate();

    // Get the day of week for the first day (0 = Sunday, 6 = Saturday)
    const firstDayOfWeek = firstDayOfMonth.getDay();

    // Get the last day of previous month
    const lastDayOfPrevMonth = new Date(year, month, 0).getDate();

    const days: { day: number; isCurrentMonth: boolean; date: Date }[] = [];

    // Add days from previous month
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const day = lastDayOfPrevMonth - i;
      const date = new Date(year, month - 1, day);
      days.push({ day, isCurrentMonth: false, date });
    }

    // Add days of current month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      days.push({ day, isCurrentMonth: true, date });
    }

    // Add days from next month to fill out the calendar (up to 42 days total for 6 weeks)
    const remainingDays = 42 - days.length;
    for (let day = 1; day <= remainingDays; day++) {
      const date = new Date(year, month + 1, day);
      days.push({ day, isCurrentMonth: false, date });
    }

    setCalendarDays(days);
  };

  // Format date for display in the input field (MM/DD/YY)
  const formatSelectedDate = (date: Date): string => {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${month}/${day}/${year}`;
  };

  // Get today's date as default
  const getTodaysDate = (): string => {
    const today = new Date();
    return formatSelectedDate(today);
  };

  // Parse date string (MM/DD/YYYY) to Date object
  const parseDate = (dateString: string): Date => {
    const [month, day, year] = dateString.split('/');
    return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
  };

  // Check if a date should be disabled (for range picker end date selection)
  const isDateDisabled = (date: Date): boolean => {
    if (!isRangePicker || isSelectingStart || !startDate) return false;

    const startDateObj = parseDate(startDate);
    const currentDate = new Date(date);

    // Normalize dates for comparison (remove time component)
    startDateObj.setHours(0, 0, 0, 0);
    currentDate.setHours(0, 0, 0, 0);

    return currentDate < startDateObj;
  };

  // Navigate to previous or next month
  const navigateMonth = (direction: 'prev' | 'next') => {
    if (direction === 'next') {
      if (currentMonth === 11) {
        setCurrentMonth(0);
        setCurrentYear(currentYear + 1);
      } else {
        setCurrentMonth(currentMonth + 1);
      }
    } else {
      if (currentMonth === 0) {
        setCurrentMonth(11);
        setCurrentYear(currentYear - 1);
      } else {
        setCurrentMonth(currentMonth - 1);
      }
    }
  };

  // Check if a day is today
  const isToday = (date: Date): boolean => {
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  // Check if a date is before today (past date)
  // const isBeforeToday = (date: Date): boolean => {
  //   const today = new Date();
  //   today.setHours(0, 0, 0, 0); // Remove time part for accurate date comparison
  //   return date < today;
  // };

  // Check if a day is selected
  const isSelected = (date: Date): boolean => {
    const formattedDate = formatSelectedDate(date);

    if (isRangePicker) {
      return formattedDate === startDate || formattedDate === endDate;
    } else {
      return formattedDate === selectedDate;
    }
  };

  // Check if a date is in the selected range (for range picker)
  const isInRange = (date: Date): boolean => {
    if (!isRangePicker || !startDate || !endDate) return false;

    try {
      // Parse start date (MM/DD/YYYY format)
      const [startMonth, startDay, startYear] = startDate.split('/');
      const start = new Date(
        `${startYear}-${startMonth.padStart(2, '0')}-${startDay.padStart(2, '0')}`
      );

      // Parse end date (MM/DD/YYYY format)
      const [endMonth, endDay, endYear] = endDate.split('/');
      const end = new Date(`${endYear}-${endMonth.padStart(2, '0')}-${endDay.padStart(2, '0')}`);

      // Normalize the current date for comparison
      const current = new Date(date);
      current.setHours(0, 0, 0, 0);
      start.setHours(0, 0, 0, 0);
      end.setHours(0, 0, 0, 0);

      return current > start && current < end;
    } catch (e) {
      console.error('Error parsing range dates:', e);
      return false;
    }
  };

  // Handle date selection
  const handleDateSelect = (day: number, isCurrentMonth: boolean, date: Date) => {
    if (!isCurrentMonth) {
      // If selecting a day from previous/next month, navigate to that month
      setCurrentMonth(date.getMonth());
      setCurrentYear(date.getFullYear());
    }

    const formattedDate = formatSelectedDate(date);

    if (isRangePicker) {
      // Range picker logic
      if (isSelectingStart) {
        setStartDate(formattedDate);
        setEndDate(''); // Clear end date when start date changes
        setIsSelectingStart(false); // Switch to selecting end date
      } else {
        // Validate that end date is not before start date
        if (startDate) {
          const startDateObj = parseDate(startDate);
          const endDateObj = parseDate(formattedDate);

          if (endDateObj < startDateObj) {
            // Don't set end date if it's before start date - provide visual feedback
            console.warn('End date cannot be before start date');
            return;
          }
        }
        setEndDate(formattedDate);
        // Keep both dates selected, let user apply manually
      }
    } else {
      // Single date picker logic
      setSelectedDate(formattedDate);
    }
  };

  // Handle time change
  const handleTimeChange = (time: Date | null) => {
    if (!time) return;

    // Extract hour, minute, period from the Date object
    let hours = time.getHours();
    const minutes = time.getMinutes();
    const period = hours >= 12 ? 'PM' : 'AM';

    // Convert 24-hour format to 12-hour
    if (hours > 12) hours -= 12;
    if (hours === 0) hours = 12;

    setSelectedHour(hours);
    setSelectedMinute(minutes);
    setSelectedPeriod(period);
    setSelectedTimeObj(time);
  };

  // API call to save date and time
  const saveDateTime = async () => {
    try {
      const userTimezone = getUserTimezone();
      
      // Convert to UTC like events do - handles cross-day scenarios
      const utcDate = moment.tz(`${selectedDate} ${formattedTime}`, 'MM/DD/YYYY hh:mm A', userTimezone).utc();
      const isoString = utcDate.toISOString(); // "2025-09-18T06:30:00.000Z"
      
      // Only make API call if workflowId is provided
      if (workflowId) {
        await apiClient.put(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/update-end-date`, {
          workflow_id: workflowId,
          task_id: taskId,
          end_date: isoString, // Send UTC ISO string
        });
      }

      // Call the onSave callback
      if (onSave) {
        onSave(selectedDate, formattedTime);
      }

      // Close modal immediately
      onClose();
    } catch (error) {
      console.error('Error updating date and time:', error);
      // Still close the modal even on error
      onClose();
    }
  };

  // Apply button handler
  const handleApply = () => {
    if (isRangePicker) {
      // For range picker, send start and end dates in pipe-separated format
      if (onSave) {
        const rangeValue =
          startDate && endDate ? `${startDate}|${endDate}` : startDate || endDate || '';
        onSave(rangeValue, '');
      }
    } else {
      // For single date picker
      if (showTime) {
        // Use existing saveDateTime logic for time-enabled mode
        saveDateTime();
        return; // saveDateTime handles closing
      } else {
        // For date-only mode, just send the selected date
        if (onSave && selectedDate) {
          onSave(selectedDate, '');
        }
      }
    }
    // Always close after applying
    onClose();
  };

  // Get default min and max times for the time picker
  const getMinTime = (): Date => {
    const min = new Date();
    min.setHours(0, 0, 0, 0);
    return min;
  };

  const getMaxTime = (): Date => {
    const max = new Date();
    max.setHours(23, 59, 0, 0);
    return max;
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-[999999]"
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.4' }}
      onClick={e => {
        // Prevent event propagation to avoid closing parent filter panel
        e.stopPropagation();
      }}
    >
      <div className="absolute inset-0  bg-opacity-40" onClick={onClose}></div>
      <div
        className={`absolute bg-white rounded-[16px] shadow-lg z-10 overflow-hidden ${isRangePicker ? 'w-[368px]' : 'w-[328px] max-w-md'}`}
        style={position ? {
          top: position.openAbove ? `${position.top - 400}px` : `${position.top}px`,
          left: `${position.left}px`,
        } : {
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
        }}
        onClick={e => {
          // Prevent clicks inside the modal from propagating
          e.stopPropagation();
        }}
      >
        <div className="px-3 py-6">
          {/* Select date header button - common for both modes */}
          {/* <div className="mb-4">
            <button className="w-full flex items-center justify-between px-4 py-3 border border-blue-500 rounded-full text-gray-600 bg-blue-50 text-sm font-medium">
              <span>Select date</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div> */}

          {/* Conditional content based on picker type */}
          {isRangePicker ? (
            /* Range picker: Start and End inputs */
            <div className="mx-[32px] my-[16px]">
              {/* Selection mode indicator */}
              {/* <div className="mb-3 text-center">
                <span className="text-sm text-gray-600">
                  {isSelectingStart
                    ? 'Select start date'
                    : startDate
                      ? 'Select end date'
                      : 'Select end date'}
                </span>
              </div> */}
              <div className="flex gap-4">
                <div className="flex-1">
                  <label className="block text-sm text-gray-600 mb-2">Start</label>
                  <div className="relative">
                    <input
                      type="text"
                      value={startDate}
                      placeholder="Select a date"
                      readOnly
                      className={`w-full px-[12px] py-[8px] text-[14px] leading-[20px] border rounded-[4px] cursor-pointer transition-all ${isSelectingStart
                          ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                          : 'border-gray-300 bg-white hover:border-gray-400'
                        }`}
                      onClick={() => setIsSelectingStart(true)}
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8 2V5"
                          stroke="#9CA3AF"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M16 2V5"
                          stroke="#9CA3AF"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M3.5 9H20.5"
                          stroke="#9CA3AF"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z"
                          stroke="#9CA3AF"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div className="flex-1">
                  <label className="block text-sm text-gray-400 mb-2">End</label>
                  <div className="relative">
                    <input
                      type="text"
                      value={endDate}
                      placeholder="Select a date"
                      readOnly
                      className={`w-full px-[12px] py-[8px] text-[14px] leading-[20px] border rounded-lg cursor-pointer transition-all ${!isSelectingStart
                          ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                          : 'border-gray-300 bg-white hover:border-gray-400'
                        }`}
                      onClick={() => setIsSelectingStart(false)}
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8 2V5"
                          stroke="#9CA3AF"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M16 2V5"
                          stroke="#9CA3AF"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M3.5 9H20.5"
                          stroke="#9CA3AF"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z"
                          stroke="#9CA3AF"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Single date picker: Time picker only when showTime is true */
            showTime && (
              <div className="mb-4">
                <div className="flex space-x-4">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                    <div className="relative">
                      <input
                        type="text"
                        value={selectedDate}
                        placeholder="Select a date"
                        readOnly
                        className="w-full border border-gray-300 rounded-lg py-2 px-3 bg-gray-50"
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8 2V5"
                            stroke="#9CA3AF"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M16 2V5"
                            stroke="#9CA3AF"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M3.5 9H20.5"
                            stroke="#9CA3AF"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z"
                            stroke="#9CA3AF"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Time</label>
                    <CustomTimePicker
                      selected={selectedTimeObj}
                      onChange={handleTimeChange}
                      minTime={getMinTime()}
                      maxTime={getMaxTime()}
                    />
                  </div>
                </div>
              </div>
            )
          )}

          {/* Calendar */}
          <div className="calendar-container">
            {/* Month/year navigation */}
            <div className="flex items-center px-[32px] py-[16px] justify-between">
              <button
                className="p-1 rounded hover:bg-gray-100"
                onClick={() => navigateMonth('prev')}
                aria-label="Previous month"
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15 19.92L8.48 13.4C7.71 12.63 7.71 11.37 8.48 10.6L15 4.08"
                    stroke="#5F6F84"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>

              <div className="flex flex-col items-center">
                <div className="text-[20px] leading-[28px] font-medium text-[#5F6F84]">
                  {monthData.names[currentMonth]} {currentYear}
                </div>
              </div>

              <button
                className="p-1 rounded hover:bg-gray-100"
                onClick={() => navigateMonth('next')}
                aria-label="Next month"
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9 4.08L15.52 10.6C16.29 11.37 16.29 12.63 15.52 13.4L9 19.92"
                    stroke="#5F6F84"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
            </div>
             <div className="h-px bg-[#E5E9EB] my-2 mx-1">
               
             </div>
            {/* Days of week headers */}
            <div className="px-[32px]">
              <div className="grid grid-cols-7 gap-y-2 text-center mb-1 py-[10px]">
                {monthData.days.map(day => (
                  <div key={day} className="text-[12px] leading-[16px] font-medium text-[#5F6F84]">
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar days */}
              <div className="grid grid-cols-7 text-center">
                {calendarDays.map((dayInfo, index) => {
                  const isSelectedDate = isSelected(dayInfo.date);
                  const isInDateRange = isInRange(dayInfo.date);
                  const isTodayDate = isToday(dayInfo.date);
                  const isDisabled = isDateDisabled(dayInfo.date);

                  return (
                    <div
                      key={index}
                      className={`
                      h-[42px] text-[14px] leading-[20px] font-regular rounded-full w-9 mx-auto flex items-center justify-center
                      transition-colors
                      ${isDisabled
                          ? 'text-gray-300 cursor-not-allowed '
                          : 'cursor-pointer'
                        }
                      ${!dayInfo.isCurrentMonth && !isDisabled ? 'text-gray-400' : ''}
                      ${dayInfo.isCurrentMonth && !isDisabled && !isSelectedDate && !isInDateRange ? 'hover:text-[#2C5EC9]' : ''}
                      ${isSelectedDate ? 'text-[#2C5EC9] hover:text-[#2C5EC9] font-medium' : ''}
                      ${isInDateRange ? 'bg-blue-50 text-[#3F73F6]' : ''}
                      ${isTodayDate && !isSelectedDate && !isInDateRange && !isDisabled ? 'font-medium' : ''}
                    `}
                      onClick={() => {
                        if (!isDisabled) {
                          handleDateSelect(dayInfo.day, dayInfo.isCurrentMonth, dayInfo.date);
                        }
                      }}
                    >
                      {dayInfo.day}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Footer with action buttons */}
          <div className="flex justify-end space-x-2 px-[32px] py-[16px]">
            <button
              className="px-4 py-2 bg-[#3F73F6] text-white rounded-lg font-medium hover:bg-blue-600 flex items-center justify-center"
              onClick={handleApply}
            >
              Apply
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DateTimeModal;
