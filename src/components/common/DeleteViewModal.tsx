import React from 'react';
import { createPortal } from 'react-dom';
import { AlertTriangle } from 'lucide-react';
import { Poppins } from 'next/font/google';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

interface DeleteViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  _viewName: string;
  isLoading?: boolean;
}

const DeleteViewModal: React.FC<DeleteViewModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  _viewName,
  isLoading = false,
}) => {
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const _handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'Enter') {
      onConfirm();
    }
  };

  if (!isOpen) return null;

  const modalElement = (
    <div
      className="fixed inset-0 flex items-center justify-center z-[9999]"
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.4)' }}
      onClick={handleBackdropClick}
      onKeyDown={_handleKeyPress}
      tabIndex={-1}
    >
      <div
        className={`bg-white relative border border-[#E5E7EB] ${poppins.className}`}
        style={{
          width: '400px',
          height: '272px',
          borderRadius: '20px',
          padding: '30px',
          boxShadow: '0px 8px 32px 0px #0000001A',
        }}
        onClick={e => e.stopPropagation()}
      >
        {/* Content with Icon */}
        <div className="flex flex-col items-center text-center h-full justify-between">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-[#FEE2E2] rounded-full flex items-center justify-center mb-6">
              <AlertTriangle className="w-6 h-6 text-[#DC2626]" />
            </div>
            <h2 className="text-[18px] font-medium text-[#2A2E34] leading-[28px] mb-3">
              Delete this view?
            </h2>
            <p className="text-[14px] text-[#5F6F84] leading-[20px] max-w-[300px]">
              Are you sure you want to delete this saved view? This process can&apos;t be undone.
            </p>
          </div>

          {/* Footer Buttons */}
          <div className="flex items-center justify-center gap-[10px] w-full">
            <button
              onClick={onClose}
              className="flex-1 py-3 text-[14px] font-medium text-[#5F6F84] bg-white border border-[#E5E7EB] rounded-[8px] hover:bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3F73F6] focus:ring-offset-2 transition-colors"
              disabled={isLoading}
            >
              No
            </button>
            <button
              onClick={onConfirm}
              disabled={isLoading}
              className="flex-1 py-3 text-[14px] font-medium text-white bg-[#DC2626] border border-transparent rounded-[8px] hover:bg-[#B91C1C] focus:outline-none focus:ring-2 focus:ring-[#DC2626] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Deleting...' : 'Yes, delete'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return typeof window !== 'undefined' ? createPortal(modalElement, document.body) : null;
};

export default DeleteViewModal;
