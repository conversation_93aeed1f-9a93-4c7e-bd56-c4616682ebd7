import React, { useEffect, useId, useMemo } from 'react';
import { Column } from './DataTable';
import { useColumnResizer } from '@/hooks/useColumnResizer';

interface ResizableDataTableProps {
  columns: Column[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any[];
  selectedIds?: string[];
  onSelectRow?: (id: string, selected: boolean) => void;
  onSelectAll?: (selected: boolean) => void;
  isAllSelected?: boolean;
  className?: string;
  idField?: string;
  rowClassName?: string;
  isLoading?: boolean;
  showHeader?: boolean;
  showCheckbox?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  lastItemRef?: (node: any) => void;
  minColumnWidth?: number;
  enableResize?: boolean;
  resizerOptions?: {
    liveDrag?: boolean;
    draggingClass?: string;
    gripInnerHtml?: string;
    minWidth?: number;
    headerOnly?: boolean;
    hoverCursor?: string;
    dragCursor?: string;
    postbackSafe?: boolean;
    flush?: boolean;
    marginLeft?: string;
    marginRight?: string;
    disable?: boolean;
    partialRefresh?: boolean;
    removePadding?: boolean;
  };
}

const ResizableDataTable: React.FC<ResizableDataTableProps> = ({
  columns,
  data,
  selectedIds = [],
  onSelectRow,
  onSelectAll,
  isAllSelected = false,
  className = '',
  idField = 'id',
  rowClassName = '',
  isLoading = false,
  showHeader = true,
  showCheckbox = true,
  lastItemRef,
  minColumnWidth = 80,
  enableResize = true,
  resizerOptions = {},
}) => {
  const tableId = useId();
  const uniqueTableId = `resizable-table-${tableId}`;

  const defaultResizerOptions = useMemo(() => ({
    liveDrag: true,
    draggingClass: 'column-dragging',
    gripInnerHtml: '<div class="column-grip"></div>',
    minWidth: minColumnWidth,
    headerOnly: true,
    hoverCursor: 'col-resize',
    dragCursor: 'col-resize',
    postbackSafe: true,
    flush: true,
    disable: !enableResize,
    ...resizerOptions,
  }), [minColumnWidth, enableResize, resizerOptions]);

  // Initialize column resizer with custom options
  const { updateResizer, disableResizer, enableResizer } = useColumnResizer(
    uniqueTableId,
    defaultResizerOptions
  );

  // Update resizer when enableResize prop changes
  useEffect(() => {
    if (enableResize) {
      enableResizer();
    } else {
      disableResizer();
    }
  }, [enableResize, enableResizer, disableResizer]);

  // Update resizer options when they change
  useEffect(() => {
    updateResizer(defaultResizerOptions);
  }, [updateResizer, defaultResizerOptions]);

  return (
    <div className={`overflow-x-auto ${className}`}>
      <table 
        id={uniqueTableId}
        className={`min-w-full ${enableResize ? 'resizable-table' : ''}`}
        style={{ tableLayout: 'fixed' }}
      >
        {showHeader && (
          <thead className="bg-[#F3F5F9] h-[40px]">
            <tr>
              {onSelectRow && showCheckbox && (
                <th
                  scope="col"
                  className="px-[10px] py-[10px] text-left text-[14px] leading-[20px] font-medium text-[#5F6F84] bg-[#F3F5F9] uppercase tracking-wider whitespace-nowrap first:rounded-l-[12px]"
                  style={{ width: '50px', minWidth: 50 }}
                >
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    onChange={(e) => onSelectAll?.(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
              )}
              {columns.map((column, index) => {
                // Prefer explicit minWidth on column; otherwise approximate based on label length
                const label = column.header || '';
                const averageCharWidthPx = 8;
                const letterSpacingPerCharPx = 0.7;
                const horizontalPaddingPx = 24;
                const safetyBufferPx = 10;
                const computedMin = Math.max(
                  minColumnWidth,
                  column.minWidth ?? Math.ceil((label.length || 0) * (averageCharWidthPx + letterSpacingPerCharPx) + horizontalPaddingPx + safetyBufferPx)
                );
                return (
                  <th
                    key={column.id}
                    scope="col"
                    className={`px-[10px] py-[10px] text-left text-[14px] leading-[20px] font-medium text-[#5F6F84] bg-[#F3F5F9] uppercase tracking-wider whitespace-nowrap ${
                      index === columns.length - 1 ? 'rounded-r-[12px]' : ''
                    } ${column.className || ''}`}
                    style={{ 
                      minWidth: column.minWidth ?? computedMin,
                      maxWidth: column.width ? undefined : '1fr',
                      position: 'relative'
                    }}
                  >
                    {column.header}
                  </th>
                );
              })}
            </tr>
          </thead>
        )}

        <tbody className="bg-white divide-y divide-gray-200">
          {data.map((row, rowIndex) => (
            <tr
              key={row[idField]}
              className={`group h-[60px] ${rowClassName} hover:bg-blue-50`}
              ref={rowIndex === data.length - 1 ? lastItemRef : undefined}
            >
              {onSelectRow && showCheckbox && (
                <td className="px-[10px] py-[20px] whitespace-nowrap hover:text-[#2A2E34]">
                  <input
                    type="checkbox"
                    checked={selectedIds.includes(row[idField])}
                    onChange={(e) => onSelectRow(row[idField], e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
              )}

              {columns.map(column => (
                <td 
                  className="px-3 py-2 text-[#5F6F84] group-hover:text-[#2A2E34] overflow-hidden"
                  key={`${row[idField]}-${column.id}`}
                >
                  <div className="truncate">
                    {column.cell(row, rowIndex)}
                  </div>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      {isLoading && (
        <div className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#3F73F6]"></div>
        </div>
      )}
    </div>
  );
};

export default ResizableDataTable;