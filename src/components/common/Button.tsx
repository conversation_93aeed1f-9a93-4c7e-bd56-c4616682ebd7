import React, { ReactNode } from 'react';
import { COLORS } from '@/constants/theme';

interface ButtonProps {
  children: ReactNode;
  onClick?: () => void;
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline';
  icon?: ReactNode;
  width?: string;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  testId?: string;
}

/**
 * Reusable button component
 */
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(({
  children,
  onClick,
  className = '',
  variant = 'primary',
  icon,
  width = 'w-auto',
  disabled = false,
  type = 'button',
  testId,
}, ref) => {
  const getVariantClasses = () => {
    if (disabled) {
      // Disabled state: gray background, gray text, gray border
      return 'text-gray-400 border border-gray-200';
    }

    switch (variant) {
      case 'primary':
        return `bg-[${COLORS.PRIMARY}] text-white border border-[${COLORS.PRIMARY}] hover:bg-[${COLORS.PRIMARY}]/90`;
      case 'secondary':
        return `bg-white border font-medium border-[${COLORS.BORDER}] text-[${COLORS.TEXT_SECONDARY}] hover:bg-gray-50`;
      case 'outline':
        return `bg-white border font-medium border-[${COLORS.PRIMARY}] text-[${COLORS.PRIMARY}] hover:bg-[${COLORS.PRIMARY}]/5`;
      default:
        return '';
    }
  };

  return (
    <button
      ref={ref}
      type={type}
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      data-testid={testId}
      className={`${width} h-[40px] px-[16px] py-[10px] rounded-[12px] flex items-center justify-between gap-1 transition-all duration-200 ${getVariantClasses()} ${
        disabled ? 'cursor-not-allowed' : 'cursor-pointer'
      } ${className}`}
    >
      <span className={disabled ? 'text-gray-400' : ''}>{children}</span>
      {icon && <div className={disabled ? 'opacity-40' : ''}>{icon}</div>}
    </button>
  );
});

Button.displayName = 'Button';

export default Button;
