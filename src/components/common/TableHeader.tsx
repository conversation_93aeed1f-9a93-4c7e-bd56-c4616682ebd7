import React from 'react';

export interface TableHeaderProps {
  label: string;
  sortable?: boolean;
  className?: string;
  minWidth?: number;
  sortField?: string;
  columnId?: string;
  currentSortBy?: string;
  currentSortOrder?: 'asc' | 'desc';
  onSort?: (field: string, order: 'asc' | 'desc', columnId?: string) => void;
}

const TableHeader: React.FC<TableHeaderProps> = ({
  label,
  sortable = false,
  className = '',
  minWidth,
  sortField = '',
  columnId,
  currentSortBy,
  currentSortOrder,
  onSort,
}) => {
  const handleSort = () => {
    if (!sortable || !onSort || !sortField) return;

    // If this column is already sorted, toggle the order
    if (currentSortBy === (columnId || sortField)) {
      const newOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';
      onSort(sortField, newOrder, columnId);
    } else {
      // If not sorted, start with ascending
      onSort(sortField, 'asc', columnId);
    }
  };

  const isActiveSort = currentSortBy === (columnId || sortField);

  return (
    <th
      scope="col"
      // className={`px-3 py-3 text-left text-[14px] leading-[20px] font-medium text-[#5F6F84] bg-[#F3F5F9] uppercase tracking-wider whitespace-nowrap ${className}`}
      // style={{ minWidth }}
      className={`px-[10px] py-[10px] text-left text-[14px] leading-[20px] font-medium text-[#5F6F84] bg-[#F3F5F9] uppercase tracking-wider whitespace-nowrap border-0 ${
        sortable ? 'cursor-pointer hover:bg-[#E8EDF7] select-none' : ''
      } ${className}`}
      style={{ minWidth }}
      onClick={handleSort}
    >
      <div className="flex items-center gap-2">
        <span>{label}</span>
        {sortable && (
          <div className="flex flex-col">
            {/* Up arrow */}
            {/* <svg 
              className={`w-5 h-5 ${
                isActiveSort && currentSortOrder === 'asc' 
                  ? 'text-[#3F73F6]' 
                  : 'text-[#C0C4CC]'
              }`}
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M7 14l5-5 5 5z"/>
            </svg> */}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="12"
              viewBox="0 0 12 12"
              fill="none"
              className={`w-[10px] h-[10px] ${
                isActiveSort && currentSortOrder === 'asc' ? 'text-[#3F73F6]' : 'text-[#5F6F84]'
              }`}
            >
              <path
                d="M1 8L6 3L11 8"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            {/* Down arrow */}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="12"
              viewBox="0 0 12 12"
              fill="none"
              className={`w-[10px] h-[10px] ${
                isActiveSort && currentSortOrder === 'desc' ? 'text-[#3F73F6]' : 'text-[#5F6F84]'
              }`}
            >
              <path
                d="M1 4L6 9L11 4"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        )}
      </div>
    </th>
  );
};

export default TableHeader;
