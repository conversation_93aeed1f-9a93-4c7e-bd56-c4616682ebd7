import React, { useState } from 'react';
// import { X } from 'lucide-react';
import { createPortal } from 'react-dom';
import { Poppins } from 'next/font/google';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

interface SaveViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (viewName: string) => void;
  isLoading?: boolean;
  defaultName?: string;
}

const SaveViewModal: React.FC<SaveViewModalProps> = ({
  isOpen,
  onClose,
  onSave,
  isLoading = false,
  defaultName = '',
}) => {
  const [viewName, setViewName] = useState(defaultName);
  const [error, setError] = useState('');

  const handleSave = () => {
    if (!viewName.trim()) {
      setError('View name is required');
      return;
    }

    if (viewName.trim().length < 2) {
      setError('View name must be at least 2 characters');
      return;
    }

    setError('');
    onSave(viewName.trim());
  };

  const handleCancel = () => {
    setViewName(defaultName);
    setError('');
    onClose();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleCancel();
    }
  };

  if (!isOpen) return null;

  const modalContent = (
    <div
      className="fixed inset-0 flex items-center justify-center z-[9999]"
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.4)' }}
      onClick={handleBackdropClick}
    >
      <div
        className={`bg-white rounded-[20px] relative ${poppins.className}`}
        style={{
          width: '540px',
          height: '248px',
          boxShadow: '0px 8px 32px 0px #0000001A',
        }}
        onClick={e => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-[30px]">
          <h2 className="text-[18px] font-medium text-[#2A2E34] leading-[24px]">Save view</h2>
          <button
            onClick={handleCancel}
            className="w-[30px] h-[30px] flex items-center justify-center text-[#5F6F84] hover:text-[#2A2E34] bg-[#F3F5F9] rounded-[8px] transition-all duration-200"
            disabled={isLoading}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </button>
        </div>

        {/* Body */}
        <div className="px-[30px] pb-[30px]">
          <div className="mb-[30px]">
            <label htmlFor="viewName" className="block text-[14px] leading-[20px] font-medium text-[#2A2E34] mb-1">
              Name
            </label>
            <input
              id="viewName"
              type="text"
              value={viewName}
              onChange={e => {
                setViewName(e.target.value);
                if (error) setError('');
              }}
              onKeyPress={handleKeyPress}
              placeholder="Name your view"
              className={`w-full h-[40px] px-4 py-[10px] border rounded-[12px] text-[14px] leading-[20px] font-regular text-[#2A2E34] placeholder-[#5F6F84] focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)] transition-colors ${error ? 'border-[#EF4444]' : 'border-[#E5E7EB]'
                }`}
              style={{
                backgroundColor: 'white !important',
                boxShadow: 'inset 0 0 0 1000px white !important'
              }}
              disabled={isLoading}
              autoFocus
            />
            {error && <p className="mt-2 text-[12px] text-[#EF4444]">{error}</p>}
          </div>

          {/* Footer Buttons */}
          <div className="flex items-center justify-between">
            <button
              onClick={handleCancel}
              className="px-[16px] py-[10px] h-[40px] text-[14px] leading-[20px] font-medium text-[#3F73F6] bg-white border border-[#3F73F6] rounded-[12px] hover:bg-[#305ED2] hover:text-white focus:outline-none focus:ring-2 focus:ring-[#3F73F6] focus:ring-offset-2 transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={isLoading || !viewName.trim()}
              className="px-[16px] py-[10px] h-[40px] text-[14px] leading-[20px] font-medium  text-white bg-[#3F73F6] hover:bg-[#305ED2] border border-transparent rounded-[12px] hover:bg-[#2563EB] focus:outline-none focus:ring-2 focus:ring-[#3F73F6] focus:ring-offset-2 disabled:bg-[#F3F5F9] disabled:border-[#DCE2EB] disabled:text-[#A2AFC2] disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Saving...' : 'Save'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Use portal to render modal at document body to avoid z-index issues
  if (typeof window !== 'undefined') {
    return createPortal(modalContent, document.body);
  }

  return null;
};

export default SaveViewModal;
