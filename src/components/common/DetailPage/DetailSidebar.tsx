import React from 'react';

interface NavItem {
  id: string;
  title: string;
  icon?: string;
}

interface DetailSidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  navItems?: NavItem[];
  className?: string;
}

const defaultNavItems: NavItem[] = [
  { id: 'about', title: 'About' },
  { id: 'matters', title: 'Matters' },
  { id: 'events', title: 'Events' },
  { id: 'notes', title: 'Notes' },
  { id: 'workflows', title: 'Workflows' },
  { id: 'files', title: 'Files' },
  { id: 'communication', title: 'Communication' },
  { id: 'invoices', title: 'Invoices' },
  { id: 'payments', title: 'Payments' },
  { id: 'time-entries', title: 'Time Entries' },
  { id: 'expenses', title: 'Expenses' },
];

const DetailSidebar: React.FC<DetailSidebarProps> = ({ 
  activeTab, 
  onTabChange,
  navItems = defaultNavItems,
  className = ''
}) => {
  return (
    <aside className={`w-[260px] p-[22px] border-r border-[#E5E9EB] h-full ${className}`}>
      <div className="flex flex-col space-y-2">
        {navItems.map((item) => (
          <button
            key={item.id}
            onClick={() => onTabChange(item.id)}
            className={`text-left p-[8px] text-[14px] leading-[20px] font-medium transition-colors duration-200 rounded-[12px] ${
              activeTab === item.id
                ? 'bg-[#F3F5F9] text-[#3F73F6]'
                : 'text-[#5F6F84] hover:bg-[#F3F5F9] hover:text-[#2A2E34]'
            }`}
            aria-label={`Navigate to ${item.title}`}
          >
            {item.title}
          </button>
        ))}
      </div>
    </aside>
  );
};

export default DetailSidebar; 