import React, { useEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import TableActionButton from './TableActionButton';
import Image from 'next/image';
import { convertStatusArrayToApiFormat } from '@/types/courtNotice';
import { ChevronDown, Plus, X } from 'lucide-react';
import CustomCheckbox from './CustomCheckbox';
import DateTimeModal from './DateTimeModal';
import workflowService from '@/services/api/workflowService';
import apiClient from '@/services/api/config';
import courtNoticeService from '@/services/api/courtNoticeService';

type Operator =
  | 'contains'
  | 'equals'
  | 'not_contains'
  | 'not_equals'
  | 'unassigned'
  | 'date_range'
  | 'before'
  | 'after'
  | 'between';
interface FilterRow {
  id: string;
  field: string;
  operator: Operator | '';
  value: string;
  selectedValues?: string[]; // For multi-select fields like assignee
}

// Fallback options (used only if API to fetch dynamic field options fails)
const DEFAULT_FIELD_OPTIONS = [
  'Workflow Name',
  'Assignee',
  'Status',
  'Contact',
  'Matter',
  'Templates',
  'Due date',
  'Create date',
  'Attorney',
];

const API_URL = process.env.NEXT_PUBLIC_BASE_URL;

interface TableActionsProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  onFilter?: () => void;
  onColumns?: (selected?: string[]) => void;
  onSaveView?: () => void;
  showFilter?: boolean;
  showColumns?: boolean;
  showSaveView?: boolean;
  disableSaveView?: boolean;
  className?: string;
  onApplyFilters?: (filters: FilterRow[]) => void;
  onFilteredDataReceived?: (data: unknown) => void;
  visibleColumns?: string[]; // Array of visible column names
  savedFilters?: Array<{
    fieldName: string;
    filter: string;
    value: string[];
  }>; // Array of saved filter configurations
  onFiltersChange?: (filters: FilterRow[]) => void; // Callback when filters change
}

interface ColumnField {
  name: string;
  isShowing: boolean;
}
export const TABLE_FIELD_NAME: ColumnField[] = [
  { name: 'Workflow Name', isShowing: false },
  { name: 'View', isShowing: false },
  { name: 'Started', isShowing: false },
  { name: 'Due On', isShowing: false },
  { name: 'Task Completed', isShowing: false },
  { name: 'Status', isShowing: false },
  { name: 'Activity', isShowing: false },
  { name: 'Assignee', isShowing: false },
  { name: 'Matter', isShowing: false },
  { name: 'Attorney', isShowing: false },
];

const TableActions: React.FC<TableActionsProps> = ({
  onFilter,
  onColumns,
  onSaveView,
  showFilter = true,
  showColumns = true,
  showSaveView = true,
  disableSaveView = false,
  className = '',
  onApplyFilters,
  onFilteredDataReceived,
  visibleColumns,
  savedFilters,
  onFiltersChange,
}) => {
  const [isColumnsOpen, setIsColumnsOpen] = useState(false);
  const [columnsSearch, setColumnsSearch] = useState('');
  const [columnsState, setColumnsState] = useState<ColumnField[]>(() => {
    if (visibleColumns) {
      // Standard logic: visible columns should be checked (isShowing: true)
      return TABLE_FIELD_NAME.map(f => ({
        ...f,
        isShowing: visibleColumns.includes(f.name),
      }));
    }
    // Default state when no visibleColumns provided - show typical default columns
    return TABLE_FIELD_NAME.map(f => ({
      ...f,
      isShowing: [
        'Workflow Name',
        'View',
        'Started',
        'Due On',
        'Task Completed',
        'Status',
        'Activity',
        'Assignee',
      ].includes(f.name),
    }));
  });

  const [appliedColumns, setAppliedColumns] = useState<ColumnField[]>(() => {
    if (visibleColumns) {
      // Standard logic: visible columns should be checked (isShowing: true)
      return TABLE_FIELD_NAME.map(f => ({
        ...f,
        isShowing: visibleColumns.includes(f.name),
      }));
    }
    // Default state when no visibleColumns provided - show typical default columns
    return TABLE_FIELD_NAME.map(f => ({
      ...f,
      isShowing: [
        'Workflow Name',
        'View',
        'Started',
        'Due On',
        'Task Completed',
        'Status',
        'Activity',
        'Assignee',
      ].includes(f.name),
    }));
  });

  // Update states when visibleColumns prop changes
  React.useEffect(() => {
    if (visibleColumns) {
      const newColumnsState = TABLE_FIELD_NAME.map(f => ({
        ...f,
        isShowing: visibleColumns.includes(f.name),
      }));
      setColumnsState(newColumnsState);
      setAppliedColumns(newColumnsState);
    } else {
      // Reset to default state when visibleColumns is undefined
      const defaultColumnsState = TABLE_FIELD_NAME.map(f => ({
        ...f,
        isShowing: [
          'Workflow Name',
          'View',
          'Started',
          'Due On',
          'Task Completed',
          'Status',
          'Activity',
          'Assignee',
        ].includes(f.name),
      }));
      setColumnsState(defaultColumnsState);
      setAppliedColumns(defaultColumnsState);
    }
  }, [visibleColumns]);

  // Update filter states when savedFilters prop changes
  React.useEffect(() => {
    if (savedFilters && savedFilters.length > 0) {
      // Convert saved filters to FilterRow format
      const convertedFilters = savedFilters.map((savedFilter, _index) => {
        const fieldMap: Record<string, string> = {
          template_name: 'Workflow Name',
          assigned_users: 'Assignee',
          status: 'Status',
          contact: 'Contact',
          matter: 'Matter',
          templates: 'Templates',
          due_date: 'Due date',
          created_date: 'Create date',
          attorney: 'Attorney',
        };

        const operatorMap: Record<string, Operator> = {
          contains: 'contains',
          equal: 'equals',
          not_contains: 'not_contains',
          not_equal: 'not_equals',
          unassigned: 'unassigned',
          before: 'before',
          after: 'after',
          between: 'between',
        };

        const fieldName = fieldMap[savedFilter.fieldName] || savedFilter.fieldName;
        const operator = (operatorMap[savedFilter.filter] || savedFilter.filter) as Operator;

        // Handle date range values specially for 'between' operator
        let value: string;
        if (operator === 'between' && savedFilter.value.length === 2) {
          // For date ranges, use pipe-separated format
          value = savedFilter.value.join('|');
        } else {
          // For other filters, use comma-separated format
          value = savedFilter.value.join(', ');
        }

        return {
          id: `saved-${_index}`,
          field: fieldName,
          operator: operator,
          value: value,
          selectedValues: savedFilter.value,
        };
      });

      setFilters(convertedFilters);
      setAppliedFilters(convertedFilters);
    } else {
      // Reset to default state when savedFilters is undefined or empty
      const defaultFilters: FilterRow[] = [
        {
          id: 'default',
          field: 'Workflow Name',
          operator: '' as const,
          value: '',
          selectedValues: [],
        },
      ];
      setFilters(defaultFilters);
      setAppliedFilters(defaultFilters);
    }
  }, [savedFilters]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const filterRef = useRef<HTMLDivElement>(null);
  const fieldDropdownRef = useRef<HTMLDivElement>(null);
  const operatorDropdownRef = useRef<HTMLDivElement>(null);
  const statusDropdownRef = useRef<HTMLDivElement>(null);
  const valueDropdownRef = useRef<HTMLDivElement>(null);

  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filterButtonPosition, setFilterButtonPosition] = useState({ top: 0, left: 0 });
  const [fieldDropdownPosition, setFieldDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
    openAbove: false,
  });
  const [operatorDropdownPosition, setOperatorDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
    openAbove: false,
  });
  const [valueDropdownPosition, setValueDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
    openAbove: false,
  });
  const [statusDropdownPosition, setStatusDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
    openAbove: false,
  });
  const filterButtonRef = useRef<HTMLButtonElement>(null);

  // Calculate filter button position when filter opens or screen size changes
  useEffect(() => {
    const calculatePosition = () => {
      if (isFilterOpen && filterButtonRef.current) {
        const rect = filterButtonRef.current.getBoundingClientRect();
        const isLargeScreen = window.innerWidth >= 1440;
        setFilterButtonPosition({
          top: rect.bottom + 4, // 4px below the button
          left: isLargeScreen
            ? rect.right - 724 // Align dropdown right edge with button right edge (724px is dropdown width)
            : rect.right - 724 + 120, // Shift 80px to the right on smaller screens
        });
      }
    };

    // Calculate position when filter opens
    calculatePosition();

    // Add resize listener to recalculate on screen size change
    window.addEventListener('resize', calculatePosition);

    // Cleanup
    return () => {
      window.removeEventListener('resize', calculatePosition);
    };
  }, [isFilterOpen]);

  const [filters, setFilters] = useState<FilterRow[]>(() => {
    if (savedFilters && savedFilters.length > 0) {
      // Convert saved filters to FilterRow format
      return savedFilters.map((savedFilter, _index) => {
        // Map backend field names to frontend field names
        const fieldMap: Record<string, string> = {
          template_name: 'Workflow Name',
          assigned_users: 'Assignee',
          status: 'Status',
          contact: 'Contact',
          matter: 'Matter',
          templates: 'Templates',
          due_date: 'Due date',
          created_date: 'Create date',
          attorney: 'Attorney',
        };

        // Map backend operators to frontend operators
        const operatorMap: Record<string, Operator> = {
          contains: 'contains',
          equal: 'equals',
          not_contains: 'not_contains',
          not_equal: 'not_equals',
          unassigned: 'unassigned',
          before: 'before',
          after: 'after',
          between: 'between',
        };

        const fieldName = fieldMap[savedFilter.fieldName] || savedFilter.fieldName;
        const operator = (operatorMap[savedFilter.filter] || savedFilter.filter) as Operator;

        return {
          id: `saved-${_index}`,
          field: fieldName,
          operator: operator,
          value: savedFilter.value.join(', '), // Join array values for display
          selectedValues: savedFilter.value,
        };
      });
    }
    return [
      {
        id: 'default',
        field: 'Workflow Name',
        operator: '' as const,
        value: '',
        selectedValues: [],
      },
    ];
  });

  const [appliedFilters, setAppliedFilters] = useState<FilterRow[]>(() => {
    if (savedFilters && savedFilters.length > 0) {
      // Convert saved filters to FilterRow format - same logic as above
      return savedFilters.map((savedFilter, _index) => {
        const fieldMap: Record<string, string> = {
          template_name: 'Workflow Name',
          assigned_users: 'Assignee',
          status: 'Status',
          contact: 'Contact',
          matter: 'Matter',
          templates: 'Templates',
          due_date: 'Due date',
          created_date: 'Create date',
          attorney: 'Attorney',
        };

        const operatorMap: Record<string, Operator> = {
          contains: 'contains',
          equal: 'equals',
          not_contains: 'not_contains',
          not_equal: 'not_equals',
          unassigned: 'unassigned',
          before: 'before',
          after: 'after',
          between: 'between',
        };

        const fieldName = fieldMap[savedFilter.fieldName] || savedFilter.fieldName;
        const operator = (operatorMap[savedFilter.filter] || savedFilter.filter) as Operator;

        return {
          id: `saved-${_index}`,
          field: fieldName,
          operator: operator,
          value: savedFilter.value.join(', '),
          selectedValues: savedFilter.value,
        };
      });
    }
    return [
      {
        id: 'default',
        field: 'Workflow Name',
        operator: '' as const,
        value: '',
        selectedValues: [],
      },
    ];
  });
  const [fieldOptions, setFieldOptions] = useState<string[]>(DEFAULT_FIELD_OPTIONS);
  const [fieldValueOptions, setFieldValueOptions] = useState<Record<string, string[]>>({});
  const [openValueDropdownId, setOpenValueDropdownId] = useState<string | null>(null);
  const [openFieldDropdownId, setOpenFieldDropdownId] = useState<string | null>(null);
  const [openOperatorDropdownId, setOpenOperatorDropdownId] = useState<string | null>(null);
  const [openStatusDropdownId, setOpenStatusDropdownId] = useState<string | null>(null);
  const [filterSearchValues, setFilterSearchValues] = useState<Record<string, string>>({});
  const [isDateModalOpen, setIsDateModalOpen] = useState(false);
  const [activeDateFilterId, setActiveDateFilterId] = useState<string | null>(null);
  const [dateModalPosition, setDateModalPosition] = useState<
    { top: number; left: number; openAbove?: boolean } | undefined
  >(undefined);
  const [pendingFiltersUpdate, setPendingFiltersUpdate] = useState<FilterRow[] | null>(null);

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (dropdownRef.current && !dropdownRef.current.contains(target)) {
        setIsColumnsOpen(false);
      }

      // Check if click is inside any portal dropdown
      const isInsidePortalDropdown =
        target instanceof Element &&
        (target.closest('[data-portal="field-dropdown"]') ||
          target.closest('[data-portal="operator-dropdown"]') ||
          target.closest('[data-portal="value-dropdown"]') ||
          target.closest('[data-portal="status-dropdown"]'));

      if (
        filterRef.current &&
        !filterRef.current.contains(target) &&
        !isDateModalOpen &&
        !isInsidePortalDropdown
      ) {
        setIsFilterOpen(false);
        // Close all filter dropdowns when filter panel closes
        setOpenFieldDropdownId(null);
        setOpenOperatorDropdownId(null);
        setOpenValueDropdownId(null);
        setOpenStatusDropdownId(null);
      }
    };
    if (isColumnsOpen || isFilterOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isColumnsOpen, isFilterOpen, isDateModalOpen]);

  // Handle clicking outside of individual dropdowns
  React.useEffect(() => {
    const handleDropdownClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // Don't close dropdowns if DateTimeModal is open
      if (isDateModalOpen) {
        return;
      }

      // Check if click is inside any portal dropdown
      const isInsidePortalDropdown =
        target instanceof Element &&
        (target.closest('[data-portal="field-dropdown"]') ||
          target.closest('[data-portal="operator-dropdown"]') ||
          target.closest('[data-portal="value-dropdown"]') ||
          target.closest('[data-portal="status-dropdown"]') ||
          target.closest('[data-portal="matter-dropdown"]') ||
          target.closest('[data-portal="template-dropdown"]'));

      // Check if click is inside any dropdown button
      const isInsideDropdownButton =
        target instanceof Element &&
        (target.closest('[data-dropdown="field"]') ||
          target.closest('[data-dropdown="operator"]') ||
          target.closest('[data-dropdown="status"]') ||
          target.closest('[data-dropdown="value"]'));

      // Only close dropdowns if click is outside both the filter container and portal dropdowns
      if (
        filterRef.current &&
        !filterRef.current.contains(target) &&
        !isInsidePortalDropdown &&
        !isInsideDropdownButton
      ) {
        // Close all dropdowns if click is outside the filter container and not inside any dropdown
        setOpenFieldDropdownId(null);
        setOpenOperatorDropdownId(null);
        setOpenStatusDropdownId(null);
        setOpenValueDropdownId(null);
        setFilterSearchValues({});
      }
    };

    // Add event listener when any dropdown is open
    if (
      openFieldDropdownId ||
      openOperatorDropdownId ||
      openStatusDropdownId ||
      openValueDropdownId
    ) {
      document.addEventListener('mousedown', handleDropdownClickOutside);
    }

    return () => document.removeEventListener('mousedown', handleDropdownClickOutside);
  }, [
    openFieldDropdownId,
    openOperatorDropdownId,
    openStatusDropdownId,
    openValueDropdownId,
    isDateModalOpen,
  ]);

  // Fetch dynamic filter field options from API (record keys)
  React.useEffect(() => {
    const controller = new AbortController();
    const fetchFieldOptions = async () => {
      try {
        // Use optimized service method with caching and cancellation
        const sorted = await courtNoticeService.getFieldOptions();

        if (sorted.length) {
          setFieldOptions(sorted);
          // If current selected field isn't part of new options, reset to first
          setFilters(prev => {
            const current = prev[0]?.field;
            if (!sorted.includes(current)) {
              const firstKey = sorted[0];
              return [
                {
                  id: `${Date.now()}`,
                  field: firstKey,
                  operator: '',
                  value: '',
                  selectedValues: [],
                },
              ];
            }
            return prev;
          });
        }
      } catch (e) {
        // Only log error if it's not an abort error
        if (e instanceof Error && e.name !== 'AbortError') {
          // eslint-disable-next-line no-console
          console.warn('Using default filter options due to API error', e);
        }
      }
    };
    fetchFieldOptions();
    return () => {
      controller.abort();
      // Also cancel any pending requests in the service
      courtNoticeService.cancelAllRequests();
    };
  }, []);

  const isDateField = (field: string) => {
    const f = (field || '').toLowerCase();
    return (
      f.includes('date') ||
      f === 'start_date' ||
      f === 'end_date' ||
      f === 'due_on' ||
      f === 'due date' ||
      f === 'create date' ||
      f === 'created_at'
    );
  };

  const ensureOperatorForField = (row: FilterRow, nextField: string): FilterRow => {
    if (isDateField(nextField)) {
      return { ...row, operator: '', value: '' };
    }

    // Reset operator to empty so "Select" is displayed by default
    return {
      ...row,
      operator: '',
      value: '', // Clear the value when field changes
      selectedValues: [], // Clear selected values when field changes
    };
  };

  const isStatusField = (field: string) => (field || '').toLowerCase() === 'status';
  const isContactField = (field: string) => (field || '').toLowerCase() === 'contact';
  const isAttorneyField = (field: string) => (field || '').toLowerCase() === 'attorney';
  const isAssigneeField = (field: string) => {
    const f = (field || '').toLowerCase();
    return f === 'assignee' || f === 'assign' || f === 'assigned_users';
  };

  const isNameField = (field: string) => {
    const f = (field || '').toLowerCase();
    return f === 'name' || f === 'template_name' || f === 'workflow name';
  };

  // Helper function to get all selected values across all filters for a specific field type
  const getAllSelectedValuesForField = (fieldName: string): string[] => {
    const selectedValues: string[] = [];
    filters.forEach(filter => {
      // Check if this filter is for the same field type as the current field
      if (filter.field.toLowerCase() === fieldName.toLowerCase()) {
        if (filter.selectedValues && filter.selectedValues.length > 0) {
          selectedValues.push(...filter.selectedValues);
        }
      }
    });
    return [...new Set(selectedValues)]; // Remove duplicates
  };

  // Helper function to get all selected assignees across all filters (for backward compatibility)
  const getAllSelectedAssignees = (): string[] => {
    const selectedAssignees: string[] = [];
    filters.forEach(filter => {
      if (isAssigneeField(filter.field) || isNameField(filter.field)) {
        if (filter.selectedValues && filter.selectedValues.length > 0) {
          selectedAssignees.push(...filter.selectedValues);
        }
      }
    });
    return [...new Set(selectedAssignees)]; // Remove duplicates
  };

  const isMatterField = (field: string) => {
    const f = (field || '').toLowerCase();
    return f === 'matter' || f === 'matter_name' || f === 'matters';
  };

  // Helper function to extract matter name from full value (part after '|')
  const extractMatterName = (value: string): string => {
    if (typeof value === 'string' && value.includes('|')) {
      return value.split('|')[1]?.trim() || value;
    }
    return value;
  };

  // Helper function to get display name for matter field (finds full name from extracted matter name)
  const getMatterDisplayName = (extractedValue: string, fieldName: string): string => {
    if (!isMatterField(fieldName)) return extractedValue;

    // Look for the full value in fieldValueOptions that contains this extracted value
    const options = fieldValueOptions[fieldName] || [];
    const fullValue = options.find(opt => extractMatterName(opt) === extractedValue);
    return fullValue || extractedValue;
  };

  const isTemplatesField = (field: string) => {
    const f = (field || '').toLowerCase();
    return (
      f === 'templates' || f === 'template' || f === 'template_name' || f === 'work_flow_runner'
    );
  };

  // Define template options for the Templates field
  const getTemplateOptions = () => ['New Court Notice', 'Court Notice Follow Up'];

  const isUserField = (field: string) => {
    const f = (field || '').toLowerCase();
    return (
      f === 'assign_by' ||
      f === 'assigned_users' ||
      f === 'assignee' ||
      f === 'user' ||
      f === 'users'
    );
  };

  const operatorOptionsForField = (
    field: string
  ): Array<{ value: Operator | ''; label: string; disabled?: boolean }> => {
    if (isDateField(field)) {
      return [
        { value: 'before', label: 'Before' },
        { value: 'after', label: 'After' },
        { value: 'between', label: 'Between' },
      ];
    }
    if (
      isStatusField(field) ||
      isContactField(field) ||
      isMatterField(field) ||
      isTemplatesField(field) ||
      isAttorneyField(field)
    ) {
      return [
        { value: 'contains', label: 'Contains' },
        { value: 'not_contains', label: 'Does not contain' },
      ];
    }
    if (isAssigneeField(field)) {
      return [
        { value: 'contains', label: 'Contains' },
        { value: 'not_contains', label: 'Does not contain' },
        { value: 'unassigned', label: 'Unassigned' },
      ];
    }
    if (isNameField(field)) {
      return [
        { value: 'contains', label: 'Contains' },
        { value: 'equals', label: 'Is equal' },
        { value: 'not_equals', label: 'Is not equal' },
        { value: 'not_contains', label: 'Does not contain' },
      ];
    }
    return [
      { value: 'contains', label: 'Contains' },
      { value: 'equals', label: 'Is equal' },
      { value: 'not_equals', label: 'Is not equal' },
      { value: 'not_contains', label: 'Does not contain' },
      // { value: 'unassigned', label: 'Unassigned' },
      // { value: 'date_range', label: 'Date range' },
    ];
  };

  // Helper function to get the display label for an operator value
  const getOperatorDisplayLabel = (field: string, operator: Operator | ''): string => {
    if (!operator) return 'Select';
    const options = operatorOptionsForField(field);
    const option = options.find(opt => opt.value === operator);
    return option ? option.label : operator;
  };

  const fetchDistinctValues = async (field: string): Promise<string[]> => {
    try {
      // Use optimized service method with caching
      return await courtNoticeService.getDistinctValues(field);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.warn('distinct fetch failed', err);
      return [];
    }
  };

  const fetchMatters = async (): Promise<string[]> => {
    try {
      const res = await apiClient.get(API_URL + '/workflow/matter-list');
      const json = await res.data;
      const matters: unknown[] = json?.data?.matters || [];

      // Extract matter names for the dropdown
      const matterNames = matters
        .map(matter => {
          if (typeof matter === 'object' && matter !== null && 'name' in matter) {
            const name = (matter as Record<string, unknown>).name;
            return typeof name === 'string' ? name : String(name);
          }
          return null;
        })
        .filter((name): name is string => typeof name === 'string');
      return Array.from(new Set(matterNames)).sort((a, b) => a.localeCompare(b));
    } catch (err) {
      // eslint-disable-next-line no-console
      console.warn('matter fetch failed', err);
      return [];
    }
  };

  const fetchUsers = async (): Promise<string[]> => {
    try {
      const res = await apiClient.get(
        API_URL + '/workflow/user-list?search=&user_group_id=6877420fd4928f6a37ba1b95'
      );
      const json = await res.data;
      const users: unknown[] = json?.data?.users || json?.users || [];

      // Extract user names for the dropdown (assuming users have name or full_name field)
      const userNames = users
        .map(user => {
          if (typeof user === 'object' && user !== null && 'value' in user) {
            const value = (user as Record<string, unknown>).value;
            return typeof value === 'string' ? value : String(value);
          }
          return null;
        })
        .filter((value): value is string => typeof value === 'string');
      return Array.from(new Set(userNames)).sort((a, b) => a.localeCompare(b));
    } catch (err) {
      // eslint-disable-next-line no-console
      console.warn('user fetch failed', err);
      return [];
    }
  };

  const fetchNames = async (): Promise<string[]> => {
    try {
      // Get userId from localStorage
      const user = localStorage.getItem('user');
      const userData = user ? JSON.parse(user) : null;
      const userId = userData?.id || 1; // fallback to 1 if not found

      // Use the workflow service to get unique names
      const names = await workflowService.getUniqueNames('new_court_notice', userId);

      return names;
    } catch (err) {
      console.error('💥 Error in fetchNames:', err);
      // Fallback to empty array if API fails
      return [];
    }
  };

  const fetchAttorneys = async (): Promise<string[]> => {
    try {
      // Get userId from localStorage
      const user = localStorage.getItem('user');
      const userData = user ? JSON.parse(user) : null;
      const userId = userData?.id || 1; // fallback to 1 if not found

      // Use the workflow service to get unique attorneys
      const attorneys = await workflowService.getUniqueAttorneys('new_court_notice', userId);

      return attorneys;
    } catch (err) {
      console.error('💥 Error in fetchAttorneys:', err);
      // Fallback to empty array if API fails
      return [];
    }
  };

  const fetchContacts = async (): Promise<string[]> => {
    try {
      const user = localStorage.getItem('user');
      const userData = user ? JSON.parse(user) : null;
      const userId = userData?.id || 1;

      // Use the workflow service to get unique clients (contacts)
      const contacts = await workflowService.getUniqueClients('new_court_notice', userId);

      return contacts;
    } catch (err) {
      console.error('💥 Error in fetchContacts:', err);
      // Fallback to empty array if API fails
      return [];
    }
  };

  const handleFilterChange = (id: string, key: keyof FilterRow, value: string) => {
    setFilters(prev => {
      const updated = prev.map(f => {
        if (f.id !== id) return f as FilterRow;

        // Handle field changes
        if (key === 'field') {
          // Clear search input when field changes
          // Also clear search input if the field is different from current field
          if (f.field !== value) {
            setFilterSearchValues(prev => ({ ...prev, [id]: '' }));
          }

          const next = ensureOperatorForField(
            {
              ...f,
              [key]: value,
            } as FilterRow,
            value
          );
          if (isTemplatesField(value)) {
            // Set template options immediately (synchronously)
            setFieldValueOptions(prevMap => ({
              ...prevMap,
              [value]: getTemplateOptions(),
            }));
          } else {
            // prefetch options when field changes
            (async () => {
              let opts: string[] = [];
              if (isMatterField(value)) {
                opts = await fetchMatters();
              } else if (isUserField(value)) {
                opts = await fetchUsers();
              } else if (isNameField(value)) {
                opts = await fetchNames();
              } else if (isAttorneyField(value)) {
                opts = await fetchAttorneys();
              } else if (isContactField(value)) {
                opts = await fetchContacts();
              } else {
                opts = await fetchDistinctValues(value);
              }
              setFieldValueOptions(prevMap => {
                const newMap = { ...prevMap, [value]: opts };
                return newMap;
              });
            })();
          }
          return next;
        }

        // Handle operator changes
        if (key === 'operator') {
          // Clear value and selectedValues when operator changes
          // This ensures the input box is empty when switching between operators

          // Clear search input when operator changes to any new value
          if (f.operator !== value) {
            setFilterSearchValues(prev => ({ ...prev, [id]: '' }));
          }

          return {
            ...f,
            [key]: value,
            value: '',
            selectedValues: [],
          } as FilterRow;
        }

        // Handle other changes (value, selectedValues, etc.)

        // Special handling for value updates - also update selectedValues
        if (key === 'value') {
          const updatedFilter = {
            ...f,
            value: value,
            selectedValues: [value], // Update selectedValues to match value
          } as FilterRow;
          return updatedFilter;
        }

        const updatedFilter = { ...f, [key]: value } as FilterRow;
        return updatedFilter;
      });

      return updated;
    });
  };

  const handleAssigneeSelection = (id: string, selectedValue: string) => {
    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f;
        const currentSelected = f.selectedValues || [];
        const isSelected = currentSelected.includes(selectedValue);
        const newSelected = isSelected
          ? currentSelected.filter(v => v !== selectedValue)
          : [...currentSelected, selectedValue];

        return {
          ...f,
          selectedValues: newSelected,
          value: newSelected.join(', '), // Keep value as comma-separated for display
        };
      })
    );
  };

  const handleStatusSelection = (id: string, selectedValue: string) => {
    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f;
        const currentSelected = f.selectedValues || [];
        const isSelected = currentSelected.includes(selectedValue);
        const newSelected = isSelected
          ? currentSelected.filter(v => v !== selectedValue)
          : [...currentSelected, selectedValue];

        return {
          ...f,
          selectedValues: newSelected,
          value: newSelected.join(', '), // Keep value as comma-separated for display
        };
      })
    );
  };

  const handleMatterSelection = (id: string, selectedValue: string) => {
    console.log('🔍 MATTER SELECTION DEBUG:', {
      filterId: id,
      selectedValue,
      timestamp: new Date().toISOString(),
    });

    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f;
        const currentSelected = f.selectedValues || [];

        // For matter fields, we store the full original value to maintain uniqueness
        // This prevents selecting multiple options with the same matter name but different clients
        const isSelected = currentSelected.includes(selectedValue);

        const newSelected = isSelected
          ? currentSelected.filter(v => v !== selectedValue)
          : [...currentSelected, selectedValue]; // Store the full original value

        // For display, show the extracted matter names
        const displayValue = newSelected.map(extractMatterName).join(', ');

        console.log('🔍 MATTER SELECTION RESULT:', {
          filterId: id,
          currentSelected,
          selectedValue,
          isSelected,
          newSelected,
          displayValue,
          extractedValues: newSelected.map(extractMatterName),
        });

        return {
          ...f,
          selectedValues: newSelected,
          value: displayValue, // Display extracted matter names
        };
      })
    );
  };

  const handleTemplatesSelection = (id: string, selectedValue: string) => {
    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f;
        const currentSelected = f.selectedValues || [];
        const isSelected = currentSelected.includes(selectedValue);

        // For templates, only allow single selection
        const newSelected = isSelected ? [] : [selectedValue];

        return {
          ...f,
          selectedValues: newSelected,
          value: newSelected.join(', '), // Keep value as comma-separated for display
        };
      })
    );
  };

  const handleUserSelection = (id: string, selectedValue: string) => {
    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f;
        const currentSelected = f.selectedValues || [];
        const isSelected = currentSelected.includes(selectedValue);
        const newSelected = isSelected
          ? currentSelected.filter(v => v !== selectedValue)
          : [...currentSelected, selectedValue];

        return {
          ...f,
          selectedValues: newSelected,
          value: newSelected.join(', '), // Keep value as comma-separated for display
        };
      })
    );
  };

  const handleAttorneySelection = (id: string, selectedValue: string) => {
    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f;
        const currentSelected = f.selectedValues || [];
        const isSelected = currentSelected.includes(selectedValue);
        const newSelected = isSelected
          ? currentSelected.filter(v => v !== selectedValue)
          : [...currentSelected, selectedValue];

        return {
          ...f,
          selectedValues: newSelected,
          value: newSelected.join(', '), // Keep value as comma-separated for display
        };
      })
    );
  };

  const handleContactSelection = (id: string, selectedValue: string) => {
    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f;
        const currentSelected = f.selectedValues || [];
        const isSelected = currentSelected.includes(selectedValue);
        const newSelected = isSelected
          ? currentSelected.filter(v => v !== selectedValue)
          : [...currentSelected, selectedValue];

        return {
          ...f,
          selectedValues: newSelected,
          value: newSelected.join(', '), // Keep value as comma-separated for display
        };
      })
    );
  };

  const handleDateModalOpen = (filterId: string, event?: React.MouseEvent) => {
    setActiveDateFilterId(filterId);
    setIsDateModalOpen(true);

    // Calculate position if event is provided
    if (event) {
      const rect = event.currentTarget.getBoundingClientRect();
      const modalHeight = 400; // Approximate modal height
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      const openAbove = spaceBelow < modalHeight && spaceAbove > modalHeight;

      setDateModalPosition({
        top: openAbove ? rect.top - 40 - 12 : rect.bottom + 4, // Position above with 40px + 8px gap or below with 8px gap
        left: rect.left,
        openAbove,
      });
    }
  };

  const handleDateModalClose = () => {
    setIsDateModalOpen(false);
    setActiveDateFilterId(null);
  };

  const handleDateSelect = (date: string, _time: string) => {
    if (activeDateFilterId) {
      // The DateTimeModal now handles the range logic internally and sends the properly formatted value
      handleFilterChange(activeDateFilterId, 'value', date);
    }
  };

  const filteredFields = useMemo(
    () => columnsState.filter(opt => opt.name.toLowerCase().includes(columnsSearch.toLowerCase())),
    [columnsSearch, columnsState]
  );

  const isAllChecked = columnsState.every(field => field.isShowing);
  const selectedCount = useMemo(() => {
    if (visibleColumns && visibleColumns.length > 0) {
      // When visibleColumns is provided, show count of visible columns
      return visibleColumns.length;
    }
    // In normal mode, show count of selected columns
    const count = appliedColumns.filter(field => field.isShowing).length;
    // If no visibleColumns provided and no columns selected, show default count
    // This handles the case when pages don't provide visibleColumns initially
    if (count === 0 && !visibleColumns) {
      // Count the number of columns that would be visible by default
      // (Name, Started, Due On, Task Completed, Status, Activity, Assignee)
      // Matter and Attorney are not included in default visible columns
      return 7;
    }
    return count;
  }, [appliedColumns, visibleColumns]);
  const isSearching = columnsSearch.trim().length > 0;

  const toggleAll = (checked: boolean) => {
    setColumnsState(prev => prev.map(field => ({ ...field, isShowing: checked })));
  };

  const toggleSingle = (name: string, checked: boolean) => {
    setColumnsState(prev =>
      prev.map(field => (field.name === name ? { ...field, isShowing: checked } : field))
    );
  };

  const handleApply = () => {
    // Update applied columns to match current selection state
    const updatedColumns = columnsState.map(field => ({
      ...field,
      isShowing: field.isShowing, // Ensure the isShowing status is properly set
    }));

    setAppliedColumns(updatedColumns);
    setIsColumnsOpen(false);

    // Get only the selected column names to pass to parent
    const selectedNames = updatedColumns.filter(f => f.isShowing).map(f => f.name);

    // Call the parent callback to update visible columns
    if (onColumns) {
      onColumns(selectedNames);
    }
  };

  const handleRemoveFilter = (id: string) => {
    setFilters(prev => prev.filter(f => f.id !== id));
  };

  const handleAddFilter = () => {
    setFilters(prev => [
      ...prev,
      { id: `${Date.now()}`, field: 'Workflow Name', operator: '', value: '', selectedValues: [] },
    ]);
  };

  // Calculate active filters count based on applied filters
  const getActiveFiltersCount = () => {
    return appliedFilters.filter(f => {
      // A filter is considered active if it has a valid field, operator, and value
      const hasValidField = f.field && f.field.trim() !== '';
      const hasValidOperator = f.operator !== '';

      // For multi-select fields, check selectedValues
      if (
        isAssigneeField(f.field) ||
        isStatusField(f.field) ||
        isMatterField(f.field) ||
        isTemplatesField(f.field) ||
        isUserField(f.field)
      ) {
        const hasValidValue = f.selectedValues && f.selectedValues.length > 0;
        return hasValidField && hasValidOperator && hasValidValue;
      }

      const hasValidValue = f.value && f.value.trim() !== '';

      // For unassigned operator, we don't need a value
      if (f.operator === 'unassigned') {
        return hasValidField && hasValidOperator;
      }

      return hasValidField && hasValidOperator && hasValidValue;
    }).length;
  };

  // Effect to handle the filters update after state is updated
  React.useEffect(() => {
    if (pendingFiltersUpdate && onFiltersChange) {
      // Convert filters to the format expected by parent (with value as string[] for saved filters)
      const convertedFilters = pendingFiltersUpdate.map(filter => {
        // For 'between' operator with pipe-separated values, convert back to array
        if (filter.operator === 'between' && filter.value.includes('|')) {
          const [start, end] = filter.value.split('|');
          return {
            ...filter,
            selectedValues: [start, end],
          };
        }
        return filter;
      });
      onFiltersChange(convertedFilters);
      setPendingFiltersUpdate(null);
    }
  }, [pendingFiltersUpdate, onFiltersChange]);

  const applyFilters = () => {
    setIsFilterOpen(false);

    // Update applied filters to track changes
    setAppliedFilters(filters);

    // Store the current filters for the callback
    setPendingFiltersUpdate(filters);

    // Call legacy callback for backward compatibility
    onFilter?.();
    // New typed callback with data if provided
    if (typeof onApplyFilters === 'function') {
      onApplyFilters(filters);
    }
    // Transform current filters to API payload and call callback
    if (onFilteredDataReceived) {
      // Field mapping from UI/API keys to backend expected keys
      const fieldMap: Record<string, string> = {
        // UI labels
        'Workflow Name': 'template_name',
        Assignee: 'assigned_users',
        Status: 'status',
        Contact: 'contact',
        Client: 'client',
        Matter: 'matter',
        Templates: 'work_flow_runner',
        'Due date': 'end_date',
        'Create date': 'start_date',
        Attorney: 'attorney',
        template_name: 'template_name',
        assigned_users: 'assigned_users',
        status: 'status',
        client_name: 'contact',
        matter: 'matter',
        matter_name: 'matter',
        work_flow_runner: 'work_flow_runner',
        end_date: 'end_date',
        due_on: 'end_date',
        start_date: 'start_date',
        created_at: 'start_date',
        attorney: 'attorney',
      };

      const opMap: Record<string, string> = {
        contains: 'contains',
        equals: 'equal',
        not_equals: 'not_equals',
        not_contains: 'not_contains',
        unassigned: 'unassigned',
      };

      // Only process valid filters (same logic as getActiveFiltersCount)
      const validFilters = filters.filter(f => {
        const hasValidField = f.field && f.field.trim() !== '';
        const hasValidOperator = f.operator || isDateField(f.field);
        const hasValidValue = f.value && f.value.trim() !== '';

        // For unassigned operator, we don't need a value
        if (f.operator === 'unassigned') {
          return hasValidField && hasValidOperator;
        }

        return hasValidField && hasValidOperator && hasValidValue;
      });

      const apiFilters = validFilters
        .map(row => {
          const fieldName = fieldMap[row.field] || row.field;
          if (!fieldName) return null;

          if (isDateField(row.field)) {
            if (row.operator === 'between') {
              const [start, end] = (row.value || '')
                .split('|')
                .map(s => s?.trim())
                .filter(Boolean);
              if (start && end) {
                // Convert from MM/DD/YYYY to YYYY-MM-DD
                const [startMonth, startDay, startYear] = start.split('/');
                const [endMonth, endDay, endYear] = end.split('/');
                const formattedStartDate = `${startYear}-${startMonth}-${startDay}`;
                const formattedEndDate = `${endYear}-${endMonth}-${endDay}`;
                return {
                  fieldName,
                  filter: 'date_between',
                  dateRange: {
                    from: formattedStartDate,
                    to: formattedEndDate,
                  },
                };
              }
              return null;
            } else if (row.operator === 'before') {
              const dateValue = (row.value || '').trim();
              if (dateValue) {
                // Convert from MM/DD/YYYY to YYYY-MM-DD
                const [month, day, year] = dateValue.split('/');
                const formattedDate = `${year}-${month}-${day}`;
                return { fieldName, filter: 'date_before', date: [formattedDate] };
              }
              return null;
            } else if (row.operator === 'after') {
              const dateValue = (row.value || '').trim();
              if (dateValue) {
                // Convert from MM/DD/YYYY to YYYY-MM-DD
                const [month, day, year] = dateValue.split('/');
                const formattedDate = `${year}-${month}-${day}`;
                return { fieldName, filter: 'date_after', date: [formattedDate] };
              }
              return null;
            }
            return null;
          }

          // Handle multi-select fields with multiple values
          if (
            (isAssigneeField(row.field) ||
              isNameField(row.field) ||
              isStatusField(row.field) ||
              isMatterField(row.field) ||
              isTemplatesField(row.field) ||
              isUserField(row.field) ||
              isAttorneyField(row.field) ||
              isContactField(row.field)) &&
            row.selectedValues &&
            row.selectedValues.length > 0
          ) {
            const apiOp = opMap[row.operator || ''] || 'contains';
            let apiValues;

            // Convert status values to API format (with underscores)
            if (isStatusField(row.field)) {
              apiValues = convertStatusArrayToApiFormat(row.selectedValues);
            } else if (isMatterField(row.field)) {
              // Send full matter values (client|matter) for precise filtering
              apiValues = row.selectedValues;
            } else {
              apiValues = row.selectedValues;
            }

            return { fieldName, filter: apiOp, value: apiValues };
          }

          const apiOp = opMap[row.operator || ''] || 'contains';
          const v = (row.value || '').trim();

          // For unassigned, send empty array value (no values needed)
          if (row.operator === 'unassigned') {
            return { fieldName, filter: apiOp, value: [] };
          }

          // For other operators, ensure we have a value
          if (!v) return null;

          // Convert status values to API format if it's a status field
          // Send full matter values for precise filtering
          let apiValue;
          if (isStatusField(row.field)) {
            apiValue = convertStatusArrayToApiFormat([v]);
          } else if (isMatterField(row.field)) {
            apiValue = [v]; // Send full value (client|matter) for precise filtering
          } else {
            apiValue = [v];
          }

          return { fieldName, filter: apiOp, value: apiValue };
        })
        .filter(Boolean);

      // Final sanitization: keep matter field values as full values for precise filtering
      const sanitizedApiFilters = apiFilters
        .map(filter => {
          if (
            filter &&
            isMatterField(filter.fieldName) &&
            filter.value &&
            Array.isArray(filter.value)
          ) {
            return {
              ...filter,
              value: filter.value, // Keep full values (client|matter) for precise filtering
            };
          }
          return filter;
        })
        .filter((filter): filter is NonNullable<typeof filter> => filter !== null);

      // Call callback to trigger data refresh with filters in the parent component
      onFilteredDataReceived(sanitizedApiFilters);
    }
  };

  return (
    <div className={`flex items-center gap-[12px] text-[14px] leading-[20px] ${className}`}>
      {/* <SearchInput value={searchValue} onChange={onSearchChange} placeholder="Search" /> */}

      {showFilter && (
        <div className="relative" ref={filterRef}>
          <TableActionButton
            ref={filterButtonRef}
            label={
              <div className="flex items-center gap-2">
                <span className="leading-[20px]">Filter</span>
                {getActiveFiltersCount() > 0 && (
                  <span className="bg-[#3F73F6] text-white text-xs px-1.5 py-0.5 rounded-full min-w-[18px] h-[18px] flex items-center justify-center">
                    {getActiveFiltersCount()}
                  </span>
                )}
              </div>
            }
            icon={<Image src="/assets/filter.svg" alt="Filter" width={20} height={20} />}
            onClick={() => {
              if (!isFilterOpen && filterButtonRef.current) {
                // Calculate position before opening
                const rect = filterButtonRef.current.getBoundingClientRect();
                const isLargeScreen = window.innerWidth >= 1440;
                setFilterButtonPosition({
                  top: rect.bottom + 4,
                  left: isLargeScreen ? rect.right - 724 : rect.right - 724 + 80,
                });
              }
              setIsFilterOpen(v => !v);
            }}
            variant="secondary"
            width="xl-plus:w-[124px] w-auto"
            className={`hover:text-[#2A2E34] hover:[&_img]:brightness-0 hover:[&_img]:invert-[0.15] ${
              isFilterOpen
                ? 'ring-1 ring-[#3F73F6] border-transparent focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)]'
                : ''
            }`}
          />

          {isFilterOpen && (
            <div
              className="fixed w-[724px] rounded-[16px] border border-[#E5E7EB] bg-white z-[99999] p-[30px] overflow-y-auto"
              style={{
                top: `${filterButtonPosition.top}px`,
                left: `${filterButtonPosition.left}px`,
                maxHeight: `calc(100vh - ${filterButtonPosition.top}px - 50px)`,
                boxShadow: '0 10px 20px 0 rgba(0, 0, 0, 0.08)',
              }}
              onClick={e => {
                // Don't close anything if DateTimeModal is open
                if (isDateModalOpen) {
                  return;
                }

                // Close all dropdowns when clicking anywhere inside the filter card
                // But don't close if clicking on a dropdown button or input
                const target = e.target as HTMLElement;
                const isDropdownButton =
                  target.closest('[data-dropdown]') ||
                  target.closest('input') ||
                  target.closest('.dropdown-content');

                if (!isDropdownButton) {
                  setOpenFieldDropdownId(null);
                  setOpenOperatorDropdownId(null);
                  setOpenValueDropdownId(null);
                  setOpenStatusDropdownId(null);
                  setFilterSearchValues({});
                }
              }}
            >
              <div className="space-y-3">
                {filters.map(f => (
                  <div
                    key={f.id}
                    className="grid grid-cols-[40px_160px_160px_1fr] gap-2 mb-[16px] items-center"
                  >
                    <button
                      data-dropdown="action"
                      type="button"
                      aria-label="Remove filter"
                      className="h-10 w-10 flex items-center justify-center rounded-[12px] border border-[#E5E7EB] text-[#5F6F84] hover:bg-[#F3F4F6]"
                      onClick={() => handleRemoveFilter(f.id)}
                    >
                      <X size={20} />
                    </button>

                    <div className="relative" ref={fieldDropdownRef}>
                      <button
                        data-dropdown="field"
                        className="h-10 w-full flex items-center rounded-[12px] border border-[#E5E7EB] px-3 text-sm text-[#2A2E34] bg-white focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)] hover:border-[#3F73F6] transition-colors cursor-pointer"
                        onClick={e => {
                          const isOpening = openFieldDropdownId !== f.id;
                          if (isOpening) {
                            // Close all other dropdowns when opening this one
                            setOpenOperatorDropdownId(null);
                            setOpenStatusDropdownId(null);
                            setOpenValueDropdownId(null);
                            setOpenFieldDropdownId(f.id);

                            // Calculate position for portal
                            const rect = e.currentTarget.getBoundingClientRect();
                            const maxDropdownHeight = 270; // Max height of the dropdown
                            const spaceBelow = window.innerHeight - rect.bottom;
                            const spaceAbove = rect.top;
                            const openAbove =
                              spaceBelow < maxDropdownHeight && spaceAbove > maxDropdownHeight;

                            setFieldDropdownPosition({
                              top: openAbove ? rect.top - 4 : rect.bottom + 4,
                              left: rect.left,
                              width: rect.width,
                              openAbove,
                            });
                          } else {
                            setOpenFieldDropdownId(null);
                          }
                        }}
                      >
                        <span className="flex-1 truncate text-left">
                          {f.field || 'Select field'}
                        </span>
                        <ChevronDown
                          size={16}
                          className={`ml-2 text-[#6B7280] transition-transform flex-shrink-0 ${
                            openFieldDropdownId === f.id ? 'rotate-180' : ''
                          }`}
                        />
                      </button>

                      {/* Field dropdown list - rendered via portal */}
                    </div>

                    <div>
                      <div className="relative" ref={operatorDropdownRef}>
                        <button
                          data-dropdown="operator"
                          className={`h-10 w-full flex items-center justify-between rounded-[12px] border border-[#E5E7EB] px-3 text-sm bg-white focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)] hover:border-[#3F73F6] transition-colors cursor-pointer ${
                            f.operator === '' ? 'text-[#2A2E34]' : 'text-[#2A2E34]'
                          }`}
                          onClick={e => {
                            const isOpening = openOperatorDropdownId !== f.id;
                            if (isOpening) {
                              // Close all other dropdowns when opening this one
                              setOpenFieldDropdownId(null);
                              setOpenStatusDropdownId(null);
                              setOpenValueDropdownId(null);
                              setOpenOperatorDropdownId(f.id);

                              // Calculate position for portal
                              const rect = e.currentTarget.getBoundingClientRect();
                              const maxDropdownHeight = 270; // Max height of the dropdown
                              const spaceBelow = window.innerHeight - rect.bottom;
                              const spaceAbove = rect.top;
                              const openAbove =
                                spaceBelow < maxDropdownHeight && spaceAbove > maxDropdownHeight;

                              setOperatorDropdownPosition({
                                top: openAbove ? rect.top - 4 : rect.bottom + 4,
                                left: rect.left,
                                width: rect.width,
                                openAbove,
                              });
                            } else {
                              setOpenOperatorDropdownId(null);
                            }
                          }}
                        >
                          <span className="truncate text-left flex-1 min-w-0">
                            {getOperatorDisplayLabel(f.field, f.operator)}
                          </span>
                          <ChevronDown
                            size={16}
                            className={`ml-2 text-[#6B7280] transition-transform flex-shrink-0 ${
                              openOperatorDropdownId === f.id ? 'rotate-180' : ''
                            }`}
                          />
                        </button>

                        {/* Operator dropdown list - rendered via portal */}
                      </div>
                    </div>

                    <div className="relative">
                      {isDateField(f.field) ? (
                        <div>
                          {f.operator === 'between' ? (
                            <button
                              data-dropdown="date"
                              type="button"
                              className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 text-sm text-[#2A2E34] focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)] hover:border-[#3F73F6] transition-colors cursor-pointer flex items-center justify-between whitespace-nowrap"
                              onClick={e => handleDateModalOpen(f.id, e)}
                            >
                              <span
                                className={`${
                                  f.value &&
                                  f.value.includes('|') &&
                                  f.value.split('|')[0] &&
                                  f.value.split('|')[1]
                                    ? 'text-[#2A2E34]'
                                    : 'text-[#9AA4B2]'
                                } truncate`}
                              >
                                {f.value &&
                                f.value.includes('|') &&
                                f.value.split('|')[0] &&
                                f.value.split('|')[1]
                                  ? `${f.value.split('|')[0]} - ${f.value.split('|')[1]}`
                                  : 'Select date range'}
                              </span>
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M8 2V5"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M16 2V5"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M3.5 9H20.5"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            </button>
                          ) : (
                            <button
                              data-dropdown="date"
                              type="button"
                              className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 text-sm text-[#2A2E34] focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)] hover:border-[#3F73F6] transition-colors cursor-pointer flex items-center justify-between"
                              onClick={e => {
                                // If no operator is selected, default to 'after' for a single date selection
                                if (!f.operator) {
                                  handleFilterChange(f.id, 'operator', 'after');
                                }
                                handleDateModalOpen(f.id, e);
                              }}
                            >
                              <span className={f.value ? 'text-[#2A2E34]' : 'text-[#9AA4B2]'}>
                                {f.value || 'Select date'}
                              </span>
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M8 2V5"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M16 2V5"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M3.5 9H20.5"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            </button>
                          )}
                        </div>
                      ) : isStatusField(f.field) ? (
                        <div className="relative" ref={statusDropdownRef}>
                          <button
                            data-dropdown="status"
                            className="h-10 w-full flex items-center justify-between rounded-[12px] border border-[#E5E7EB] px-3 text-sm bg-white focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)] hover:border-[#3F73F6] transition-colors cursor-pointer"
                            onClick={async e => {
                              const isOpening = openStatusDropdownId !== f.id;
                              if (isOpening) {
                                // Close all other dropdowns when opening this one
                                setOpenFieldDropdownId(null);
                                setOpenOperatorDropdownId(null);
                                setOpenValueDropdownId(null);
                                setOpenStatusDropdownId(f.id);

                                // Calculate position for portal
                                const rect = e.currentTarget.getBoundingClientRect();
                                const maxDropdownHeight = 320; // Max height of the dropdown
                                const spaceBelow = window.innerHeight - rect.bottom;
                                const spaceAbove = rect.top;
                                const openAbove =
                                  spaceBelow < maxDropdownHeight && spaceAbove > maxDropdownHeight;

                                setStatusDropdownPosition({
                                  top: openAbove ? rect.top - 4 : rect.bottom + 4,
                                  left: rect.left,
                                  width: rect.width,
                                  openAbove,
                                });

                                if (!fieldValueOptions[f.field]) {
                                  const opts = await fetchDistinctValues(f.field);
                                  setFieldValueOptions(prevMap => ({
                                    ...prevMap,
                                    [f.field]: opts,
                                  }));
                                }
                              } else {
                                setOpenStatusDropdownId(null);
                              }
                            }}
                          >
                            <div className="flex items-center gap-[4px]">
                              {f.selectedValues && f.selectedValues.length > 0 ? (
                                <>
                                  <span
                                    className={`px-[16px] py-1 rounded-full text-[12px] leading-[16px] font-regular ${
                                      f.selectedValues[0] === 'ON_TRACK' ||
                                      f.selectedValues[0] === 'On Track' ||
                                      f.selectedValues[0] === 'Current'
                                        ? 'bg-[#8CF1BD] text-[#2A2E34]'
                                        : f.selectedValues[0] === 'DUE_SOON' ||
                                            f.selectedValues[0] === 'Due Soon'
                                          ? 'bg-[#FFDB93] text-[#2A2E34]'
                                          : f.selectedValues[0] === 'OVERDUE' ||
                                              f.selectedValues[0] === 'Overdue'
                                            ? 'bg-[#EF8B8B] text-[#2A2E34]'
                                            : f.selectedValues[0] === 'COMPLETED' ||
                                                f.selectedValues[0] === 'Completed'
                                              ? 'bg-[#97C7FF] text-[#2A2E34]'
                                              : 'bg-gray-500 text-white'
                                    }`}
                                  >
                                    {f.selectedValues[0]}
                                  </span>
                                  {f.selectedValues.length > 1 && (
                                    <span className="inline-flex items-center justify-center h-6 px-3 py-1 rounded-full bg-[#C7D1DF] text-[#2A2E34] text-xs font-medium leading-none">
                                      +{f.selectedValues.length - 1}
                                    </span>
                                  )}
                                </>
                              ) : (
                                <span className="text-[#9AA4B2]">Select</span>
                              )}
                            </div>
                            <ChevronDown
                              size={16}
                              className={`text-[#6B7280] transition-transform ${
                                openStatusDropdownId === f.id ? 'rotate-180' : ''
                              }`}
                            />
                          </button>

                          {/* Status dropdown list with checkboxes */}
                          {/* Status dropdown - rendered via portal */}
                        </div>
                      ) : isAssigneeField(f.field) ||
                        isNameField(f.field) ||
                        isAttorneyField(f.field) ||
                        isContactField(f.field) ? (
                        <div className="relative" ref={valueDropdownRef}>
                          <div
                            data-dropdown="value"
                            className={`h-10 w-full rounded-[12px] border p-[8px] text-sm flex items-center justify-between transition-colors ${
                              f.operator === 'unassigned'
                                ? 'border-[#E5E7EB] bg-[#F9FAFB] cursor-not-allowed opacity-50'
                                : 'border-[#E5E7EB] bg-white focus-within:border-[#3F73F6] focus-within:shadow-[0_0_0_2px_rgba(63,115,246,0.2)] hover:border-[#3F73F6] cursor-pointer'
                            }`}
                            onClick={async e => {
                              // Don't allow clicks if operator is unassigned
                              if (f.operator === 'unassigned') return;

                              const isOpening = openValueDropdownId !== f.id;

                              if (isOpening) {
                                // Close all other dropdowns when opening this one
                                setOpenFieldDropdownId(null);
                                setOpenOperatorDropdownId(null);
                                setOpenStatusDropdownId(null);
                                setOpenValueDropdownId(f.id);

                                // Calculate position for portal
                                const rect = e.currentTarget.getBoundingClientRect();
                                const maxDropdownHeight = 450; // Max height of the dropdown
                                const spaceBelow = window.innerHeight - rect.bottom;
                                const spaceAbove = rect.top;
                                const openAbove =
                                  spaceBelow < maxDropdownHeight && spaceAbove > maxDropdownHeight;

                                setValueDropdownPosition({
                                  top: openAbove ? rect.top - 4 : rect.bottom + 4,
                                  left: rect.left,
                                  width: rect.width,
                                  openAbove,
                                });

                                if (!fieldValueOptions[f.field]) {
                                  try {
                                    let opts: string[] = [];
                                    if (isAssigneeField(f.field)) {
                                      opts = await fetchUsers();
                                    } else if (isNameField(f.field)) {
                                      opts = await fetchNames();
                                    } else if (isAttorneyField(f.field)) {
                                      opts = await fetchAttorneys();
                                    } else if (isContactField(f.field)) {
                                      opts = await fetchContacts();
                                    }
                                    setFieldValueOptions(prevMap => ({
                                      ...prevMap,
                                      [f.field]: opts,
                                    }));
                                  } catch (error) {
                                    console.error('Error fetching options:', error);
                                  }
                                }
                              } else {
                                setOpenValueDropdownId(null);
                                setFilterSearchValues(prev => ({ ...prev, [f.id]: '' }));
                              }
                            }}
                          >
                            <div className="flex-1">
                              {f.operator === 'unassigned' ? (
                                <span className="text-[#9CA3AF] font-regular">Search & select</span>
                              ) : f.selectedValues && f.selectedValues.length > 0 ? (
                                <span className="inline-flex items-center px-[12px] py-[4px] rounded-full text-[12px] leading-[16px] font-regular bg-[#C7D1DF] text-[#2A2E34]">
                                  {f.selectedValues.length === 1
                                    ? '1 selected'
                                    : `${f.selectedValues.length} selected`}
                                </span>
                              ) : (
                                <span className="text-[#9CA3AF] pl-[8px]">Search & select</span>
                              )}
                            </div>
                            <ChevronDown
                              size={16}
                              className={`text-[#6B7280] transition-transform ${
                                openValueDropdownId === f.id ? 'rotate-180' : ''
                              }`}
                            />
                          </div>

                          {/* Value dropdown - rendered via portal */}
                        </div>
                      ) : isMatterField(f.field) ? (
                        <div className="relative" ref={valueDropdownRef}>
                          <div
                            data-dropdown="value"
                            className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 text-sm bg-white focus-within:border-[#3F73F6] focus-within:shadow-[0_0_0_2px_rgba(63,115,246,0.2)] hover:border-[#3F73F6] transition-colors cursor-pointer flex items-center justify-between"
                            onClick={async e => {
                              const isOpening = openValueDropdownId !== f.id;
                              if (isOpening) {
                                // Close all other dropdowns when opening this one
                                setOpenFieldDropdownId(null);
                                setOpenOperatorDropdownId(null);
                                setOpenStatusDropdownId(null);
                                setOpenValueDropdownId(f.id);

                                // Calculate position for portal
                                const rect = e.currentTarget.getBoundingClientRect();
                                const maxDropdownHeight = 450; // Max height of the dropdown
                                const spaceBelow = window.innerHeight - rect.bottom;
                                const spaceAbove = rect.top;
                                const openAbove =
                                  spaceBelow < maxDropdownHeight && spaceAbove > maxDropdownHeight;

                                setValueDropdownPosition({
                                  top: openAbove ? rect.top - 4 : rect.bottom + 4,
                                  left: rect.left,
                                  width: rect.width,
                                  openAbove,
                                });

                                if (!fieldValueOptions[f.field]) {
                                  const opts = await fetchMatters();
                                  setFieldValueOptions(prevMap => ({
                                    ...prevMap,
                                    [f.field]: opts,
                                  }));
                                }
                              } else {
                                setOpenValueDropdownId(null);
                                setFilterSearchValues(prev => ({ ...prev, [f.id]: '' }));
                              }
                            }}
                          >
                            <div className="flex-1">
                              {f.selectedValues && f.selectedValues.length > 0 ? (
                                <span className="inline-flex items-center px-[12px] py-[4px] text-[12px] leading-[16px] font-regular rounded-full bg-[#C7D1DF] text-[#2A2E34]">
                                  {f.selectedValues.length} selected
                                </span>
                              ) : (
                                <span className="text-[#9AA4B2] pl-[8px]">Search & select</span>
                              )}
                            </div>
                            <ChevronDown
                              size={16}
                              className={`text-[#6B7280] transition-transform ${
                                openValueDropdownId === f.id ? 'rotate-180' : ''
                              }`}
                            />
                          </div>
                        </div>
                      ) : isTemplatesField(f.field) ? (
                        <div className="relative" ref={valueDropdownRef}>
                          <div
                            data-dropdown="value"
                            className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 text-sm bg-white focus-within:border-[#3F73F6] focus-within:shadow-[0_0_0_2px_rgba(63,115,246,0.2)] hover:border-[#3F73F6] transition-colors cursor-pointer flex items-center justify-between"
                            onClick={e => {
                              const isOpening = openValueDropdownId !== f.id;
                              if (isOpening) {
                                // Close all other dropdowns when opening this one
                                setOpenFieldDropdownId(null);
                                setOpenOperatorDropdownId(null);
                                setOpenStatusDropdownId(null);
                                setOpenValueDropdownId(f.id);

                                // Calculate position for portal
                                const rect = e.currentTarget.getBoundingClientRect();
                                const maxDropdownHeight = 450; // Max height of the dropdown
                                const spaceBelow = window.innerHeight - rect.bottom;
                                const spaceAbove = rect.top;
                                const openAbove =
                                  spaceBelow < maxDropdownHeight && spaceAbove > maxDropdownHeight;

                                setValueDropdownPosition({
                                  top: openAbove ? rect.top - 4 : rect.bottom + 4,
                                  left: rect.left,
                                  width: rect.width,
                                  openAbove,
                                });

                                // Ensure template options are always available
                                if (
                                  !fieldValueOptions[f.field] ||
                                  fieldValueOptions[f.field].length === 0
                                ) {
                                  setFieldValueOptions(prevMap => ({
                                    ...prevMap,
                                    [f.field]: getTemplateOptions(),
                                  }));
                                }
                              } else {
                                setOpenValueDropdownId(null);
                                setFilterSearchValues(prev => ({ ...prev, [f.id]: '' }));
                              }
                            }}
                          >
                            <div className="flex-1">
                              {f.selectedValues && f.selectedValues.length > 0 ? (
                                <span className="inline-flex items-center px-3 py-1 rounded-full text-[12px] leading-[16px] font-regular bg-[#C7D1DF] text-[#2A2E34]">
                                  {f.selectedValues.length} selected
                                </span>
                              ) : (
                                <span className="text-[#9AA4B2]">Search & select</span>
                              )}
                            </div>
                            <ChevronDown
                              size={16}
                              className={`text-[#6B7280] transition-transform ${
                                openValueDropdownId === f.id ? 'rotate-180' : ''
                              }`}
                            />
                          </div>
                        </div>
                      ) : isUserField(f.field) ? (
                        <div className="relative" ref={valueDropdownRef}>
                          <div
                            data-dropdown="value"
                            className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 text-sm bg-white focus-within:border-[#3F73F6] focus-within:shadow-[0_0_0_2px_rgba(63,115,246,0.2)] hover:border-[#3F73F6] transition-colors cursor-pointer flex items-center justify-between"
                            onClick={async () => {
                              const isOpening = openValueDropdownId !== f.id;
                              if (isOpening) {
                                // Close all other dropdowns when opening this one
                                setOpenFieldDropdownId(null);
                                setOpenOperatorDropdownId(null);
                                setOpenStatusDropdownId(null);
                                setOpenValueDropdownId(f.id);
                                if (!fieldValueOptions[f.field]) {
                                  const opts = await fetchUsers();
                                  setFieldValueOptions(prevMap => ({
                                    ...prevMap,
                                    [f.field]: opts,
                                  }));
                                }
                              } else {
                                setOpenValueDropdownId(null);
                                setFilterSearchValues(prev => ({ ...prev, [f.id]: '' }));
                              }
                            }}
                          >
                            <div className="flex-1">
                              {f.selectedValues && f.selectedValues.length > 0 ? (
                                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#C7D1DF] text-[#2A2E34]">
                                  {f.selectedValues.length} selected
                                </span>
                              ) : (
                                <span className="text-[#9AA4B2]">Select users</span>
                              )}
                            </div>
                            <ChevronDown
                              size={16}
                              className={`text-[#6B7280] transition-transform ${
                                openValueDropdownId === f.id ? 'rotate-180' : ''
                              }`}
                            />
                          </div>

                          {openValueDropdownId === f.id && (
                            <div className="absolute left-0 right-0 top-11 z-[99999] bg-white border border-[#E5E7EB] rounded-[16px] max-h-96 overflow-hidden shadow-2xl">
                              {/* Selected items section */}
                              {f.selectedValues && f.selectedValues.length > 0 && (
                                <div className="px-4 py-4">
                                  <div className="text-sm font-medium text-[#2A2E34] mb-3">
                                    {f.field}
                                  </div>
                                  <div className="space-y-2">
                                    {f.selectedValues.map(selectedValue => (
                                      <div
                                        key={selectedValue}
                                        className="flex items-center justify-between p-2 rounded-[6px]"
                                      >
                                        <div className="flex items-center gap-2 flex-1 min-w-0">
                                          <span className="text-[#2A2E34] font-medium text-sm truncate">
                                            {selectedValue}
                                          </span>
                                        </div>
                                        <button
                                          type="button"
                                          onClick={e => {
                                            e.stopPropagation();
                                            handleUserSelection(f.id, selectedValue);
                                          }}
                                          className="text-[#9AA4B2] hover:text-[#6B7280] transition-colors"
                                        >
                                          <X size={16} />
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Search input section */}
                              <div className="p-4 border-b border-[#E5E7EB]">
                                <div className="relative">
                                  <input
                                    data-dropdown="search"
                                    className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)]"
                                    placeholder="Type a user name"
                                    value={filterSearchValues[f.id] || ''}
                                    onChange={e =>
                                      setFilterSearchValues(prev => ({
                                        ...prev,
                                        [f.id]: e.target.value,
                                      }))
                                    }
                                  />
                                  <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                                    <svg
                                      width="20"
                                      height="20"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      className="text-[#9AA4B2]"
                                    >
                                      <circle cx="11" cy="11" r="8" />
                                      <path d="m21 21-4.35-4.35" />
                                    </svg>
                                  </div>
                                  {filterSearchValues[f.id] && (
                                    <button
                                      type="button"
                                      onClick={e => {
                                        e.stopPropagation();
                                        setFilterSearchValues(prev => ({
                                          ...prev,
                                          [f.id]: '',
                                        }));
                                      }}
                                      className="absolute right-8 top-1/2 -translate-y-1/2 text-[#9AA4B2] hover:text-[#6B7280] transition-colors"
                                    >
                                      <svg
                                        width="16"
                                        height="16"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                      >
                                        <line x1="18" y1="6" x2="6" y2="18" />
                                        <line x1="6" y1="6" x2="18" y2="18" />
                                      </svg>
                                    </button>
                                  )}
                                </div>
                              </div>

                              {/* Suggestions section with horizontal scroll */}
                              <div className="p-4">
                                <div className="max-h-80 overflow-y-auto overflow-x-auto">
                                  <div className="grid grid-cols-1 gap-1 min-w-max">
                                    {(() => {
                                      const selectedValuesForField = getAllSelectedValuesForField(f.field);
                                      return (fieldValueOptions[f.field] || [])
                                        .filter(opt => {
                                          // Filter by search text
                                          const matchesSearch = opt
                                            .toLowerCase()
                                            .includes((filterSearchValues[f.id] || '').toLowerCase());
                                          
                                          // Exclude already selected values for this field type
                                          return matchesSearch && !selectedValuesForField.includes(opt);
                                        })
                                        .slice(0, 50);
                                    })()
                                      .map((opt, _index) => {
                                        const isSelected = f.selectedValues?.includes(opt) || false;
                                        return (
                                          <div
                                            key={opt}
                                            className={`flex items-center gap-3 px-2 py-2.5 hover:bg-[#F3F4F6] cursor-pointer rounded-[8px] text-sm transition-colors whitespace-nowrap ${
                                              isSelected
                                                ? 'bg-[#EEF2FF] border border-[#C7D2FE]'
                                                : ''
                                            }`}
                                            onClick={e => {
                                              e.stopPropagation();
                                              handleUserSelection(f.id, opt);
                                            }}
                                          >
                                            <div className="w-8 h-8 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                              <span className="text-white text-xs font-medium">
                                                {opt
                                                  .split(' ')
                                                  .map(n => n[0])
                                                  .join('')
                                                  .substring(0, 2)
                                                  .toUpperCase()}
                                              </span>
                                            </div>
                                            <span
                                              className={`font-medium ${isSelected ? 'text-[#3F73F6]' : 'text-[#2A2E34]'}`}
                                            >
                                              {opt}
                                            </span>
                                            {isSelected && (
                                              <div className="ml-auto">
                                                <div className="w-4 h-4 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                                  <svg
                                                    width="12"
                                                    height="12"
                                                    viewBox="0 0 24 24"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    strokeWidth="2"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    className="text-white"
                                                  >
                                                    <polyline points="20,6 9,17 4,12" />
                                                  </svg>
                                                </div>
                                              </div>
                                            )}
                                          </div>
                                        );
                                      })}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : isDateField(f.field) ? (
                        <div>
                          <div className="relative" ref={valueDropdownRef}>
                            <input
                              data-dropdown="input"
                              className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)]"
                              placeholder="Type a name"
                              value={f.value}
                              onFocus={async () => {
                                // Close all other dropdowns when opening this one
                                setOpenFieldDropdownId(null);
                                setOpenOperatorDropdownId(null);
                                setOpenStatusDropdownId(null);
                                setOpenValueDropdownId(f.id);
                                if (!fieldValueOptions[f.field]) {
                                  const opts = await fetchDistinctValues(f.field);
                                  setFieldValueOptions(prevMap => ({
                                    ...prevMap,
                                    [f.field]: opts,
                                  }));
                                }
                              }}
                              onChange={e => {
                                handleFilterChange(f.id, 'value', e.target.value);
                                if (!openValueDropdownId) {
                                  // Close all other dropdowns when opening this one
                                  setOpenFieldDropdownId(null);
                                  setOpenOperatorDropdownId(null);
                                  setOpenStatusDropdownId(null);
                                  setOpenValueDropdownId(f.id);
                                }
                              }}
                              onBlur={() => {
                                // delay close to allow option onMouseDown
                                setTimeout(
                                  () =>
                                    setOpenValueDropdownId(prevId =>
                                      prevId === f.id ? null : prevId
                                    ),
                                  100
                                );
                              }}
                            />
                            <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-[#9AA4B2]"
                              >
                                <circle cx="11" cy="11" r="8" />
                                <path d="m21 21-4.35-4.35" />
                              </svg>
                            </div>
                          </div>
                          {openValueDropdownId === f.id &&
                            (fieldValueOptions[f.field]?.length || 0) > 0 && (
                              <div className="absolute left-0 right-0 top-11 z-[99999] bg-white border border-[#E5E7EB] rounded-[16px] max-h-80 overflow-hidden shadow-2xl">
                                {/* Search input inside dropdown */}
                                <div className="p-4 border-b border-[#E5E7EB]">
                                  <div className="relative">
                                    <input
                                      className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)]"
                                      placeholder="Type a name"
                                      value={f.value}
                                      onChange={e => {
                                        handleFilterChange(f.id, 'value', e.target.value);
                                      }}
                                    />
                                    <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                                      <svg
                                        width="20"
                                        height="20"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        className="text-[#9AA4B2]"
                                      >
                                        <circle cx="11" cy="11" r="8" />
                                        <path d="m21 21-4.35-4.35" />
                                      </svg>
                                    </div>
                                  </div>
                                </div>

                                {/* Suggestions section */}
                                <div className="p-4">
                                  {/* <h3 className="text-sm font-medium text-[#2A2E34] mb-3">Suggestions</h3> */}
                                  <div className="max-h-60 overflow-auto">
                                    {(() => {
                                      const selectedAssignees = getAllSelectedAssignees();
                                      return (fieldValueOptions[f.field] || [])
                                        .filter(opt => {
                                          // Filter by search text
                                          const matchesSearch = opt.toLowerCase().includes((f.value || '').toLowerCase());
                                          
                                          // For assignee and name fields, exclude already selected assignees
                                          if (isAssigneeField(f.field) || isNameField(f.field)) {
                                            return matchesSearch && !selectedAssignees.includes(opt);
                                          }
                                          
                                          return matchesSearch;
                                        })
                                        .slice(0, 50);
                                    })()
                                      .map((opt, _index) => {
                                        // Generate avatar based on name
                                        // const _getInitials = (name: string) => {
                                        //     return name
                                        //       .split(' ')
                                        //       .map(n => n[0])
                                        //       .join('')
                                        //       .substring(0, 2)
                                        //       .toUpperCase();
                                        // };

                                        // const _getAvatarColor = (name: string) => {
                                        //   const colors = [
                                        //       'bg-blue-500',
                                        //       'bg-green-500',
                                        //       'bg-purple-500',
                                        //       'bg-pink-500',
                                        //       'bg-indigo-500',
                                        //       'bg-yellow-500',
                                        //       'bg-red-500',
                                        //       'bg-teal-500',
                                        //   ];
                                        //   const index = name.length % colors.length;
                                        //   return colors[index];
                                        // };

                                        // const _isPersonName =
                                        //   /^[A-Za-z\s]+$/.test(opt) && opt.includes(' ');

                                        return (
                                          <div
                                            key={opt}
                                            className="flex items-center gap-3 px-2 py-2.5 hover:bg-[#F3F4F6] cursor-pointer rounded-[8px] text-sm text-[#2A2E34] transition-colors"
                                            onMouseDown={() => {
                                              // onMouseDown to avoid blur before click
                                              handleFilterChange(f.id, 'value', opt);
                                              setOpenValueDropdownId(null);
                                            }}
                                          >
                                            {/* {isPersonName ? (
                                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium ${getAvatarColor(opt)}`}>
                                              {getInitials(opt)}
                                            </div>
                                          ) : (
                                            <div className="w-8 h-8 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                                                <circle cx="12" cy="7" r="4" />
                                              </svg>
                                            </div>
                                          )} */}
                                            <span className="font-medium">{opt}</span>
                                          </div>
                                        );
                                      })}
                                  </div>
                                </div>
                              </div>
                            )}
                        </div>
                      ) : (
                        <>
                          <div className="relative" ref={valueDropdownRef}>
                            <input
                              data-dropdown="input"
                              className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none  focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)]"
                              placeholder="Type a name"
                              value={f.value}
                              onFocus={async () => {
                                // Close all other dropdowns when opening this one
                                setOpenFieldDropdownId(null);
                                setOpenOperatorDropdownId(null);
                                setOpenStatusDropdownId(null);
                                setOpenValueDropdownId(f.id);
                                if (!fieldValueOptions[f.field]) {
                                  const opts = await fetchDistinctValues(f.field);
                                  setFieldValueOptions(prevMap => ({
                                    ...prevMap,
                                    [f.field]: opts,
                                  }));
                                }
                              }}
                              onChange={e => {
                                handleFilterChange(f.id, 'value', e.target.value);
                                if (!openValueDropdownId) {
                                  // Close all other dropdowns when opening this one
                                  setOpenFieldDropdownId(null);
                                  setOpenOperatorDropdownId(null);
                                  setOpenStatusDropdownId(null);
                                  setOpenValueDropdownId(f.id);
                                }
                              }}
                              onBlur={() => {
                                // delay close to allow option onMouseDown
                                setTimeout(
                                  () =>
                                    setOpenValueDropdownId(prevId =>
                                      prevId === f.id ? null : prevId
                                    ),
                                  100
                                );
                              }}
                            />
                            <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-[#9AA4B2]"
                              >
                                <circle cx="11" cy="11" r="8" />
                                <path d="m21 21-4.35-4.35" />
                              </svg>
                            </div>
                          </div>
                          {openValueDropdownId === f.id &&
                            (fieldValueOptions[f.field]?.length || 0) > 0 && (
                              <div className="absolute left-0 right-0 top-11 z-[99999] bg-white border border-[#E5E7EB] rounded-[16px] max-h-80 overflow-hidden shadow-2xl">
                                {/* Search input inside dropdown */}
                                <div className="p-4 border-b border-[#E5E7EB]">
                                  <div className="relative">
                                    <input
                                      className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)]"
                                      placeholder="Type a name"
                                      value={f.value}
                                      onChange={e => {
                                        handleFilterChange(f.id, 'value', e.target.value);
                                      }}
                                    />
                                    <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                                      <svg
                                        width="20"
                                        height="20"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        className="text-[#9AA4B2]"
                                      >
                                        <circle cx="11" cy="11" r="8" />
                                        <path d="m21 21-4.35-4.35" />
                                      </svg>
                                    </div>
                                  </div>
                                </div>

                                {/* Suggestions section */}
                                <div className="p-4">
                                  {/* <h3 className="text-sm font-medium text-[#2A2E34] mb-3">Suggestions</h3> */}
                                  <div className="max-h-60 overflow-auto">
                                    {(() => {
                                      const selectedAssignees = getAllSelectedAssignees();
                                      return (fieldValueOptions[f.field] || [])
                                        .filter(opt => {
                                          // Filter by search text
                                          const matchesSearch = opt.toLowerCase().includes((f.value || '').toLowerCase());
                                          
                                          // For assignee and name fields, exclude already selected assignees
                                          if (isAssigneeField(f.field) || isNameField(f.field)) {
                                            return matchesSearch && !selectedAssignees.includes(opt);
                                          }
                                          
                                          return matchesSearch;
                                        })
                                        .slice(0, 50);
                                    })()
                                      .map((opt, _index) => {
                                        // Generate avatar based on name
                                        // const _getInitials = (name: string) => {
                                        //   return name
                                        //     .split(' ')
                                        //     .map(n => n[0])
                                        //     .join('')
                                        //     .substring(0, 2)
                                        //     .toUpperCase();
                                        // };

                                        // const _getAvatarColor = (name: string) => {
                                        //   const colors = [
                                        //       'bg-blue-500',
                                        //       'bg-green-500',
                                        //       'bg-purple-500',
                                        //       'bg-pink-500',
                                        //       'bg-indigo-500',
                                        //       'bg-yellow-500',
                                        //       'bg-red-500',
                                        //       'bg-teal-500',
                                        //   ];
                                        //   const index = name.length % colors.length;
                                        //   return colors[index];
                                        // };

                                        // const _isPersonName =
                                        //   /^[A-Za-z\s]+$/.test(opt) && opt.includes(' ');

                                        return (
                                          <div
                                            key={opt}
                                            className="flex items-center gap-3 px-2 py-2.5 hover:bg-[#F3F4F6] cursor-pointer rounded-[8px] text-sm text-[#2A2E34] transition-colors"
                                            onMouseDown={() => {
                                              // onMouseDown to avoid blur before click
                                              handleFilterChange(f.id, 'value', opt);
                                              setOpenValueDropdownId(null);
                                            }}
                                          >
                                            {/* {isPersonName ? (
                                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium ${getAvatarColor(opt)}`}>
                                              {getInitials(opt)}
                                            </div>
                                          ) : (
                                            <div className="w-8 h-8 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                                                <circle cx="12" cy="7" r="4" />
                                              </svg>
                                            </div>
                                          )} */}
                                            <span className="font-medium">{opt}</span>
                                          </div>
                                        );
                                      })}
                                  </div>
                                </div>
                              </div>
                            )}
                        </>
                      )}
                    </div>
                  </div>
                ))}

                <div className="flex items-center justify-between pt-2">
                  <button
                    data-dropdown="action"
                    type="button"
                    className="flex items-center cursor-pointer gap-1 text-[#3F73F6] px-[16px] py-[9px] rounded-[12px] border border-[#C7D2FE] hover:bg-[#EEF2FF]"
                    onClick={handleAddFilter}
                  >
                    <Plus size={20} />
                    <span className="text-sm font-medium">Add filter</span>
                  </button>

                  <button
                    data-dropdown="action"
                    type="button"
                    className="px-[16px] py-[10px] cursor-pointer rounded-[12px] bg-[#3F73F6] hover:bg-[#305ED2] text-white text-sm font-medium"
                    onClick={applyFilters}
                  >
                    Apply
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {showColumns && (
        <div className="relative" ref={dropdownRef}>
          <TableActionButton
            label={
              <div className="flex items-center gap-2">
                <span className="leading-[20px]">Columns</span>
                <span className="bg-[#3F73F6] text-white text-xs px-1.5 py-0.5 rounded-full min-w-[18px] h-[18px] flex items-center justify-center">
                  {selectedCount}
                </span>
              </div>
            }
            icon={
              <Image
                src="/assets/chevron-down.svg"
                alt="Chevron Down"
                width={20}
                height={20}
                className="w-5 h-5"
              />
            }
            onClick={() => {
              setIsColumnsOpen(v => {
                // Reset search when opening the dropdown
                if (!v) {
                  setColumnsSearch('');
                }
                return !v;
              });
            }}
            variant="secondary"
            // width="w-[140px]"
            className={`hover:text-[#2A2E34] hover:[&_img]:brightness-0 hover:[&_img]:invert-[0.15] ${
              isColumnsOpen
                ? 'ring-1 ring-[#3F73F6] border-transparent focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)]'
                : ''
            }`}
          />

          {isColumnsOpen && (
            <div className="absolute right-0 mt-1 w-[277px] rounded-[12px] border border-[#E5E7EB] bg-white shadow-xl z-[99999]">
              <div className="px-[16px] pt-[16px] pb-[12px]">
                <div className="flex h-[36px] items-center gap-2 px-[12px] py-[8px] rounded-[4px] border border-[#DCE2EB]">
                  <Image src="/assets/search.svg" alt="Search" width={20} height={20} />
                  <input
                    value={columnsSearch}
                    onChange={e => setColumnsSearch(e.target.value)}
                    placeholder="Search columns"
                    className="bg-transparent outline-none text-[14px] leading-[20px] font-regular text-[#5F6F84] w-full"
                  />
                </div>
              </div>

              <div className="max-h-[360px] overflow-y-auto bg-white border-[#DCE2EB] [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100 text-base py-2">
                {!isSearching && (
                  <>
                    <div className="group flex items-center gap-[10px] px-[16px] py-2 mb-1 hover:bg-[#D9E3FD]">
                      <CustomCheckbox
                        id="columns-all"
                        checked={isAllChecked}
                        onChange={e => toggleAll(e.target.checked)}
                      />
                      <span className="text-sm text-[#2A2E34] group-hover:text-[#3F73F6]">All</span>
                    </div>
                    <div className="h-px bg-[#E5E7EB] mx-4" />
                  </>
                )}

                {filteredFields.map(field => (
                  <div
                    key={field.name}
                    className="group flex items-center gap-[10px] px-4 py-2 mt-1 mb-1 hover:bg-[#D9E3FD]"
                  >
                    <CustomCheckbox
                      id={`col-${field.name}`}
                      checked={field.isShowing}
                      onChange={e => toggleSingle(field.name, e.target.checked)}
                    />
                    <span className="text-sm text-[#2A2E34] group-hover:text-[#3F73F6]">
                      {field.name}
                    </span>
                  </div>
                ))}
              </div>

              <div className="p-[16px] flex justify-end">
                <button
                  className="px-4 py-2 rounded-[4px] bg-[#3F73F6] hover:bg-[#305ED2] text-white text-[14px] leading-[20px] font-medium cursor-pointer"
                  onClick={handleApply}
                >
                  Apply
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {showSaveView && (
        <TableActionButton
          label="Save view"
          className="hover:bg-gray-50"
          icon={
            <Image
              aria-hidden
              src="/assets/save.svg"
              alt="File icon"
              width={20}
              height={20}
              className={disableSaveView ? 'opacity-100 grayscale' : ''}
            />
          }
          onClick={disableSaveView ? undefined : onSaveView}
          variant="outline"
          // width="w-[134px]"
          disabled={disableSaveView}
        />
      )}

      {/* DateTimeModal for date selection */}
      <DateTimeModal
        isOpen={isDateModalOpen}
        onClose={handleDateModalClose}
        onSave={handleDateSelect}
        initialDate={(() => {
          // Only calculate initialDate when modal is actually open
          if (!isDateModalOpen || !activeDateFilterId) {
            return '';
          }

          // Get the current filter value if it exists
          const currentFilter = filters.find(f => f.id === activeDateFilterId);
          const existingValue = currentFilter?.value;

          // If we have an existing value and it's a range (contains |), return it as is
          if (existingValue && currentFilter?.operator === 'between') {
            return existingValue; // This will be the pipe-separated range: "start|end"
          }

          // If we have a single date value, return it
          if (existingValue && currentFilter?.operator !== 'between') {
            return existingValue;
          }

          // Default to today's date if no existing value
          const today = new Date();
          const month = (today.getMonth() + 1).toString().padStart(2, '0');
          const day = today.getDate().toString().padStart(2, '0');
          const year = today.getFullYear().toString();
          const todaysDate = `${month}/${day}/${year}`;
          return todaysDate;
        })()}
        initialTime=""
        showTime={false}
        isRangePicker={
          activeDateFilterId
            ? filters.find(f => f.id === activeDateFilterId)?.operator === 'between'
            : false
        }
        _currentOperator={
          activeDateFilterId ? filters.find(f => f.id === activeDateFilterId)?.operator || '' : ''
        }
        position={dateModalPosition}
      />

      {/* Field dropdown portal */}
      {openFieldDropdownId &&
        typeof window !== 'undefined' &&
        createPortal(
          <ul
            data-portal="field-dropdown"
            className={`fixed max-h-[270px] py-2 overflow-y-auto [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100 rounded-[12px] border border-[#E5E7EB] bg-white shadow-md overflow-hidden z-[99999] ${fieldDropdownPosition.openAbove ? 'shadow-lg' : 'shadow-md'}`}
            style={{
              top: `${fieldDropdownPosition.top}px`,
              left: `${fieldDropdownPosition.left}px`,
              width: `${fieldDropdownPosition.width}px`,
              transform: fieldDropdownPosition.openAbove ? 'translateY(-100%)' : 'none',
            }}
            onClick={e => e.stopPropagation()}
          >
            {fieldOptions.map(opt => (
              <li
                key={opt}
                onClick={e => {
                  e.stopPropagation();
                  handleFilterChange(openFieldDropdownId, 'field', opt);
                  setOpenFieldDropdownId(null);
                }}
                className="px-4 py-2 mb-1 text-sm text-[#2A2E34] hover:bg-[#D9E3FD] hover:text-[#3F73F6] cursor-pointer border-[#F3F4F6] last:border-0"
              >
                {opt}
              </li>
            ))}
          </ul>,
          document.body
        )}

      {/* Operator dropdown portal */}
      {openOperatorDropdownId &&
        typeof window !== 'undefined' &&
        createPortal(
          <ul
            data-portal="operator-dropdown"
            className={`fixed max-h-[270px] py-2 overflow-y-auto [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100 rounded-[12px] border border-[#E5E7EB] bg-white shadow-md overflow-hidden z-[99999] ${operatorDropdownPosition.openAbove ? 'shadow-lg' : 'shadow-md'}`}
            style={{
              top: `${operatorDropdownPosition.top}px`,
              left: `${operatorDropdownPosition.left}px`,
              width: `${operatorDropdownPosition.width}px`,
              transform: operatorDropdownPosition.openAbove ? 'translateY(-100%)' : 'none',
            }}
            onClick={e => e.stopPropagation()}
          >
            {operatorOptionsForField(
              filters.find(f => f.id === openOperatorDropdownId)?.field || ''
            ).map(opt => (
              <li
                key={opt.value || 'empty'}
                onClick={e => {
                  e.stopPropagation();
                  handleFilterChange(openOperatorDropdownId, 'operator', opt.value);
                  setOpenOperatorDropdownId(null);
                }}
                className="px-4 py-2 mb-1 text-sm text-[#5F6F84] hover:bg-[#D9E3FD] hover:text-[#3F73F6] cursor-pointer border-[#F3F4F6] last:border-0"
              >
                {opt.label}
              </li>
            ))}
          </ul>,
          document.body
        )}

      {/* Value dropdown portal */}
      {openValueDropdownId &&
        typeof window !== 'undefined' &&
        (() => {
          const currentFilter = filters.find(f => f.id === openValueDropdownId);
          return (
            currentFilter &&
            !isMatterField(currentFilter.field) &&
            !isTemplatesField(currentFilter.field)
          );
        })() &&
        createPortal(
          <div
            data-portal="value-dropdown"
            className={`fixed overflow-y-auto [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100 bg-white border border-[#E5E7EB] rounded-[12px] w-[280px] shadow-2xl z-[99999] ${valueDropdownPosition.openAbove ? 'shadow-lg' : 'shadow-md'}`}
            style={{
              top: `${valueDropdownPosition.top}px`,
              left: `${valueDropdownPosition.left}px`,
              width: `${valueDropdownPosition.width}px`,
              transform: valueDropdownPosition.openAbove ? 'translateY(-100%)' : 'none',
            }}
            onClick={e => e.stopPropagation()}
          >
            {/* Search input section */}
            <div className="py-4 px-4">
              <div className="relative">
                <svg
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-[#5F6F84]"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="11" cy="11" r="8" />
                  <path d="m21 21-4.35-4.35" />
                </svg>
                <input
                  data-dropdown="search"
                  className="h-[36px] w-full rounded-[4px] border border-[#DCE2EB] pl-10 pr-[12px] text-[14px] text-[#2A2E34] placeholder-[#5F6F84] focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)]"
                  placeholder="Type a name"
                  value={filterSearchValues[openValueDropdownId] || ''}
                  onChange={e =>
                    setFilterSearchValues(prev => ({
                      ...prev,
                      [openValueDropdownId]: e.target.value,
                    }))
                  }
                />
                {filterSearchValues[openValueDropdownId] && (
                  <button
                    type="button"
                    onClick={e => {
                      e.stopPropagation();
                      setFilterSearchValues(prev => ({
                        ...prev,
                        [openValueDropdownId]: '',
                      }));
                    }}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-[#9CA3AF] hover:text-[#6B7280] transition-colors"
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <line x1="18" y1="6" x2="6" y2="18" />
                      <line x1="6" y1="6" x2="18" y2="18" />
                    </svg>
                  </button>
                )}
              </div>
            </div>

            <div className="overflow-y-auto max-h-[300px] [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100">
              {/* Selected items section */}
              {(() => {
                const currentFilter = filters.find(f => f.id === openValueDropdownId);
                return (
                  currentFilter?.selectedValues &&
                  currentFilter.selectedValues.length > 0 && (
                    <div>
                      <div className="pb-[8px]">
                        <div className="mb-[2px]">
                          <div className="px-4 pb-2 text-sm font-medium text-[#2A2E34] py-[8px] mb-[4px]">
                            {currentFilter.field}
                          </div>
                        </div>
                        <div className="space-y-1">
                          {currentFilter.selectedValues.map(selectedValue => {
                            // Get the full display name for matter fields
                            const displayValue = getMatterDisplayName(
                              selectedValue,
                              currentFilter.field
                            );
                            return (
                              <div
                                key={selectedValue}
                                className="flex items-center justify-between py-2 px-4 rounded-[6px]"
                              >
                                <div className="flex items-center gap-2 flex-1 min-w-0">
                                  <div className="w-[30px] h-[30px] rounded-full bg-[#3F73F6] flex items-center justify-center flex-shrink-0">
                                    <span className="text-white text-xs font-medium">
                                      {displayValue
                                        .split(' ')
                                        .map(n => n[0])
                                        .join('')
                                        .substring(0, 2)
                                        .toUpperCase()}
                                    </span>
                                  </div>
                                  <span className="text-[#2A2E34] text-[14px] leading-[20px] truncate">
                                    {displayValue}
                                  </span>
                                </div>
                                <button
                                  type="button"
                                  onClick={e => {
                                    e.stopPropagation();
                                    if (isAssigneeField(currentFilter.field)) {
                                      handleAssigneeSelection(openValueDropdownId, selectedValue);
                                    } else if (isNameField(currentFilter.field)) {
                                      handleAssigneeSelection(openValueDropdownId, selectedValue);
                                    } else if (isAttorneyField(currentFilter.field)) {
                                      handleAttorneySelection(openValueDropdownId, selectedValue);
                                    } else if (isContactField(currentFilter.field)) {
                                      handleContactSelection(openValueDropdownId, selectedValue);
                                    } else if (isMatterField(currentFilter.field)) {
                                      handleMatterSelection(openValueDropdownId, selectedValue);
                                    } else if (isTemplatesField(currentFilter.field)) {
                                      handleTemplatesSelection(openValueDropdownId, selectedValue);
                                    }
                                  }}
                                  className="text-[#5F6F84] hover:text-[#2A2E34] transition-colors"
                                >
                                  <X size={20} />
                                </button>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                      <div className="border-b border-[#E5E7EB] mx-[16px]"></div>
                    </div>
                  )
                );
              })()}

              {/* Suggestions section */}
              <div className="px-4 pt-[8px] pb-[4px]">
                <h4 className="text-sm py-1 font-medium text-[#2A2E34] mb-1">Suggestions</h4>
              </div>
              <div className="space-y-1">
                {(() => {
                  const currentFilter = filters.find(f => f.id === openValueDropdownId);
                  const selectedValuesForField = getAllSelectedValuesForField(currentFilter?.field || '');
                  return (fieldValueOptions[currentFilter?.field || ''] || [])
                    .filter(opt => {
                      // Filter by search text
                      const matchesSearch = opt
                        .toLowerCase()
                        .includes((filterSearchValues[openValueDropdownId] || '').toLowerCase());
                      
                      // Exclude already selected values for this field type
                      return matchesSearch && !selectedValuesForField.includes(opt);
                    })
                    .slice(0, 50)
                    .map((opt, _index) => {
                      const isSelected = isMatterField(currentFilter?.field || '')
                        ? currentFilter?.selectedValues?.includes(opt) || false
                        : currentFilter?.selectedValues?.includes(opt) || false;
                      return (
                        <div
                          key={opt}
                          className={`flex items-center gap-2 px-4 py-2 hover:bg-[#D9E3FD] cursor-pointer text-sm transition-colors group ${
                            isSelected ? 'bg-[#D9E3FD]' : ''
                          }`}
                          onClick={e => {
                            e.stopPropagation();
                            if (isAssigneeField(currentFilter?.field || '')) {
                              handleAssigneeSelection(openValueDropdownId, opt);
                            } else if (isNameField(currentFilter?.field || '')) {
                              handleAssigneeSelection(openValueDropdownId, opt);
                            } else if (isAttorneyField(currentFilter?.field || '')) {
                              handleAttorneySelection(openValueDropdownId, opt);
                            } else if (isContactField(currentFilter?.field || '')) {
                              handleContactSelection(openValueDropdownId, opt);
                            } else if (isMatterField(currentFilter?.field || '')) {
                              handleMatterSelection(openValueDropdownId, opt);
                            } else if (isTemplatesField(currentFilter?.field || '')) {
                              handleTemplatesSelection(openValueDropdownId, opt);
                            }
                          }}
                        >
                          <div className="w-[30px] h-[30px] rounded-full bg-[#5F6F84] flex items-center justify-center flex-shrink-0">
                            <span className="text-white text-xs font-medium">
                              {opt
                                .split(' ')
                                .map(n => n[0])
                                .join('')
                                .substring(0, 2)
                                .toUpperCase()}
                            </span>
                          </div>
                          <span
                            className={`font-regular flex-1 min-w-0 truncate group-hover:text-[#3F73F6] transition-colors ${isSelected ? 'text-[#3F73F6]' : 'text-[#2A2E34]'}`}
                          >
                            {opt}
                          </span>
                          {isSelected && (
                            <div className="w-5 h-5 rounded-full bg-[#3F73F6] flex items-center justify-center flex-shrink-0">
                              <svg
                                width="12"
                                height="12"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-white"
                              >
                                <polyline points="20,6 9,17 4,12" />
                              </svg>
                            </div>
                          )}
                        </div>
                      );
                    });
                })()}
              </div>
            </div>
          </div>,
          document.body
        )}

      {/* Status dropdown portal */}
      {openStatusDropdownId &&
        typeof window !== 'undefined' &&
        createPortal(
          <div
            data-portal="status-dropdown"
            className={`fixed overflow-y-auto [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100 bg-white border border-[#E5E7EB] rounded-[16px] max-h-80 shadow-2xl z-[99999] ${statusDropdownPosition.openAbove ? 'shadow-lg' : 'shadow-md'}`}
            style={{
              top: `${statusDropdownPosition.top}px`,
              left: `${statusDropdownPosition.left}px`,
              width: `${statusDropdownPosition.width}px`,
              transform: statusDropdownPosition.openAbove ? 'translateY(-100%)' : 'none',
            }}
            onClick={e => e.stopPropagation()}
          >
            <div className="p-4">
              <div
                className="flex flex-col gap-4 overflow-x-auto pb-2 max-h-60"
                style={{
                  minWidth: 'max-content',
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#D1D5DB #F3F4F6',
                }}
              >
                {(() => {
                  const currentFilter = filters.find(f => f.id === openStatusDropdownId);
                  if (!currentFilter) return [];
                  return (
                    fieldValueOptions[currentFilter.field]?.length
                      ? fieldValueOptions[currentFilter.field]
                      : ['On Track', 'Due Soon', 'Overdue', 'Completed']
                  ).map((opt, _index) => {
                    const isSelected = currentFilter?.selectedValues?.includes(opt) || false;
                    return (
                      <div
                        key={opt}
                        className="flex items-center gap-3 cursor-pointer flex-shrink-0 min-w-fit"
                        onClick={e => {
                          e.stopPropagation();
                          handleStatusSelection(openStatusDropdownId, opt);
                        }}
                      >
                        <div className="relative">
                          <div
                            className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                              isSelected
                                ? 'bg-blue-600 border-blue-600'
                                : 'bg-white border-gray-300 hover:border-gray-400'
                            }`}
                          >
                            {isSelected && (
                              <svg
                                width="12"
                                height="12"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="3"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-white"
                              >
                                <polyline points="20,6 9,17 4,12" />
                              </svg>
                            )}
                          </div>
                        </div>
                        <span
                          className={`px-[16px] py-[4px] rounded-full text-[12px] leading-[16px] font-regular whitespace-nowrap ${
                            opt === 'On Track' || opt === 'Current'
                              ? 'bg-[#8CF1BD] text-[#2A2E34]'
                              : opt === 'Due Soon'
                                ? 'bg-[#FFDB93] text-[#2A2E34]'
                                : opt === 'Overdue'
                                  ? 'bg-[#EF8B8B] text-[#2A2E34]'
                                  : opt === 'Completed'
                                    ? 'bg-[#97C7FF] text-[#2A2E34]'
                                    : 'bg-gray-500 text-white'
                          }`}
                        >
                          {opt}
                        </span>
                      </div>
                    );
                  });
                })()}
              </div>
            </div>
          </div>,
          document.body
        )}

      {/* Matter dropdown portal */}
      {openValueDropdownId &&
        typeof window !== 'undefined' &&
        (() => {
          const currentFilter = filters.find(f => f.id === openValueDropdownId);
          return currentFilter && isMatterField(currentFilter.field);
        })() &&
        createPortal(
          <div
            data-portal="matter-dropdown"
            className={`fixed overflow-y-auto [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100 bg-white border border-[#E5E7EB] rounded-[12px] shadow-lg z-[99999] ${valueDropdownPosition.openAbove ? 'shadow-lg' : 'shadow-md'}`}
            style={{
              top: `${valueDropdownPosition.top}px`,
              left: `${valueDropdownPosition.left}px`,
              width: `${valueDropdownPosition.width}px`,
              transform: valueDropdownPosition.openAbove ? 'translateY(-100%)' : 'none',
            }}
            onClick={e => e.stopPropagation()}
            onMouseDown={e => e.stopPropagation()}
          >
            {/* Search input section */}
            <div className="p-4">
              <div className="relative">
                <input
                  data-dropdown="search"
                  className="h-10 w-full rounded-[4px] border border-[#3F73F6] pl-10 pr-10 text-sm text-[#2A2E34] placeholder-[#5F6F84] focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)]"
                  placeholder="Search"
                  value={filterSearchValues[openValueDropdownId] || ''}
                  onChange={e =>
                    setFilterSearchValues(prev => ({
                      ...prev,
                      [openValueDropdownId]: e.target.value,
                    }))
                  }
                />
                <div className="absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#9AA4B2]"
                  >
                    <circle cx="11" cy="11" r="8" />
                    <path d="m21 21-4.35-4.35" />
                  </svg>
                </div>
                {filterSearchValues[openValueDropdownId] && (
                  <button
                    type="button"
                    onClick={e => {
                      e.stopPropagation();
                      setFilterSearchValues(prev => ({
                        ...prev,
                        [openValueDropdownId]: '',
                      }));
                    }}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-[#9AA4B2] hover:text-[#6B7280] transition-colors"
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <line x1="18" y1="6" x2="6" y2="18" />
                      <line x1="6" y1="6" x2="18" y2="18" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
            <div className="overflow-y-auto [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100 max-h-70">
              {/* Selected items section */}
              {(() => {
                const currentFilter = filters.find(f => f.id === openValueDropdownId);
                return (
                  currentFilter?.selectedValues &&
                  currentFilter.selectedValues.length > 0 && (
                    <div className="selected-items">
                      <div className="px-4 py-2">
                        <div className="text-[14px] leading-[20px] font-semibold text-[#2A2E34]">
                          Selected
                        </div>
                      </div>
                      <div className="space-y-1">
                        {currentFilter.selectedValues.map(selectedValue => {
                          // Get the full display name for matter fields
                          const displayValue = getMatterDisplayName(
                            selectedValue,
                            currentFilter.field
                          );
                          return (
                            <div
                              key={selectedValue}
                              className="flex px-4 py-2 items-center justify-between text-[14px] leading-[20px]"
                            >
                              <span className="text-[#2A2E34] font-regular">{displayValue}</span>
                              <button
                                type="button"
                                onClick={e => {
                                  e.stopPropagation();
                                  handleMatterSelection(openValueDropdownId, selectedValue);
                                }}
                                className="text-[#9AA4B2] hover:text-[#6B7280] transition-colors"
                              >
                                <X size={20} />
                              </button>
                            </div>
                          );
                        })}
                      </div>
                      <div className="border-t border-[#E5E7EB] my-2 mx-4"></div>
                    </div>
                  )
                );
              })()}

              {/* Suggestions section */}
              <div className="mb-4">
                <div className="text-[14px] leading-[20px] font-semibold text-[#2A2E34] px-4 py-1">
                  Suggestions
                </div>
                <div className="space-y-1">
                  {(() => {
                    const currentFilter = filters.find(f => f.id === openValueDropdownId);
                    const selectedValuesForField = getAllSelectedValuesForField(currentFilter?.field || '');
                    return (fieldValueOptions[currentFilter?.field || ''] || [])
                      .filter(opt => {
                        // Filter by search text
                        const matchesSearch = opt
                          .toLowerCase()
                          .includes((filterSearchValues[openValueDropdownId] || '').toLowerCase());
                        
                        // Exclude already selected values for this field type
                        return matchesSearch && !selectedValuesForField.includes(opt);
                      })
                      .slice(0, 50)
                      .map((opt, _index) => {
                        // For matter fields, compare full values since selectedValues now stores full values
                        const isSelected = isMatterField(currentFilter?.field || '')
                          ? currentFilter?.selectedValues?.includes(opt) || false
                          : currentFilter?.selectedValues?.includes(opt) || false;
                        return (
                          <div
                            key={opt}
                            className={`flex items-center justify-between px-4 py-2 hover:bg-[#F3F4F6] cursor-pointer text-sm transition-colors ${
                              isSelected ? 'bg-[#EEF2FF]' : ''
                            }`}
                            onClick={e => {
                              e.stopPropagation();
                              handleMatterSelection(openValueDropdownId, opt);
                            }}
                          >
                            <span
                              className={`${isSelected ? 'text-[#3F73F6] font-medium' : 'text-[#2A2E34]'}`}
                            >
                              {opt}
                            </span>
                            {isSelected && (
                              <div className="w-5 h-5 rounded-full bg-[#3F73F6] flex items-center justify-center flex-shrink-0">
                                <svg
                                  width="12"
                                  height="12"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="text-white"
                                >
                                  <polyline points="20,6 9,17 4,12" />
                                </svg>
                              </div>
                            )}
                          </div>
                        );
                      });
                  })()}
                </div>
              </div>
            </div>
          </div>,
          document.body
        )}

      {/* Template dropdown portal */}
      {openValueDropdownId &&
        typeof window !== 'undefined' &&
        (() => {
          const currentFilter = filters.find(f => f.id === openValueDropdownId);
          return currentFilter && isTemplatesField(currentFilter.field);
        })() &&
        createPortal(
          <div
            data-portal="template-dropdown"
            className={`fixed bg-white border border-[#E5E7EB] rounded-[12px] shadow-lg z-[99999] ${valueDropdownPosition.openAbove ? 'shadow-lg' : 'shadow-md'}`}
            style={{
              top: `${valueDropdownPosition.top}px`,
              left: `${valueDropdownPosition.left}px`,
              width: `${valueDropdownPosition.width}px`,
              transform: valueDropdownPosition.openAbove ? 'translateY(-100%)' : 'none',
            }}
            onClick={e => e.stopPropagation()}
            onMouseDown={e => e.stopPropagation()}
          >
            {/* Search input section */}
            <div className="p-4">
              <div className="relative">
                <input
                  data-dropdown="search"
                  className="h-10 w-full rounded-[4px] border border-[#3F73F6] pl-10 pr-10 text-sm text-[#2A2E34] placeholder-[#5F6F84] focus:outline-none focus:border-[#3F73F6] focus:shadow-[0_0_0_2px_rgba(63,115,246,0.2)]"
                  placeholder="Search"
                  value={filterSearchValues[openValueDropdownId] || ''}
                  onChange={e =>
                    setFilterSearchValues(prev => ({
                      ...prev,
                      [openValueDropdownId]: e.target.value,
                    }))
                  }
                />
                <div className="absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#9AA4B2]"
                  >
                    <circle cx="11" cy="11" r="8" />
                    <path d="m21 21-4.35-4.35" />
                  </svg>
                </div>
                {filterSearchValues[openValueDropdownId] && (
                  <button
                    type="button"
                    onClick={e => {
                      e.stopPropagation();
                      setFilterSearchValues(prev => ({
                        ...prev,
                        [openValueDropdownId]: '',
                      }));
                    }}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-[#9AA4B2] hover:text-[#6B7280] transition-colors"
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <line x1="18" y1="6" x2="6" y2="18" />
                      <line x1="6" y1="6" x2="18" y2="18" />
                    </svg>
                  </button>
                )}
              </div>
            </div>

            {/* Selected items section */}
            {(() => {
              const currentFilter = filters.find(f => f.id === openValueDropdownId);
              return (
                currentFilter?.selectedValues &&
                currentFilter.selectedValues.length > 0 && (
                  <div className="selected-items">
                    <div className="px-4 py-2">
                      <div className="text-[14px] leading-[20px] font-semibold text-[#2A2E34]">
                        Selected
                      </div>
                    </div>
                    <div className="space-y-1">
                      {currentFilter.selectedValues.map(selectedValue => (
                        <div
                          key={selectedValue}
                          className="flex px-4 py-2 items-center justify-between text-[14px] leading-[20px]"
                        >
                          <span className="text-[#2A2E34] font-regular">{selectedValue}</span>
                          <button
                            type="button"
                            onClick={e => {
                              e.stopPropagation();
                              handleTemplatesSelection(openValueDropdownId, selectedValue);
                            }}
                            className="text-[#9AA4B2] hover:text-[#6B7280] transition-colors"
                          >
                            <X size={20} />
                          </button>
                        </div>
                      ))}
                    </div>
                    <div className="border-t border-[#E5E7EB] my-2 mx-4"></div>
                  </div>
                )
              );
            })()}

            {/* Suggestions section */}
            <div className="mb-4">
              <div className="text-[14px] leading-[20px] font-semibold text-[#2A2E34] px-4 py-1">
                Suggestions
              </div>
              <div className="space-y-1">
                {(() => {
                  const currentFilter = filters.find(f => f.id === openValueDropdownId);
                  const selectedValuesForField = getAllSelectedValuesForField(currentFilter?.field || '');
                  return (
                    fieldValueOptions[currentFilter?.field || '']?.length
                      ? fieldValueOptions[currentFilter?.field || '']
                      : getTemplateOptions()
                  )
                    .filter(opt => {
                      // Filter by search text
                      const matchesSearch = opt
                        .toLowerCase()
                        .includes((filterSearchValues[openValueDropdownId] || '').toLowerCase());
                      
                      // Exclude already selected values for this field type
                      return matchesSearch && !selectedValuesForField.includes(opt);
                    })
                    .slice(0, 50)
                    .map((opt, _index) => {
                      const isSelected = currentFilter?.selectedValues?.includes(opt) || false;
                      return (
                        <div
                          key={opt}
                          className={`flex items-center justify-between px-4 py-2 hover:bg-[#F3F4F6] cursor-pointer text-sm transition-colors ${
                            isSelected ? 'bg-[#EEF2FF]' : ''
                          }`}
                          onClick={e => {
                            e.stopPropagation();
                            handleTemplatesSelection(openValueDropdownId, opt);
                          }}
                        >
                          <span
                            className={`${isSelected ? 'text-[#3F73F6] font-medium' : 'text-[#2A2E34]'}`}
                          >
                            {opt}
                          </span>
                          {isSelected && (
                            <div className="w-5 h-5 rounded-full bg-[#3F73F6] flex items-center justify-center flex-shrink-0">
                              <svg
                                width="12"
                                height="12"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-white"
                              >
                                <polyline points="20,6 9,17 4,12" />
                              </svg>
                            </div>
                          )}
                        </div>
                      );
                    });
                })()}
              </div>
            </div>
          </div>,
          document.body
        )}
    </div>
  );
};

export default TableActions;
