import React, { ReactNode } from 'react';
import Button from './Button';

interface TableActionButtonProps {
  label: string | ReactNode;
  icon: ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  width?: string;
  className?: string;
  disabled?: boolean;
  children?: React.ReactNode;
}

const TableActionButton = React.forwardRef<HTMLButtonElement, TableActionButtonProps>(({
  label,
  icon,
  onClick,
  variant = 'secondary',
  width = 'w-auto',
  className = '',
  disabled = false,
  children,
}, ref) => {
  return (
    <>
      {/* <Button variant={variant} width={width} icon={icon} onClick={onClick} className={className}> */}
      <Button ref={ref} variant={variant} width={width} icon={icon} onClick={onClick} disabled={disabled} className={className}>
        {children || label}
      </Button>
    </>
  );
});

TableActionButton.displayName = 'TableActionButton';

export default TableActionButton;
