import React from 'react';
import Image from 'next/image';

interface NoDataFoundProps {
  /**
   * Custom message to display when no data is found
   */
  message?: string;
  /**
   * Custom icon/image path to display
   */
  iconPath?: string;
  /**
   * Additional CSS classes for styling
   */
  className?: string;
  /**
   * Show a secondary description below the main message
   */
  description?: string;
}

/**
 * NoDataFound component displays a centered message with an icon when no data matches the current filters
 * 
 * @param message - The main message to display (default: "No data matches this filter")
 * @param iconPath - Path to the icon/image to display (default: search icon)
 * @param className - Additional CSS classes for custom styling
 * @param description - Optional secondary description text
 */
const NoDataFound: React.FC<NoDataFoundProps> = ({
  message = "No data matches this filter",
  iconPath = "/assets/search.svg",
  className = "",
  description
}) => {
  return (
    <div className={`flex flex-col items-center justify-center h-full min-h-[400px] text-center px-6 ${className}`}>
      <div className="flex flex-col items-center space-y-4">
        {/* Icon/Image */}
        <div className="w-16 h-16 flex items-center justify-center">
          <Image
            src={iconPath}
            alt="No data found"
            width={64}
            height={64}
            className="opacity-60"
          />
        </div>
        
        {/* Main message */}
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-[#2A2E34]">
            {message}
          </h3>
          
          {description && (
            <p className="text-sm text-[#5F6F84] max-w-md">
              {description}
            </p>
          )}
        </div>
        
        {/* Subtle suggestion */}
        <div className="mt-2">
          <p className="text-xs text-[#A2AFC2]">
            Try adjusting your filters or check back later
          </p>
        </div>
      </div>
    </div>
  );
};

export default NoDataFound;
