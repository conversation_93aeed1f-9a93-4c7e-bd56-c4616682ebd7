import { useRouter } from 'next/router';
import { useEffect, useState, ReactNode } from 'react';
import { useAppSelector, useAppDispatch } from '@/hooks';
import { resetMFA, setMFAVerified } from '@/features/user/userSlice';
import { secureAuthToken } from '@/utils';

// Public paths that don't require authentication
const publicPaths = [
  '/',
  '/signin',
  '/signup',
  '/auth/resetpassword',
  '/auth/accountlocked',
  '/auth/verifymfa',
  '/auth/setpassword',
  '/auth/forgotpassword',
];

interface RouteGuardProps {
  children: ReactNode;
}

/**
 * Component that guards routes based on authentication status
 */
export default function RouteGuard({ children }: RouteGuardProps) {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { isMFAVerified } = useAppSelector(state => state.user);
  const [authorized, setAuthorized] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    setIsClient(true);

    // Restore MFA verification state from localStorage on page refresh BEFORE auth check
    if (typeof window !== 'undefined') {
      const mfaVerified = localStorage.getItem('mfaVerified');
      if (mfaVerified === 'true') {
        dispatch(setMFAVerified(true));
      }
      setIsInitialized(true);
    }

    // Auth check function
    const authCheck = (url: string) => {
      // Get token from secure storage - only runs client-side
      const token = secureAuthToken.get();

      // Check if the path is public or starts with a public path
      const isPublicPath = publicPaths.some(
        path =>
          url === path ||
          url.startsWith(path + '/') ||
          (path.includes('/auth/resetpassword') && url.startsWith('/auth/resetpassword'))
      );

      const normalizedToken = token ? token.replace(/^"+|"+$/g, '') : '';
      const isTokenInvalid =
        !normalizedToken ||
        normalizedToken === 'null' ||
        normalizedToken === 'undefined' ||
        normalizedToken.trim() === '';

      const logoutAndRedirect = () => {
        setAuthorized(false);
        if (typeof window !== 'undefined') {
          secureAuthToken.remove();
          localStorage.removeItem('user');
          localStorage.removeItem('reviewTasks');
          localStorage.removeItem('isUpdated');
          localStorage.removeItem('isEventAdded');
          localStorage.removeItem('selectedMatters');
          localStorage.removeItem('timezone');
          Object.keys(localStorage).forEach(key => {
            if (key.startsWith('isEventAdded_')) {
              localStorage.removeItem(key);
            }
          });
          localStorage.removeItem('selectedMatterMyMatterId');
          localStorage.removeItem('SelectedEventMatterId');
          localStorage.removeItem('matterList');
          localStorage.removeItem('mfaVerified'); // Clear MFA verification flag
        }
        // Reset MFA state in Redux
        dispatch(resetMFA());
        router.push('/signin');
      };

      // If token is missing/invalid, block protected pages and logout
      if (isTokenInvalid) {
        if (!isPublicPath) {
          logoutAndRedirect();
        } else {
          setAuthorized(true);
        }
        return;
      }

      if (normalizedToken && isMFAVerified) {
        if (isPublicPath) {
          setAuthorized(false);
          router.push('/all-work-flow?type=new-court-notice'); // Redirect to dashboard if logged in and trying to access public page
          return;
        }

        if (!isMFAVerified) {
          setAuthorized(false);
          return;
        }

        setAuthorized(true); // Allow access to protected routes if MFA is verified
      } else {
        if (!isPublicPath) {
          setAuthorized(false);
          router.push('/signin'); // Redirect unauthenticated users to sign-in
        } else {
          setAuthorized(true); // Allow public paths
        }
      }
    };

    // Only run auth check after initialization is complete
    if (isInitialized) {
      authCheck(router.asPath);
    }

    // Listen for route changes
    const hideContent = () => setAuthorized(false);
    router.events.on('routeChangeStart', hideContent);

    // Run auth check on route change
    router.events.on('routeChangeComplete', authCheck);

    // Cleanup event listeners
    return () => {
      router.events.off('routeChangeStart', hideContent);
      router.events.off('routeChangeComplete', authCheck);
    };
  }, [router, isMFAVerified, dispatch, isInitialized]);

  // During SSR, don't block rendering, but protect client-side
  if (!isClient || !isInitialized) {
    return <>{children}</>;
  }

  return authorized ? <>{children}</> : null;
}
