import React from 'react';

interface ClientEmailAlertModalProps {
    isOpen: boolean;
    onClose: () => void;
}

const ClientEmailAlertModal: React.FC<ClientEmailAlertModalProps> = ({ isOpen, onClose }) => {
    if (!isOpen) return null;
    return (
        <div className="fixed inset-0 flex items-center justify-center z-50" style={{ backgroundColor: 'rgba(128, 128, 128, 0.4)' }}>
            <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-8 flex flex-col items-center relative">
                <div className="flex flex-col items-center mb-4">
                    <div className="w-12 h-12 flex items-center justify-center  mb-2">
                        <img src="/info-circle.svg" alt="Alert" height={48} width={48} />
                    </div>
                    <h2 className="text-lg font-semibold text-gray-900 mb-2">Alert!</h2>
                    <p className="text-center text-gray-600 text-base">
                        Email address not available in Clio.<br />
                        Please add client’s email in Clio manually to invite.
                    </p>
                </div>
                <button
                    className="mt-4 px-8 w-full py-2 border cursor-pointer border-[#3F73F6] text-[#3F73F6] rounded-[12px] font-medium hover:bg-blue-50 focus:outline-none"
                    onClick={onClose}
                >
                    Okay
                </button>
            </div>
        </div>
    );
};

export default ClientEmailAlertModal; 