import React from 'react';
import { X } from 'lucide-react';

interface ReusableAlertModalProps {
  isOpen: boolean;
  message: string;
  title?: string;
  onOk?: () => void;
  onCancel?: () => void;
  okText?: string;
  cancelText?: string;
}

const ReusableAlertModal: React.FC<ReusableAlertModalProps> = ({
  isOpen,
  message,
  title = 'Alert',
  onOk,
  onCancel,
  okText = 'OK',
  cancelText = 'Cancel',
}) => {
  if (!isOpen) return null;

  const handleOk = () => {
    if (onOk) onOk();
  };

  const handleCancel = () => {
    if (onCancel) onCancel();
  };

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50"
      style={{ backgroundColor: 'rgba(128, 128, 128, 0.3)' }}
    >
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-5">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-[18px] font-medium text-gray-900">{title}</h2>
          {onCancel && (
            <button
              onClick={handleCancel}
              className="flex items-center  justify-center text-gray-500 w-[30px] h-[30px] bg-[#F3F5F9] rounded-[8px] cursor-pointer hover:text-gray-700"
            >
              <X size={20} />
            </button>
          )}
        </div>

        <div className="mb-6">
          <p className="text-[14px] text-[#2a2e34]">{message}</p>
        </div>

        <div className="flex justify-end space-x-2">
          {onCancel && (
            <button
              onClick={handleCancel}
              className="h-[40px] px-[16px] py-[10px] text-[14px] leading-[20px] border border-[#DCE2EB] cursor-pointer text-[#2a2e34] rounded-[12px] bg-white hover:bg-gray-50"
            >
              {cancelText}
            </button>
          )}
          <button
            onClick={handleOk}
            className="h-[40px] px-[16px] py-[10px] text-[14px] leading-[20px] cursor-pointer bg-[#3F73F6] text-white rounded-[12px] hover:bg-[#305ED2]"
          >
            {okText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReusableAlertModal;
