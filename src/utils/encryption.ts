/**
 * Encryption utility for localStorage values
 * Provides secure encryption/decryption for sensitive data like auth tokens
 */

// Simple encryption key - in production, this should be more secure
// Consider using environment variables or a more sophisticated key management system
const ENCRYPTION_KEY = 'FirmProfit-AuthToken-Key-2024';

/**
 * Simple XOR encryption/decryption function
 * This is a basic implementation - for production, consider using Web Crypto API
 * @param text - The text to encrypt/decrypt
 * @param key - The encryption key
 * @returns Encrypted/decrypted string
 */
function xorEncrypt(text: string, key: string): string {
  let result = '';
  for (let i = 0; i < text.length; i++) {
    result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
  }
  return btoa(result); // Base64 encode the result
}

/**
 * Simple XOR decryption function
 * @param encryptedText - The encrypted text to decrypt
 * @param key - The decryption key
 * @returns Decrypted string
 */
function xorDecrypt(encryptedText: string, key: string): string {
  try {
    const decoded = atob(encryptedText); // Base64 decode
    let result = '';
    for (let i = 0; i < decoded.length; i++) {
      result += String.fromCharCode(decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return result;
  } catch (error) {
    console.error('Decryption error:', error);
    return '';
  }
}

/**
 * Encrypts a value before storing in localStorage
 * @param value - The value to encrypt
 * @returns Encrypted string
 */
export function encryptValue(value: string): string {
  if (!value) return '';
  return xorEncrypt(value, ENCRYPTION_KEY);
}

/**
 * Decrypts a value retrieved from localStorage
 * @param encryptedValue - The encrypted value to decrypt
 * @returns Decrypted string
 */
export function decryptValue(encryptedValue: string): string {
  if (!encryptedValue) return '';
  return xorDecrypt(encryptedValue, ENCRYPTION_KEY);
}

/**
 * Secure localStorage wrapper for authToken
 * Handles encryption/decryption automatically
 */
export const secureAuthToken = {
  /**
   * Set encrypted authToken in localStorage
   * @param token - The auth token to store
   */
  set: (token: string): void => {
    if (typeof window === 'undefined') return;
    
    if (!token || token.trim() === '') {
      localStorage.removeItem('authToken');
      return;
    }
    
    try {
      const encryptedToken = encryptValue(token);
      localStorage.setItem('authToken', encryptedToken);
    } catch (error) {
      console.error('Error encrypting authToken:', error);
      // Fallback to storing unencrypted (for backward compatibility)
      localStorage.setItem('authToken', token);
    }
  },

  /**
   * Get and decrypt authToken from localStorage
   * @returns Decrypted auth token or null if not found/invalid
   */
  get: (): string | null => {
    if (typeof window === 'undefined') return null;
    
    try {
      const encryptedToken = localStorage.getItem('authToken');
      if (!encryptedToken) return null;
      
      const decryptedToken = decryptValue(encryptedToken);
      
      // Validate the decrypted token
      if (!decryptedToken || decryptedToken.trim() === '') {
        return null;
      }
      
      return decryptedToken;
    } catch (error) {
      console.error('Error decrypting authToken:', error);
      
      // Fallback: try to get unencrypted token (for backward compatibility)
      try {
        const fallbackToken = localStorage.getItem('authToken');
        return fallbackToken;
      } catch (fallbackError) {
        console.error('Error getting fallback authToken:', fallbackError);
        return null;
      }
    }
  },

  /**
   * Remove authToken from localStorage
   */
  remove: (): void => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem('authToken');
  },

  /**
   * Check if authToken exists and is valid
   * @returns boolean indicating if a valid token exists
   */
  exists: (): boolean => {
    const token = secureAuthToken.get();
    return !!(token && token.trim() !== '' && token !== 'null' && token !== 'undefined');
  }
};

/**
 * Migration function to encrypt existing unencrypted tokens
 * Call this once during app initialization to migrate existing tokens
 */
export function migrateExistingTokens(): void {
  if (typeof window === 'undefined') return;
  
  try {
    const existingToken = localStorage.getItem('authToken');
    if (existingToken) {
      // Check if token is already encrypted (base64 encoded)
      try {
        // Try to decrypt - if it fails, it's probably unencrypted
        const decrypted = decryptValue(existingToken);
        if (decrypted && decrypted !== existingToken) {
          // Token was successfully decrypted, so it's already encrypted
          return;
        }
      } catch {
        // Decryption failed, token is probably unencrypted
      }
      
      // Token is unencrypted, encrypt it
      const encryptedToken = encryptValue(existingToken);
      localStorage.setItem('authToken', encryptedToken);
      console.log('Migrated existing authToken to encrypted format');
    }
  } catch (error) {
    console.error('Error migrating existing tokens:', error);
  }
}
