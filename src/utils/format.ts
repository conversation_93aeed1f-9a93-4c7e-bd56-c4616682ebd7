/**
 * Format a date using Intl.DateTimeFormat
 * @param date Date to format
 * @param options Intl.DateTimeFormatOptions
 * @param locale Locale string
 * @returns Formatted date string
 */
export const formatDate = (
  date: Date | number,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  },
  locale = 'en-US'
): string => {
  return new Intl.DateTimeFormat(locale, options).format(date);
};

/**
 * Format a number as currency
 * @param amount Amount to format
 * @param currencyCode Currency code (default: USD)
 * @param locale Locale string (default: en-US)
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number, currencyCode = 'USD', locale = 'en-US'): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
  }).format(amount);
};

/**
 * Truncate a string with ellipsis
 * @param str String to truncate
 * @param maxLength Maximum length
 * @returns Truncated string
 */
export const truncateString = (str: string, maxLength: number): string => {
  if (str.length <= maxLength) return str;
  return `${str.slice(0, maxLength)}...`;
};

/**
 * Function to truncate name without breaking mid-name
 * Intelligently truncates at word boundaries, commas, or ampersands
 * @param text Text to truncate
 * @param maxLength Maximum length including ellipsis
 * @returns Truncated text with ellipsis
 */
export const truncateWithoutBreakingName = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }

  // Find the last complete name within the limit
  const truncated = text.substring(0, maxLength - 1); // Leave space for ellipsis
  const lastSpaceIndex = truncated.lastIndexOf(' ');
  const lastCommaIndex = truncated.lastIndexOf(',');
  const lastAmpersandIndex = truncated.lastIndexOf('&');

  // Find the best breaking point (space, comma, or ampersand)
  const breakPoint = Math.max(lastSpaceIndex, lastCommaIndex, lastAmpersandIndex);

  if (breakPoint > 0) {
    return truncated.substring(0, breakPoint).trim() + '…';
  }

  // If no good breaking point, just truncate at character limit
  return truncated + '…';
};

/**
 * Format workflow name based on clients array and apply intelligent truncation
 * @param clients Array of client names
 * @param maxLength Maximum length for the formatted name (default: 60)
 * @returns Formatted and truncated workflow name
 */
export const formatWorkflowName = (clients: string[], maxLength: number = 60): string => {
  if (clients.length === 0) {
    return 'New Court notice';
  }

  if (clients.length === 1) {
    return clients[0];
  }

  if (clients.length === 2) {
    const name = `${clients[0]} & ${clients[1]}`;
    return name.length <= maxLength ? name : truncateWithoutBreakingName(name, maxLength);
  }

  // More than 2 clients
  const baseName = `${clients[0]}, ${clients[1]} & more…`;
  return baseName.length <= maxLength ? baseName : truncateWithoutBreakingName(baseName, maxLength);
};
