import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import WorkflowSidebar from '@/components/workflows/sidebar/WorkflowSidebar';
import { Poppins } from 'next/font/google';
import { courtNoticeService } from '@/services/api';
import { CourtNoticeItem, CourtNoticeListParams, mapStatusVariant } from '@/types/courtNotice';
// import Link from 'next/link';
import { useRouter } from 'next/router';

import { DataTable, PageHeader, StatusBadge, Column, Pagination } from '@/components/common';
import Image from 'next/image';
import Head from 'next/head';
import SaveViewModal from '@/components/common/SaveViewModal';
import { UserViewService } from '@/services/userViewService';
import { toast } from 'react-hot-toast';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

const CourtNoticeNewPage = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [workflows, setWorkflows] = useState<CourtNoticeItem[]>([]);
  console.log('🚀 ~ CourtNoticeNewPage ~ workflows:', workflows);
  const [selectedWorkflows, setSelectedWorkflows] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({ page: 1, limit: 20, total: 0 });
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [showSaveViewModal, setShowSaveViewModal] = useState(false);
  const [savingView, setSavingView] = useState(false);

  useEffect(() => {
    fetchCourtNoticeList();
  }, [pagination.page, pagination.limit, sortBy, sortOrder]);

  const fetchCourtNoticeList = async () => {
    try {
      localStorage.setItem('workflowShowAllToggle', String(false));

      setLoading(true);
      setError(null);

      const params: CourtNoticeListParams = {
        page: pagination.page,
        limit: pagination.limit,
        type: 'new_court_notice',
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (sortBy) {
        params.sortBy = sortBy;
        params.sortOrder = sortOrder;
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const response: any = await courtNoticeService.getCourtNoticeList(params);

      // Handle cancellation (response is null)
      if (response === null) {
        console.log('Request was cancelled, ignoring response');
        return;
      }

      if (response.statusCode === 200) {
        setWorkflows(response.data.fields);
        setPagination({
          ...pagination,
          total: response.data.total,
        });
      } else {
        setError('Failed to fetch court notice data');
      }
    } catch (err) {
      // Check if this is a cancellation error - don't show it as an error to the user
      if (
        (err as Error).name === 'CanceledError' ||
        (err as Error).name === 'AbortError' ||
        (err as Error).message === 'canceled'
      ) {
        console.log('Request was cancelled, ignoring error');
        return;
      }

      console.error('Error fetching court notice data:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while fetching data');
    } finally {
      setLoading(false);
    }
  };

  // Search effect with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (pagination.page === 1) {
        fetchCourtNoticeList();
      } else {
        // Reset to page 1 when search term changes
        setPagination({ ...pagination, page: 1 });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const handleSelectWorkflow = (id: string, selected: boolean) => {
    console.log('id', id);
    console.log('selected', selected);
    if (selected) {
      setSelectedWorkflows(prev => [...prev, id]);
    } else {
      setSelectedWorkflows(prev => prev.filter(itemId => itemId !== id));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedWorkflows(workflows.map(item => item.id.toString()));
    } else {
      setSelectedWorkflows([]);
    }
  };

  const handleFilter = () => {
    console.log('Filter button clicked');
  };

  const handleColumns = () => {
    console.log('Columns button clicked');
  };

  const handleSaveView = () => {
    setShowSaveViewModal(true);
  };

  const handleSaveViewSubmit = async (viewName: string) => {
    try {
      setSavingView(true);

      // Get current filters and columns state for Court Notice
      const viewData = {
        name: viewName,
        type: 'new_court_notice', // enum: [my_work_flow, completed, archive, new_court_notice, follow_up]
        userId: 1, // TODO: Get from auth context
        filters: {
          search: searchTerm,
          sortBy,
          sortOrder,
        },
        columns: [
          {
            FieldName: 'WORKFLOW RUN',
            searchable: true,
            sortable: true,
            visible: true,
          },
          {
            FieldName: 'STARTED',
            searchable: true,
            sortable: true,
            visible: true,
          },
          {
            FieldName: 'DUE',
            searchable: true,
            sortable: true,
            visible: true,
          },
          {
            FieldName: 'STATUS',
            searchable: true,
            sortable: true,
            visible: true,
          },
          {
            FieldName: 'ASSIGNEE',
            searchable: true,
            sortable: true,
            visible: true,
          },
          {
            FieldName: 'Activity',
            searchable: true,
            sortable: true,
            visible: true,
          },
        ],
        filter: [
          // TODO: Add actual filter state from the page
          // Example structure based on API response:
          // {
          //   "fieldName": "status",
          //   "filter": "equal",
          //   "value": ["on_track", "completed"]
          // }
        ],
      };

      const response = await UserViewService.saveUserView(viewData);

      if (response.data.success) {
        toast.success('View saved successfully');
        setShowSaveViewModal(false);
        // Refresh the sidebar views
        const refreshSidebarViews = (window as unknown as { refreshSidebarViews?: () => void })
          .refreshSidebarViews;
        if (refreshSidebarViews) {
          refreshSidebarViews();
        }
      } else {
        toast.error('Failed to save view');
      }
    } catch (error) {
      console.error('Error saving view:', error);
      toast.error('Failed to save view');
    } finally {
      setSavingView(false);
    }
  };

  const handleRowClick = (rowData: CourtNoticeItem) => {
    router.push(`/workflowrun?taskId=${rowData?.last_task_id}&work_flow_id=${rowData?._id}`);
  };

  const handleSort = (field: string, order: 'asc' | 'desc') => {
    setSortBy(field);
    setSortOrder(order);
    // Reset to first page when sorting changes
    setPagination({ ...pagination, page: 1 });
  };

  const handlePageChange = (page: number) => {
    setPagination({ ...pagination, page });
  };

  const handleItemsPerPageChange = (limit: number) => {
    setPagination({ page: 1, limit, total: pagination.total });
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  const columns: Column[] = [
    {
      id: 'workflowRun',
      header: 'WORKFLOW RUN',
      width: '200px',
      sortable: true,
      sortField: 'work_flow_runner',
      cell: (row: CourtNoticeItem) => (
        <div
          className="text-[14px] text-[#2A2E34] cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          {row.work_flow_runner.split(',').length > 2
            ? row.work_flow_runner.split(',').slice(0, 2).join('   &') + '...'
            : row.work_flow_runner.split(',').join('   &')}
        </div>
      ),
    },
    // {
    //   id: 'matter',
    //   header: 'MATTER',
    //   cell: (row: CourtNoticeItem) => (
    //     <div className="text-sm text-[#5F6F84]">
    //       {row.run_by}
    //     </div>
    //   ),
    // },
    // {
    //   id: 'attorney',
    //   header: 'ATTORNEY',
    //   cell: (row: CourtNoticeItem) => (
    //     <div className="text-sm text-[#5F6F84]">
    //       {row.attorney}
    //     </div>
    //   ),
    // },
    {
      id: 'started',
      header: 'STARTED',
      sortable: true,
      sortField: 'start_date',
      cell: (row: CourtNoticeItem) => (
        <div className="text-sm cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.start_date}
        </div>
      ),
    },
    {
      id: 'due',
      header: 'DUE',
      sortable: true,
      sortField: 'end_date',
      cell: (row: CourtNoticeItem) => (
        <div className="text-sm  cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.end_date}
        </div>
      ),
    },
    {
      id: 'status',
      header: 'STATUS',
      sortable: false,
      sortField: 'status',
      cell: (row: CourtNoticeItem) => (
        <div onClick={() => handleRowClick(row)} className="cursor-pointer">
          <StatusBadge status={mapStatusVariant(row.status)} variant="fill" />
        </div>
      ),
    },
    {
      id: 'activity',
      header: 'ACTIVITY',
      sortable: true,
      sortField: 'last_activity',
      cell: (row: CourtNoticeItem) => <div className="text-sm ">{row.last_activity}</div>,
    },
    {
      id: 'assignee',
      header: 'ASSIGNEE',
      sortable: false,
      sortField: 'assigned_users',
      cell: (row: CourtNoticeItem) => {
        const assignedUsers = row.assigned_users
          ? row.assigned_users.split(',').filter(Boolean)
          : [];

        const userCount = assignedUsers.length || 0;
        const firstUser = assignedUsers[0];

        const roleKeywords = ['Paralegal', 'Attorney', 'Intake', 'Billing'];

        const hasRoleKeyword = assignedUsers.some(user =>
          roleKeywords.some(keyword => user.toLowerCase().includes(keyword.toLowerCase()))
        );

        return (
          <div
            className="relative w-[24px] h-[24px] cursor-pointer"
            onClick={() => handleRowClick(row)}
          >
            {hasRoleKeyword ? (
              <>
                <div className="w-[24px] h-[24px] rounded-full bg-[#3f73f65e] flex items-center justify-center">
                  <Image src="/assets/Group-icon.svg" alt="Group icon" width={25} height={25} />
                </div>
                <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                  {userCount}
                </span>
              </>
            ) : firstUser ? (
              <>
                <div className="w-[24px] h-[24px] rounded-full bg-[#5F6F84] text-white flex items-center justify-center font-semibold text-[10px]">
                  {getInitials(firstUser)}
                </div>
                <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                  {userCount}
                </span>
              </>
            ) : (
              <>
                <div className="w-[24px] h-[24px] rounded-full bg-[#C5D5FC] flex items-center justify-center font-semibold">
                  <Image
                    src="/assets/ai-robot-new-2.svg"
                    alt="AI assistant"
                    width={25}
                    height={25}
                  />
                </div>
                <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                  {userCount}
                </span>
              </>
            )}
          </div>
        );
      },
    },

    // {
    //   id: 'notes',
    //   header: 'NOTES',
    //   cell: (row: CourtNoticeItem) => (
    //     <div className="text-sm ">
    //       {row.notes || ''}
    //     </div>
    //   ),
    // },
  ];

  return (
    <Layout>
      <Head>
        <title>Court Notice New - FirmProfit</title>
        <meta name="description" content="Manage court notice new in FirmProfit" />
      </Head>
      <div className={`flex ml-0 h-full overflow-hidden ${poppins.className}`}>
        <WorkflowSidebar className="flex-shrink-0" />
        <div className="flex-1 overflow-auto">
          <div className="p-6">
            <PageHeader
              title="Court Notice"
              searchValue={searchTerm}
              onSearchChange={setSearchTerm}
              onFilter={handleFilter}
              onColumns={handleColumns}
              onSaveView={handleSaveView}
            />

            {error && <div className="mt-4 p-3 bg-red-100 text-red-700 rounded">{error}</div>}

            <DataTable
              columns={columns}
              data={workflows}
              selectedIds={selectedWorkflows}
              onSelectRow={handleSelectWorkflow}
              onSelectAll={handleSelectAll}
              isAllSelected={selectedWorkflows.length === workflows.length && workflows.length > 0}
              isLoading={loading}
              currentSortBy={sortBy}
              currentSortOrder={sortOrder}
              onSort={handleSort}
              className="mt-8"
            />

            {/* Pagination */}
            {!loading && workflows.length > 0 && (
              <Pagination
                currentPage={pagination.page}
                totalPages={totalPages}
                totalItems={pagination.total}
                itemsPerPage={pagination.limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                className="mt-6"
              />
            )}
          </div>
        </div>
      </div>

      <SaveViewModal
        isOpen={showSaveViewModal}
        onClose={() => setShowSaveViewModal(false)}
        onSave={handleSaveViewSubmit}
        isLoading={savingView}
      />
    </Layout>
  );
};

export default CourtNoticeNewPage;
