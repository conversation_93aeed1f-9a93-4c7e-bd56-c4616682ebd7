import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import Logo from '../../../public/logo.svg'; // Adjust the path as necessary
import axios from 'axios';
import { useRouter } from 'next/router';
import { Poppins } from 'next/font/google';
import { AxiosError } from 'axios';
import toast from 'react-hot-toast';
import { Button, Form, Input } from '@/components';
import { useAppDispatch } from '@/hooks';
import { setUser, resetMFA } from '@/features/user/userSlice';
import { secureAuthToken } from '@/utils';

// Define types for better type safety
interface ApiErrorResponse {
  message: string;
  statusCode?: number;
}

interface LoginResponse {
  data: {
    user: {
      id: string;
      email: string;
      isMFAEnable: boolean;
      // eslint-disable-next-line
      [key: string]: any;
    };
  };
  access_token: string;
  mfaRequired?: boolean;
  userId?: string;
}

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

const API_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

export default function SignIn() {
  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // Auth state
  // eslint-disable-next-line
  const [failedAttempts, setFailedAttempts] = useState(0);
  const [isAccountLocked, setIsAccountLocked] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [timezone, setTimezone] = useState('');

  const router = useRouter();
  const dispatch = useAppDispatch();

  // Validation patterns
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  /**
   * Validates the email field
   * @returns boolean indicating if email is valid
   */
  const validateEmail = (): boolean => {
    if (!email) {
      toast.error('Please enter your email address and password to continue.');
      return false;
    }
    if (!emailRegex.test(email)) {
      toast.error('Please enter a valid email address');
      return false;
    }
    return true;
  };

  useEffect(() => {
    const detectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    console.log(detectedTimezone, 'detectedTimezone');
    setTimezone(detectedTimezone);
    // Persist immediately so other parts of the app can use it
    try {
      localStorage.setItem('timezone', detectedTimezone);
    } catch (_err) {
      // noop: localStorage may be unavailable in some environments
      void _err;
    }
  }, []);

  // Refresh timezone when tab becomes active (minimal, no polling)
  useEffect(() => {
    const handleVisibility = () => {
      if (document.visibilityState === 'visible') {
        const tz = Intl.DateTimeFormat().resolvedOptions().timeZone;
        if (tz && tz !== timezone) {
          setTimezone(tz);
          try {
            localStorage.setItem('timezone', tz);
          } catch (_err) {
            // noop
            void _err;
          }
        }
      }
    };
    document.addEventListener('visibilitychange', handleVisibility);
    return () => document.removeEventListener('visibilitychange', handleVisibility);
  }, [timezone]);

  /**
   * Handles the login form submission
   */
  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email && !password) {
      toast.error('Please enter an email address and password');
      return;
    }

    if (!email) {
      // setEmailError('Email is required');
      toast.error('Please enter an email address');

      return true;
    }

    if (!password) {
      // setEmailError('Email is required');
      toast.error('Please enter a password');

      return true;
    }

    if (!emailRegex.test(email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    // Validate form inputs
    const isEmailValid = validateEmail();

    if (!isEmailValid) {
      return;
    }

    // Check for account lock
    if (isAccountLocked) {
      toast.error(
        'Your account is temporarily locked due to multiple failed sign in attempts. Check your email for instructions to unlock your account.'
      );
      return;
    }

    setIsLoading(true);

    try {
      // Always re-detect timezone at submit time to avoid stale state
      const detectedAtSubmit = Intl.DateTimeFormat().resolvedOptions().timeZone || timezone || 'UTC';
      try {
        localStorage.setItem('timezone', detectedAtSubmit);
      } catch (_err) {
        // noop
        void _err;
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const response: any = await axios.post<LoginResponse>(`${API_URL}/auth/login`, {
        email,
        password,
        timezone: detectedAtSubmit,
      }, {
        headers: {
          'Origin': window.location.origin
        }
       
      });

      // Helper: deep search token-like keys in an object
      const extractToken = (obj: unknown): string | null => {
        const TOKEN_KEYS = /^(access[_-]?token|accessToken|token|jwt|authorization)$/i;
        const seen = new Set<unknown>();
        const stack: Array<unknown> = [obj];
        while (stack.length) {
          const current = stack.pop();
          if (!current || typeof current !== 'object') continue;
          if (seen.has(current)) continue;
          seen.add(current);
          for (const [key, value] of Object.entries(current as Record<string, unknown>)) {
            if (TOKEN_KEYS.test(key) && value != null) {
              let str = String(value);
              if (/^Bearer\s+/i.test(str)) str = str.replace(/^Bearer\s+/i, '');
              str = str.trim();
              if (str && str !== 'null' && str !== 'undefined') return str;
            }
            if (value && typeof value === 'object') stack.push(value);
          }
        }
        return null;
      };

      // Save user data to localStorage
      const userData = response?.data?.data?.user ?? response?.data?.user;

      // Prefer explicit known path first
      let rawToken = (response?.data?.data?.user?.access_token as string | undefined)?.trim() || '';

      // If not found, fall back to generic deep extraction
      if (!rawToken) {
        const headerToken = extractToken(response?.headers ?? {});
        const bodyToken = extractToken(response?.data ?? {});
        rawToken = (headerToken || bodyToken || '').trim();
      }

      if (userData) {
        localStorage.setItem('user', JSON.stringify(userData));
        
        // CRITICAL: Update Redux user state with MFA information
        dispatch(setUser({
          name: userData.email,
          avatar: '/avatars/default.png',
          isLoggedIn: true,
          isMFAEnabled: userData.isMFAEnable || false,
          isMFAVerified: false, // Will be set to true after MFA verification
        }));
      }
      
      // Ensure latest timezone is persisted with user data
      localStorage.setItem('timezone', detectedAtSubmit);

      // Store token securely using the encryption wrapper
      secureAuthToken.set(rawToken);

      // Reset MFA state for new login
      dispatch(resetMFA());

      // Handle MFA if required
      if (response.data.mfaRequired) {
        router.push({
          pathname: '/auth/verifymfa',
          query: { userId: response.data.userId },
        });
      } else {
        router.push('/auth/verifymfa');
      }

      // Reset failed attempts on successful login
      setFailedAttempts(0);
    } catch (error) {
      setIsLoading(false);
      handleLoginError(error);
    }
  };

  /**
   * Handles API errors during login
   */
  const handleLoginError = (error: unknown) => {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<ApiErrorResponse>;
      const { response } = axiosError;

      if (!response) {
        toast.error('Network error. Please try again.');
        return;
      }

      const { status, data } = response;
      const errorMessage = data?.message || 'An unexpected error occurred';

      // Handle specific status codes
      switch (status) {
        case 409:
          router.push('/auth/accountlocked');
          return;

        case 403:
          setIsAccountLocked(true);
          toast.error(
            errorMessage || 'Your account is temporarily locked. Check your email for instructions.'
          );
          break;

        case 500:
          toast.error('Server error. Please try again later.');
          break;

        default:
          toast.error(errorMessage);
      }
    } else {
      toast.error('Network error. Please try again.');
    }
  };

  return (
    <>
      <Head>
        <title>Sign in to FirmProfit</title>
        <meta name="description" content="Sign in to your FirmProfit account" />
      </Head>

      <div
        className={`flex min-h-screen bg-white flex-col font-poppins md:flex-row ${poppins.className}`}
      >
        {/* Left Section - Form - 50% width, takes remaining space when right hits max-width */}
        <div className="w-full md:w-1/2 md:flex-1 flex flex-col px-6 py-12 lg:px-8">
          {/* Logo in top left */}
          <div className="absolute top-[40px] left-[40px]">
            <Image src={Logo} alt="FirmProfit Logo" width={169} height={40} />
          </div>
          <div className="flex flex-col items-center justify-center flex-1">
            <div className="w-full lg:w-[400px] lg:mx-auto">
              <h2 className="mt-[2px] text-center text-[24px] leading-[36px] font-medium text-[#2A2E34]">
                Sign in to FirmProfit
              </h2>
              <p className="mt-2 text-center font-normal text-[14px] leading-[20px] text-[#5F6F84]">
                Enter your credentials to login to your account
              </p>
            </div>
            <div className="mt-[30px] w-full lg:w-[400px] lg:mx-auto">
              {/* Social Sign-in Buttons */}
              <div className="space-y-4 mb-4">
                <Button
                  variant="secondary"
                  fullWidth
                  data-testid="google-signin"
                  className="text-[14px] leading-[20px] text-[#5F6F84] font-normal"
                  icon={
                    <svg width="20" height="20" xmlns="/images/google.svg" viewBox="0 0 24 24">
                      <path
                        fill="#4285F4"
                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      />
                      <path
                        fill="#34A853"
                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      />
                      <path
                        fill="#FBBC05"
                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      />
                      <path
                        fill="#EA4335"
                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      />
                    </svg>
                  }
                  iconPosition="left"
                >
                  Sign in with Google
                </Button>

                <Button
                  variant="secondary"
                  fullWidth
                  data-testid="microsoft-signin"
                  className="text-[14px] leading-[20px] text-[#5F6F84] font-normal"
                  icon={
                    <Image
                      src="/images/Microsoft.svg"
                      alt="Microsoft Logo"
                      width={20}
                      height={20}
                      className="h-[20px] w-[20px]"
                    />
                  }
                  iconPosition="left"
                >
                  Sign in with Microsoft
                </Button>
              </div>

              {/* Divider */}
              <div className="relative my-[30px]">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-[#DCE2EB]" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="bg-white font-normal text-[14px] px-[15px] leading-[20px] text-[#A2AFC2]">
                    Or
                  </span>
                </div>
              </div>

              <Form onSubmit={handleSignIn} className="space-y-[20px]" data-testid="signin-form">
                <Input
                  id="email"
                  label="Email*"
                  autoComplete="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={e => setEmail(e.target.value.trim())}
                  data-testid="email-input"
                />

                <div>
                  <div className="flex items-center justify-between">
                    <label
                      htmlFor="password"
                      className="block text-sm font-medium leading-6 text-[#2A2E34]"
                    >
                      Password*
                    </label>
                    <div className="">
                      <Link
                        href="/auth/forgotpassword"
                        className="font-medium text-[#3F73F6] text-[14px] leading-[20px] hover:text-blue-500"
                        data-testid="forgot-password-link"
                        tabIndex={-1}
                      >
                        Forgot password?
                      </Link>
                    </div>
                  </div>

                  <Input
                    id="password"
                    type="password"
                    autoComplete="current-password"
                    placeholder="Enter your password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    showPasswordToggle
                    data-testid="password-input"
                  />
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  fullWidth
                  isLoading={isLoading}
                  disabled={isLoading}
                  className="mt-[30px]"
                  data-testid="signin-button"
                >
                  Sign in
                </Button>
              </Form>

              <p className="mt-6 text-center text-[14px] leading-[20px] font-normal text-[#5F6F84]">
                <span className="text-[14px] leading-[20px] font-normal text-[#5F6F84]">
                  Don&apos;t have an account?{' '}
                </span>
                <Link
                  // href="/signup" //this is to be done only for MVP delivery as we are not allowing Users to signup through application Sign Up link
                  href="#"
                  className="font-medium leading-6 ml-[1px] text-[#3F73F6] hover:text-blue-500"
                  data-testid="signup-link"
                  onClick={(e) => e.preventDefault()}
                >
                  Sign up
                </Link>
              </p>
            </div>
          </div>
        </div>

        {/* Right Section - 50% width with max-width 1280px */}
        <div className="hidden md:flex bg-[#3F73F6] relative overflow-hidden w-1/2 max-w-[1280px] flex-shrink-0">
          <div className="absolute inset-0 mix-blend-multiply origin-center left-[-158px] top-0 xl-plus:left-0 xl-plus:top-0 3xl:left-[-22px] 3xl:top-0 bg-[url('/images/background_image.png')] bg-cover bg-no-repeat 3xl:bg-[url('/images/background_image-big.png')]"></div>

          <div className="flex items-center justify-center h-full w-full relative z-10 ">
            <div className="w-full max-w-[444px] xl-plus:max-w-[658px] flex flex-col align-items-start">

              <h1 className="text-white font-poppins text-[36px] xl-plus:text-[48px] font-medium leading-[1.1] ml-8 ">
                <div className="flex flex-col">
                  <span>AI-powered workflow</span>
                  <span>automation</span>
                </div>
              </h1>


              <p className="text-white font-poppins text-[20px] font-medium leading-[28px] mt-2 text-left mb-10 ml-8">
                Effortlessly manage your operation with FirmProfitAI.
              </p>


              <div className="w-full overflow-hidden relative">
                <img
                  src="/Sign up image.svg"
                  alt="AI workflow"
                  className="w-full max-w-[658px] h-auto border-red-600"

                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
