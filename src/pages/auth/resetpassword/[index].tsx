import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import { Poppins } from 'next/font/google';
import { useRouter } from 'next/router';
import toast from 'react-hot-toast';
import { Button, Form, Input } from '@/components';
import Head from 'next/head';
import apiClient from '@/services/api/config';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

const ResetPasswordPage = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(true);
  const [tokenValid, setTokenValid] = useState(true);
  const [token, setToken] = useState('');
  const API_URL = process.env.NEXT_PUBLIC_BASE_URL || '';
  const router = useRouter();

  useEffect(() => {
    // Extract token from URL
    if (router.isReady) {
      const pathSegments = router.asPath.split('/');
      const urlToken = pathSegments[pathSegments.length - 1];

      if (urlToken) {
        setToken(urlToken);
        validateToken(urlToken);
      } else {
        toast.error('Invalid or missing token');
        setLoading(false);
      }
    }
  }, [router.isReady, router.asPath]);

  const validatePassword = (password: string) => {
    if (password.length < 8) {
      return 'Password must meet the required security criteria';
    }
    if (!/[A-Z]/.test(password)) {
      return 'Password must meet the required security criteria.';
    }
    if (!/[a-z]/.test(password)) {
      return 'Password must meet the required security criteria';
    }
    if (!/\d/.test(password)) {
      return 'Password must include at least one number.';
    }
    if (!/[@$!%?&#_]/.test(password)) {
      return 'Password must meet the required security criteria';
    }
    return;
  };

  const validateToken = async (tokenValue: string) => {
    try {
      const response = await apiClient.post(`${API_URL}/auth/validate-token`, {
        token: tokenValue,
      });

      if (response.data.statusCode === 200) {
        setTokenValid(true);
      } else {
        toast.error('Token is invalid or expired');
      }
    } catch (err) {
      console.log(err);
      toast.error('This link has expired. Please request a new one to reset your password.');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const validationError = validatePassword(password);

    if (!password && !confirmPassword) {
      toast.error('Please enter a new password and confirm it');
      return;
    }

    if (!password) {
      toast.error('Please enter a new password');
      return;
    }

    if (validationError) {
      toast.error(validationError);
      return;
    }

    if (!confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    try {
      const response = await apiClient.post(`${API_URL}/auth/reset-password`, { token, password });

      if (response.data.statusCode === 200) {
        // toast.success('Your password has been successfully updated');
        setSuccess(true);
        setPassword('');
        setConfirmPassword('');
      } else {
        toast.error(response.data.message || 'Password reset failed.');
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (err: any) {
      toast.error(
        err.response?.data?.message || 'An error occurred while resetting your password.'
      );
    } finally {
      setLoading(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className={`flex min-h-screen bg-white ${poppins.className} md:flex-row`}>
        <div className="flex flex-1 flex-col px-6 py-12 lg:px-8 justify-center items-center">
          <div className="absolute top-[40px] left-[40px]">
            <Image src="../logo.svg" alt="FirmProfit Logo" width={169} height={40} />
          </div>
          <div className="w-full max-w-md text-center">
            <p>Loading...</p>
          </div>
        </div>
        <div className="hidden md:block md:flex-1 bg-[#3F73F6]"></div>
      </div>
    );
  }

  // Show error if token is invalid
  if (!tokenValid && !loading) {
    return (
      <div className={`flex min-h-screen bg-white ${poppins.className} md:flex-row`}>
        <div className="flex flex-1 flex-col px-6 py-12 lg:px-8 justify-center items-center">
          <div className="absolute top-[40px] left-[40px]">
            <Image src="../../public/logo.svg" alt="FirmProfit Logo" width={149} height={40} />
          </div>
          <div className="w-full max-w-md text-center">
            <p className="text-red-700 mb-4">
              Invalid or expired token. Please request a new password reset link.
            </p>
            <Link
              href="/signin"
              className="text-sm text-[#3F73F6] font-medium hover:text-blue-500 flex items-center justify-center"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-1 text-[#3F73F6]" />
              Back to Sign in
            </Link>
          </div>
        </div>
        <div className="hidden md:block md:flex-1 bg-[#3F73F6]"></div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Reset Password - FirmProfit</title>
        <meta name="description" content="Reset password in FirmProfit" />
      </Head>
      <div className={`flex min-h-screen bg-white ${poppins.className} md:flex-row`}>
        {/* Left Section - Form */}
        <div className="flex flex-1 flex-col px-6 py-12 lg:px-8 justify-center items-center">
          <div className="absolute top-[40px] left-[40px]">
            <Image src="../../../logo.svg" alt="FirmProfit Logo" width={169} height={40} />
          </div>
          <div className=" w-[400px] max-w-md">
            {!success ? (
              <div>
                <div className="text-left">
                  <h2 className="text-[24px] leading-[36px] font-medium text-[#25282C] mb-1">
                    Set up a new password
                  </h2>
                  <p className="text-[#5F6F84] leading-[20px] font-normal text-[14px] mb-6">
                    Password must be at least 8 characters and include an uppercase letter, a lowercase letter, a number, and a special character.
                  </p>
                </div>

                <Form onSubmit={handleSubmit} className="space-y-6">
                  <Input
                    id="password"
                    type="password"
                    label="New password*"
                    autoComplete="new-password"
                    placeholder="Enter a new password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    showPasswordToggle
                  />

                  <Input
                    id="confirmPassword"
                    type="password"
                    label="Confirm password*"
                    autoComplete="new-password"
                    placeholder="Repeat a new password"
                    value={confirmPassword}
                    onChange={e => setConfirmPassword(e.target.value)}
                    showPasswordToggle
                  />

                  <div className="pt-2">
                    <Button
                      type="submit"
                      variant="primary"
                      fullWidth
                      isLoading={loading}
                      disabled={loading}
                    >
                      Reset password
                    </Button>
                  </div>
                </Form>
                <div className="mt-6 text-center">
                  <Link
                    href="/signin"
                    className="text-[14px] leading-[20px] font-medium text-[#3F73F6] hover:text-blue-500 flex items-center justify-center"
                  >
                    <ArrowLeftIcon className="h-4 w-4 mr-1" />
                    Back to Sign in
                  </Link>
                </div>
              </div>
            ) : (
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="h-16 w-16 rounded-full flex items-center justify-center">
                    <Image src="/passwordUpdate.svg" alt="Password Icon" width={48} height={48} />
                  </div>
                </div>
                <h2 className="text-[24px] leading-[36px] font-medium text-[#25282C] mb-2">
                  Password updated!
                </h2>
                <p className="text-[#5F6F84] font-normal text-[14px] mb-8">
                  Your password has been updated successfully.
                </p>
                <div className="flex justify-center">
                  <Link href="/signin" className="w-full">
                    <Button variant="primary" fullWidth icon={<ArrowRightIcon className="h-4 w-4" />}>
                      Sign in to your account
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Section - Blue Background */}
        <div className="hidden md:block md:flex-1 bg-[#3F73F6]">
          {/* Visible only on md screens and above */}
        </div>
      </div>
    </>
  );
};

export default ResetPasswordPage;
