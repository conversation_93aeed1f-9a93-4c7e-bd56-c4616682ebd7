import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import { Poppins } from 'next/font/google';
import axios from 'axios';
import toast from 'react-hot-toast';
import { Button, Form, Input } from '@/components';
import Head from 'next/head';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

const ForgotPasswordPage = () => {
  const [email, setEmail] = useState('');
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const [responseMessage, setResponseMessage] = useState('');
  const API_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      return 'Email is required';
    }
    if (!emailRegex.test(email)) {
      return 'Please enter a valid email address';
    }
    return;
  };


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const validationError = validateEmail(email);

    if (validationError) {
      toast.error(validationError);
      return;
    }

    setLoading(true);

    try {
      const response = await axios.post(`${API_URL}/auth/forgot-password`, { email });

      if (response.data.statusCode === 200 || response.status === 200) {
        setSuccess(true);
        setResponseMessage(response.data.data.message || 'We sent a password reset link to your email address.');
        setEmail('');
      } else {
        toast.error(response.data.message || 'Failed to send reset email.');
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (err: any) {
      toast.error(
        err.response?.data?.message || 'An error occurred while sending the reset email.'
      );
    } finally {
      setLoading(false);
    }
  };


  return (
    <>
      <Head>
        <title>Forgot Password - FirmProfit</title>
        <meta name="description" content="Reset your password in FirmProfit" />
      </Head>
      <div className={`flex min-h-screen bg-white ${poppins.className} md:flex-row`}>
        {/* Left Section - Form */}
        <div className="flex flex-1 flex-col px-6 py-12 lg:px-8 justify-center items-center">
          <div className="absolute top-[40px] left-[40px]">
            <Image src="/logo.svg" alt="FirmProfit Logo" width={169} height={40} />
          </div>
          <div className=" w-[400px] max-w-md">
            {!success ? (
              <div>
                <div className="text-left">
                  <h2 className="text-[24px] leading-[36px] font-medium text-[#25282C] mb-1">
                    Forgot password?
                  </h2>
                  <p className="text-[#5F6F84] leading-[20px] font-normal text-[14px] mb-6">
                  {/* eslint-disable-next-line */}
                    No worries, we'll send you reset instructions.
                  </p>
                </div>

                <Form onSubmit={handleSubmit} className="space-y-6">
                  <Input
                    id="email"
                    type="email"
                    label="Email*"
                    autoComplete="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                  />

                  <div className="pt-2">
                    <Button
                      type="submit"
                      variant="primary"
                      fullWidth
                      isLoading={loading}
                      disabled={loading}
                    >
                      Send reset instructions
                    </Button>
                  </div>
                </Form>
                <div className="mt-6 text-center">
                  <Link
                    href="/signin"
                    className="text-[14px] leading-[20px] font-medium text-[#3F73F6] hover:text-blue-500 flex items-center justify-center"
                  >
                    <ArrowLeftIcon className="h-4 w-4 mr-1" />
                    Back to Sign in
                  </Link>
                </div>
              </div>
            ) : (
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="h-16 w-16 rounded-full flex items-center justify-center">
                    <Image src="/passwordUpdate.svg" alt="Password Icon" width={48} height={48} />
                  </div>
                </div>
                <h2 className="text-[24px] leading-[36px] font-medium text-[#25282C] mb-2">
                  Check your email
                </h2>
                <p className="text-[#5F6F84] font-normal text-[14px] mb-8">
                  {responseMessage}
                </p>
                <div className="flex justify-center">
                  <Link href="/signin" className="w-full">
                    <Button variant="primary" fullWidth icon={<ArrowRightIcon className="h-4 w-4" />}>
                      Back to Sign in
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Section - Blue Background */}
        <div className="hidden md:block md:flex-1 bg-[#3F73F6]">
          {/* Visible only on md screens and above */}
        </div>
      </div>
    </>
  );
};

export default ForgotPasswordPage;
