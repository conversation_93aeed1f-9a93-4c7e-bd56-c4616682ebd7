import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import WorkflowSidebar from '@/components/workflows/sidebar/WorkflowSidebar';
import { Poppins } from 'next/font/google';
import { EventService } from '@/services/eventService';
import { Event, EventListParams } from '@/types/event';
// import { useRouter } from 'next/router';
import Link from 'next/link';

import { DataTable, PageHeader, Column, Pagination, StatusBadge } from '@/components/common';
import Image from 'next/image';
import Head from 'next/head';
import SaveViewModal from '@/components/common/SaveViewModal';
import { UserViewService } from '@/services/userViewService';
import { toast } from 'react-hot-toast';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

const CalendarPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvents, setSelectedEvents] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({ page: 1, limit: 20, total: 0 });

  // Utility function to extract name before brackets
  const extractNameFromString = (fullString: string): string => {
    if (!fullString) return '';
    const bracketIndex = fullString.indexOf('(');
    return bracketIndex !== -1 ? fullString.substring(0, bracketIndex).trim() : fullString.trim();
  };
  const [showSaveViewModal, setShowSaveViewModal] = useState(false);
  const [savingView, setSavingView] = useState(false);

  useEffect(() => {
    fetchEventsList();
  }, [pagination.page, pagination.limit]);

  const fetchEventsList = async () => {
    try {
      setLoading(true);
      setError(null);

      const params: EventListParams = {
        page: pagination.page,
        limit: pagination.limit,
      };

      if (searchTerm) {
        params.search = searchTerm;
      }


      const response = await EventService.getEvents(params);

      // The API handles filtering and sorting, so we use the response directly
      setEvents(response.data);
      setPagination({
        ...pagination,
        total: response.total,
      });
    } catch (err) {
      console.error('Error fetching events data:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while fetching events');
    } finally {
      setLoading(false);
    }
  };

  // Search effect with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (pagination.page === 1) {
        fetchEventsList();
      } else {
        // Reset to page 1 when search term changes
        setPagination({ ...pagination, page: 1 });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const handleSelectEvent = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedEvents(prev => [...prev, id]);
    } else {
      setSelectedEvents(prev => prev.filter(itemId => itemId !== id));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedEvents(events.map(item => item.id));
    } else {
      setSelectedEvents([]);
    }
  };

  const handleFilter = () => {
    console.log('Filter button clicked');
    // TODO: Implement filter functionality
  };

  const handleColumns = () => {
    console.log('Columns button clicked');
    // TODO: Implement columns customization
  };

  const handleSaveView = () => {
    setShowSaveViewModal(true);
  };

  const handleSaveViewSubmit = async (viewName: string) => {
    try {
      setSavingView(true);

      // Get current filters and columns state for Calendar Events
      const viewData = {
        name: viewName,
        type: 'court_notices', // Court notices filter type
        userId: 1, // TODO: Get from auth context
        filters: {
          search: searchTerm,
        },
        columns: [
          {
            FieldName: 'SUBJECT',
            searchable: true,
            sortable: false,
            visible: true,
          },
          {
            FieldName: 'WORKFLOW RUN',
            searchable: true,
            sortable: false,
            visible: true,
          },
          {
            FieldName: 'CONTACT NAME',
            searchable: true,
            sortable: false,
            visible: true,
          },
          {
            FieldName: 'MATTER',
            searchable: true,
            sortable: false,
            visible: true,
          },
          {
            FieldName: 'EVENT TYPE',
            searchable: true,
            sortable: false,
            visible: true,
          },
          {
            FieldName: 'START TIME',
            searchable: true,
            sortable: false,
            visible: true,
          },
          {
            FieldName: 'END TIME',
            searchable: true,
            sortable: false,
            visible: true,
          },
          {
            FieldName: 'EVENT STATUS',
            searchable: true,
            sortable: false,
            visible: true,
          },
          {
            FieldName: 'LOCATION',
            searchable: true,
            sortable: false,
            visible: true,
          },
          {
            FieldName: 'ATTORNEY(S)',
            searchable: true,
            sortable: false,
            visible: true,
          },
          {
            FieldName: 'SYNC STATUS',
            searchable: true,
            sortable: false,
            visible: true,
          },
        ],
        filter: [],
      };

      const response = await UserViewService.saveUserView(viewData);

      if (response.data.success) {
        toast.success('View saved successfully');
        setShowSaveViewModal(false);
        // Refresh the sidebar views
        const refreshSidebarViews = (window as unknown as { refreshSidebarViews?: () => void })
          .refreshSidebarViews;
        if (refreshSidebarViews) {
          refreshSidebarViews();
        }
      } else {
        toast.error('Failed to save view');
      }
    } catch (error) {
      console.error('Error saving view:', error);
      toast.error('Failed to save view');
    } finally {
      setSavingView(false);
    }
  };

  const handleRowClick = (rowData: Event) => {
    // TODO: Navigate to event details page
    console.log('Event clicked:', rowData);
    // router.push(`/events/${rowData.id}`);
  };


  const handlePageChange = (page: number) => {
    setPagination({ ...pagination, page });
  };

  const handleItemsPerPageChange = (limit: number) => {
    setPagination({ page: 1, limit, total: pagination.total });
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  const columns: Column[] = [
    {
      id: 'subject',
      header: 'SUBJECT',
      width: '250px',
      cell: (row: Event) => (
        <div
          className="text-[14px] cursor-pointer font-medium"
          onClick={() => handleRowClick(row)}
        >
          {row.subject}
        </div>
      ),
    },
    {
      id: 'matter',
      header: 'MATTER',
      cell: (row: Event) => (
        <div className="text-sm">
          {row.matter}
        </div>
      ),
    },
    {
      id: 'courtNoticeType',
      header: 'EVENT TYPE',
      cell: (row: Event) => (
        <div className="text-sm">
          {row.courtNoticeType}
        </div>
      ),
    },
    {
      id: 'start',
      header: 'START TIME',
      cell: (row: Event) => (
        <div className="text-sm cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.start}
        </div>
      ),
    },
    {
      id: 'end',
      header: 'END TIME',
      cell: (row: Event) => (
        <div className="text-sm cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.end}
        </div>
      ),
    },
    {
      id: 'status',
      header: 'EVENT STATUS',
      cell: (row: Event) => (
        <div onClick={() => handleRowClick(row)} className="cursor-pointer">
          <StatusBadge status={row.status} variant="fill" />
        </div>
      ),
    },
    {
      id: 'location',
      header: 'LOCATION',
      cell: (row: Event) => (
        <div className="text-sm cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.location}
        </div>
      ),
    },
    {
      id: 'attorney',
      header: 'ATTORNEY(S)',
      cell: (row: Event) => (
        <div
          className="text-sm cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          <div className="font-normal">{row.attorney}</div>
          {/* {row.attorneys && row.attorneys.length > 1 && (
            <div className="text-xs mt-1">
              {row.attorneys.filter(a => !a.includes('Primary')).join(', ')}
            </div>
          )} */}
        </div>
      ),
    },
    {
      id: 'contactName',
      header: 'CONTACT NAME',
      cell: (row: Event) => (
        <div
          className="text-sm cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          {extractNameFromString(row.contactName)}
        </div>
      ),
    },
    {
      id: 'workflowRun',
      header: 'WORKFLOW RUN',
      width: '200px',
      cell: (row: Event) => (
        <div>
          {row.workflowRuns.map((workflow, index) => (
            <div key={workflow.id} className={index > 0 ? 'mt-1' : ''}>
              <Link
                href={workflow.url}
                className="text-[#3F73F6] text-[14px] leading-[20px] font-normal block"
                style={{ fontFamily: 'Poppins' }}
              >
                {row.contactName}
              </Link>
              <div 
                className="text-[#5F6F84] text-[10px] leading-[16px] font-normal"
                style={{ fontFamily: 'Poppins' }}
              >
                {workflow.templateName}
              </div>
            </div>
          ))}
        </div>
      ),
    },
    {
      id: 'syncStatus',
      header: 'SYNC STATUS',
      cell: (row: Event) => (
        <div className="text-sm">
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-[14px]`}
          >
            {row.syncStatus}
          </span>
        </div>
      ),
    },
  ];

  return (
    <Layout>
      <Head>
        <title>Calendar - FirmProfit</title>
        <meta name="description" content="Manage calendar court notices in FirmProfit" />
      </Head>
      <div className={`flex ml-0 h-full overflow-hidden ${poppins.className}`}>
        <WorkflowSidebar className="flex-shrink-0" />
        <div className="flex-1 overflow-auto">
          <div className="p-6">
            <PageHeader
              title="Court Notices"
              searchValue={searchTerm}
              onSearchChange={setSearchTerm}
              onFilter={handleFilter}
              onColumns={handleColumns}
              onSaveView={handleSaveView}
              showFilter={false}
              showColumns={false}
              showSaveView={false}
            />

            {/* Filter tabs */}
            {/* <div className="mt-4 border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                <button className="whitespace-nowrap py-2 px-1 border-b-2 border-[#3F73F6] font-medium text-sm text-[#3F73F6]">
                  Court Notices
                </button>
              </nav>
            </div> */}

            {error && <div className="mt-4 p-3 bg-red-100 text-red-700 rounded">{error}</div>}

            <DataTable
              columns={columns}
              showCheckbox={false}
              data={events}
              selectedIds={selectedEvents}
              onSelectRow={handleSelectEvent}
              onSelectAll={handleSelectAll}
              isAllSelected={selectedEvents.length === events.length && events.length > 0}
              isLoading={loading}
              className="mt-8"
            />

            {/* Pagination */}
            {!loading && events.length > 0 && (
              <Pagination
                currentPage={pagination.page}
                totalPages={totalPages}
                totalItems={pagination.total}
                itemsPerPage={pagination.limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                className="mt-6"
              />
            )}

            {/* Empty state */}
            {!loading && events.length === 0 && (
              <div className="mt-8 text-center py-12">
                <Image
                  src="/assets/calendar.svg"
                  alt="No events"
                  width={64}
                  height={64}
                  className="mx-auto mb-4 opacity-50"
                />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No court notices found</h3>
                <p className="text-gray-500">
                  {searchTerm ? 'Try adjusting your search terms.' : 'No court notices available.'}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      <SaveViewModal
        isOpen={showSaveViewModal}
        onClose={() => setShowSaveViewModal(false)}
        onSave={handleSaveViewSubmit}
        isLoading={savingView}
      />
    </Layout>
  );
};

export default CalendarPage;
