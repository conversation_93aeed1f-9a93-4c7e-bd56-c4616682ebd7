import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import Layout from '@/components/layout/Layout';
import WorkflowSidebar from '@/components/workflows/sidebar/WorkflowSidebar';
import { Poppins } from 'next/font/google';
import {
  DataTable,
  PageHeader,
  StatusBadge,
  // Avatar,
  Column,
  Pagination,
} from '@/components/common';
import Head from 'next/head';
import { workflowService } from '@/services/api';

import SaveViewModal from '@/components/common/SaveViewModal';
import { UserViewService } from '@/services/userViewService';
import { mapStatusVariant } from '@/types/courtNotice';
import { formatWorkflowName } from '@/utils/format';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

interface TableViewField {
  visible: boolean;
  FieldName: string;
  searchable: boolean;
  sortable: boolean;
}

interface CourtNoticeItem {
  assigned_users: string;
  id: string;
  _id: string;
  workflowRun: string;
  started: string;
  due: string;
  status: 'On Track' | 'Delayed' | 'Completed' | 'DUE SOON' | 'OVERDUE';
  activity: string;
  assignee: string;
  notes?: string;
  work_flow_runner: string;
  start_date: string;
  attorney?: string;
  matter?: string;
  templates?: string;
  contact?: string;
  task_completed?: string;
  view?: string[];
  last_activity?: string;
  work_flow_name?: string;
  end_date?: string;
  last_task_id?: string;
}

// type StatusType = 'On Track' | 'Delayed' | 'Completed';

// Function to get page title and header based on query type
const getPageInfo = (type: string | string[] | undefined) => {
  const typeStr = Array.isArray(type) ? type[0] : type;

  switch (typeStr) {
    case 'my-workflow':
      return { title: 'My Workflows - FirmProfit', header: 'Workflows Assigned to Me' };
    case 'my-tasks':
      return { title: 'My Tasks - FirmProfit', header: 'My Tasks' };
    case 'inbound':
      return { title: 'Inbound - FirmProfit', header: 'Inbound' };
    case 'new-court-notice':
      return { title: 'New Court Notice - FirmProfit', header: 'New Court Notice' };
    case 'court-notice-follow-up':
      return { title: 'Court Notice Follow up - FirmProfit', header: 'Court Notice Follow up' };
    case 'completed':
      return { title: 'Completed - FirmProfit', header: 'Completed' };
    case 'archived':
      return { title: 'Archived - FirmProfit', header: 'Archived' };
    default:
      return { title: 'Court Notice - FirmProfit', header: 'Court Notice' };
  }
};

const CourtNoticePage = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userId, setUserId] = useState<number | null>(null);
  const [pagination, setPagination] = useState({ page: 1, limit: 20, total: 0 });
  const [viewFields, setViewFields] = useState<TableViewField[]>([]);
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [activeColumn, setActiveColumn] = useState<string>(''); // Track which column header was clicked for visual indicator
  const [hasFetched, setHasFetched] = useState(false); // Tracks if at least one API request has completed

  // Store user's explicit column selections in localStorage
  const COLUMN_STORAGE_KEY = 'workflow_visible_columns';

  // Helper functions for localStorage management
  const saveColumnVisibility = (columns: string[]) => {
    try {
      localStorage.setItem(COLUMN_STORAGE_KEY, JSON.stringify(columns));
    } catch (error) {
      console.error('Failed to save column visibility to localStorage:', error);
    }
  };

  const getStoredColumnVisibility = (): string[] | null => {
    try {
      const stored = localStorage.getItem(COLUMN_STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Failed to get column visibility from localStorage:', error);
      return null;
    }
  };

  const clearColumnVisibility = () => {
    try {
      localStorage.removeItem(COLUMN_STORAGE_KEY);
    } catch (error) {
      console.error('Failed to clear column visibility from localStorage:', error);
    }
  };

  useEffect(() => {
    clearColumnVisibility();
    // Reset default filter flag when page type changes to ensure fresh filter application
    if (router.isReady) {
      setDefaultFilterSet(false);
    }
  }, [router.isReady, router.query.type]);

  const [courtNotices, setCourtNotices] = useState<CourtNoticeItem[]>([]);

  const [selectedNotices, setSelectedNotices] = useState<string[]>([]);
  const [visibleColumnNames, setVisibleColumnNames] = useState<string[] | null>(null);
  const [showSaveViewModal, setShowSaveViewModal] = useState(false);
  const [savingView, setSavingView] = useState(false);
  const [currentFilters, setCurrentFilters] = useState<
    Array<{
      fieldName: string;
      filter: string;
      value: string[];
    }>
  >([]);
  const [appliedFilters, setAppliedFilters] = useState<
    Array<{
      fieldName: string;
      filter: string;
      value: string[];
    }>
  >([]);

  // Track if default filter has been set to avoid overriding user changes
  const [defaultFilterSet, setDefaultFilterSet] = useState(false);

  /**
   * Get logged-in user's ID from localStorage
   * @returns {string | null} User ID or null if not found
   */
  const getLoggedInUserId = (): string | null => {
    try {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        const parsedUser = JSON.parse(storedUser);
        console.log('🚀 User data from localStorage:', parsedUser);

        // Priority: use user_id first (ObjectId for API matching), then fallback to other fields
        const userId = parsedUser.user_id || parsedUser._id || parsedUser.id || parsedUser.userId;
        console.log('🚀 Available ID fields:', {
          user_id: parsedUser.user_id,
          _id: parsedUser._id,
          id: parsedUser.id,
          userId: parsedUser.userId,
        });
        console.log('🚀 Using user ID for API matching:', userId);

        return userId ? String(userId) : null;
      }
      return null;
    } catch (error) {
      console.error('Error getting user ID from localStorage:', error);
      return null;
    }
  };

  /**
   * Fetch logged-in user's name from assignee API by matching user ID
   * @returns {Promise<string | null>} User's display name or null if not found
   */
  const getLoggedInUserNameFromAPI = async (): Promise<string | null> => {
    try {
      const userId = getLoggedInUserId();
      if (!userId) {
        console.warn('No user ID found in localStorage');
        return null;
      }

      // Import the userService
      const { default: userService } = await import('@/services/api/userService');

      // Find user by ID in the assignee list
      // Note: Using the user_group_id from your example API call
      const user = await userService.findUserById(userId, '6877420fd4928f6a37ba1b95');

      console.log('🚀 Searching for user ID in assignee list:', userId);

      if (user && user.value) {
        console.log('🚀 Found logged-in user in assignee list:', user);
        return user.value; // This is the display name like "Alex Morgan"
      } else {
        console.warn('Logged-in user not found in assignee list:', userId);

        // Fallback: Try to create a filter using user's email or a generic name
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser);
          const email = parsedUser.email;

          if (email) {
            // Extract name from email (e.g., "<EMAIL>" -> "Paralegal Account")
            const emailPrefix = email.split('@')[0];
            const cleanName = emailPrefix
              .replace(/_/g, ' ')
              .replace(/\d+/g, '')
              .replace(/account/i, '')
              .trim()
              .split(' ')
              .filter((word: string) => word.length > 0)
              .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
              .join(' ');

            console.log('🚀 Using fallback name from email:', cleanName);
            return cleanName || 'Current User';
          }
        }

        return 'Current User'; // Final fallback
      }
    } catch (error) {
      console.error('Error fetching user name from assignee API:', error);
      return 'Current User'; // Fallback on error
    }
  };

  const handleSelectNotice = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedNotices(prev => [...prev, id]);
    } else {
      setSelectedNotices(prev => prev.filter(noticeId => noticeId !== id));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedNotices(courtNotices.map(notice => notice.id));
    } else {
      setSelectedNotices([]);
    }
  };
  const getInitials = (name: string): string => {
    return name
      ?.split(' ')
      ?.map(word => word[0])
      ?.join('')
      ?.toUpperCase()
      ?.slice(0, 2);
  };
  const handleFilter = () => {
    console.log('Filter button clicked');
  };


  // Helper function to check if field is a matter field
  const isMatterField = (field: string) => {
    const f = (field || '').toLowerCase();
    return f === 'matter' || f === 'matter_name' || f === 'matters';
  };

  const handleFilters = (
    filters: Array<{
      id: string;
      field: string;
      operator: string;
      value: string;
      selectedValues?: string[];
    }>
  ) => {
    console.log('Filters changed', filters);

    // Convert FilterRow format to backend format
    const backendFilters = filters
      .filter(
        f =>
          f.field &&
          f.operator &&
          (f.value || f.selectedValues?.length || f.operator === 'unassigned')
      )
      .map(f => {
        // Map frontend field names to backend field names
        const fieldMap: Record<string, string> = {
          'Workflow Name': 'template_name',
          Name: 'template_name', // Keep for backward compatibility
          Assignee: 'assigned_users',
          Status: 'status',
          Contact: 'contact',
          Matter: 'matter',
          Templates: 'templates',
          'Due date': 'due_date',
          'Create date': 'created_date',
          Attorney: 'attorney',
        };

        // Map frontend operators to backend operators
        const operatorMap: Record<string, string> = {
          contains: 'contains',
          equals: 'equal',
          not_contains: 'not_contains',
          not_equals: 'not_equal',
          unassigned: 'unassigned',
          before: 'before',
          after: 'after',
          between: 'between',
        };

        const fieldName = fieldMap[f.field] || f.field;
        const operator = operatorMap[f.operator] || f.operator;

        // Handle unassigned filter - no values needed
        let value: string[];
        if (f.operator === 'unassigned') {
          value = [];
        } else {
          const rawValues =
            f.selectedValues && f.selectedValues.length > 0
              ? f.selectedValues
              : f.value.split(', ').filter(v => v.trim());

          // Send full matter values for precise filtering (client|matter)
          if (isMatterField(f.field)) {
            value = rawValues; // Keep full values for precise filtering
          } else {
            value = rawValues;
          }
        }

        return {
          fieldName,
          filter: operator,
          value,
        };
      });

    // Final sanitization: keep matter field values as full values for precise filtering
    const sanitizedBackendFilters = backendFilters.map(filter => {
      if (isMatterField(filter.fieldName) && filter.value && Array.isArray(filter.value)) {
        return {
          ...filter,
          value: filter.value, // Keep full values (client|matter) for precise filtering
        };
      }
      return filter;
    });

    setCurrentFilters(sanitizedBackendFilters);
  };

  const handleColumns = (selected?: string[]) => {
    // Map "Workflow Name" to "Name" for internal column handling
    const mappedSelected =
      selected?.map(col => {
        if (col === 'Workflow Name') return 'Name';
        return col;
      }) ?? null;

    setVisibleColumnNames(mappedSelected);

    // Save user's column selection to localStorage
    if (selected && selected.length > 0) {
      saveColumnVisibility(selected);
      console.log('🚀 Saved column visibility to localStorage:', selected);

      // Create a comprehensive list of all possible columns
      const allPossibleColumns = [
        { FieldName: 'Name', visible: true, sortable: true, searchable: true },
        { FieldName: 'View', visible: true, sortable: true, searchable: true },
        { FieldName: 'Started', visible: true, sortable: true, searchable: true },
        { FieldName: 'Due On', visible: true, sortable: true, searchable: true },
        { FieldName: 'Task Completed', visible: true, sortable: true, searchable: true },
        { FieldName: 'Status', visible: true, sortable: true, searchable: true },
        { FieldName: 'Activity', visible: true, sortable: true, searchable: true },
        { FieldName: 'Assignee', visible: true, sortable: true, searchable: true },
        { FieldName: 'Matter', visible: true, sortable: true, searchable: true },
        { FieldName: 'Attorney', visible: true, sortable: true, searchable: true },
        { FieldName: 'Notes', visible: true, sortable: true, searchable: true },
      ];

      // Filter to only include selected columns
      const updatedViewFields = allPossibleColumns.filter(
        col => mappedSelected?.includes(col.FieldName) ?? false
      );

      console.log('🚀 Updated viewFields:', updatedViewFields);
      setViewFields(updatedViewFields);
    } else {
      // Clear localStorage when no columns are selected or when resetting
      clearColumnVisibility();
      console.log('🚀 Cleared column visibility from localStorage');
    }
  };

  const handleSaveView = () => {
    setShowSaveViewModal(true);
  };

  const handleFilteredDataReceived = (filters: unknown) => {
    // Type guard to ensure filters is an array of the expected format
    if (Array.isArray(filters)) {
      const typedFilters = filters as Array<{
        fieldName: string;
        filter: string;
        value: string[];
      }>;

      setAppliedFilters(typedFilters);
      setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page when filtering
    }
  };

  const handleSaveViewSubmit = async (viewName: string) => {
    try {
      setSavingView(true);

      // Get current filters and columns state
      // If no columns are selected, use visibleColumnNames to preserve the selection
      let columnsToSave: Array<{
        FieldName: string;
        searchable: boolean;
        sortable: boolean;
        visible: boolean;
      }>;

      if (visibleColumnNames && visibleColumnNames.length === 0) {
        // Save the fact that no columns are selected
        columnsToSave = [];
      } else if (columns.length > 0) {
        // Use the actual rendered columns
        columnsToSave = columns.map(col => ({
          FieldName: col.header as string,
          searchable: true,
          sortable: col.sortable || false,
          visible: true,
        }));
      } else {
        // Fallback: save visible column names if available
        columnsToSave = (visibleColumnNames || []).map(colName => ({
          FieldName: colName === 'Workflow Name' ? 'Name' : colName,
          searchable: true,
          sortable: true,
          visible: true,
        }));
      }

      // Get the current page type from URL to save the correct type
      const currentPageType = router.query.type as string;

      // Map URL type to API type format
      const getApiTypeFromUrlType = (urlType: string): string => {
        const typeMapping: Record<string, string> = {
          'my-workflow': 'my_work_flow',
          'new-court-notice': 'new-court-notice',
          'court-notice-follow-up': 'court-notice-follow-up',
          completed: 'completed',
          archived: 'archive',
          inbound: 'inbound',
          'my-tasks': 'my_tasks',
        };
        return typeMapping[urlType] || 'my_work_flow'; // Default fallback
      };

      const mappedType = getApiTypeFromUrlType(currentPageType);

      console.log(
        `🚀 Saving view with dynamic type: URL="${currentPageType}" → API="${mappedType}"`
      );

      const viewData = {
        name: viewName,
        type: mappedType, // Dynamic type based on current page
        userId: Number(userId),
        filters: {
          search: searchTerm,
          sortBy,
          sortOrder,
        },
        columns: columnsToSave,
        filter: currentFilters,
        // Add a flag to indicate if no columns were selected
        noColumnsSelected: visibleColumnNames && visibleColumnNames.length === 0,
      };

      const response = await UserViewService.saveUserView(viewData);

      if (response.data.success) {
        console.log('View saved successfully:', response.data.view);
        // Refresh the sidebar views
        const windowWithRefresh = window as unknown as { refreshSidebarViews?: () => void };
        if (windowWithRefresh.refreshSidebarViews) {
          windowWithRefresh.refreshSidebarViews();
        }
        // You could show a success toast here
      } else {
        console.error('Failed to save view:', response.data.message);
        // You could show an error toast here
      }
    } catch (error) {
      console.error('Error saving view:', error);
      // You could show an error toast here
    } finally {
      setSavingView(false);
      setShowSaveViewModal(false);
    }
  };

  const handleSort = (field: string, order: 'asc' | 'desc', columnId?: string) => {
    console.log('handleSort', field, order, 'columnId:', columnId);
    setSortBy(field);
    setSortOrder(order);
    setActiveColumn(columnId || field); // Set the active column for visual indicator
    setPagination({ ...pagination, page: 1 });
  };

  const handlePageChange = (page: number) => {
    setPagination({ ...pagination, page });
  };

  const handleItemsPerPageChange = (limit: number) => {
    setPagination({ page: 1, limit, total: pagination.total });
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  const handleRowClick = (rowData: CourtNoticeItem) => {
    router.push(`/workflowrun?taskId=${rowData?.last_task_id}&work_flow_id=${rowData?._id}`);
  };

  // Effect to set userId from localStorage
  useEffect(() => {
    try {
      const storedUser = localStorage.getItem('user');

      if (storedUser) {
        const parsedUser = JSON.parse(storedUser);

        // Try multiple possible userId fields
        const storedUserId =
          parsedUser.id || parsedUser.userId || parsedUser.user_id || parsedUser._id;

        if (storedUserId) {
          setUserId(Number(storedUserId));
        } else {
          console.error('No userId found in user data:', parsedUser);
        }
      } else {
        console.warn('No user data found in localStorage');
      }
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
    }
  }, []);

  // Effect to load stored column visibility on page load
  useEffect(() => {
    const storedColumns = getStoredColumnVisibility();
    if (storedColumns && storedColumns.length > 0) {
      setVisibleColumnNames(storedColumns);
      console.log('🚀 Loaded stored column visibility on page load:', storedColumns);
    }

    // Note: Keeping localStorage persistent across page refreshes for better UX
    // If you need to clear on page refresh, you can call clearColumnVisibility() here
  }, []);

  const fetchAll = useCallback(
    async (
      filters?: Array<{
        fieldName: string;
        filter: string;
        value: string[];
      }>
    ) => {
      let didRequest = false;
      const type = router.query.type as string;

      // Early return if userId is not available
      if (!userId) {
        console.warn('fetchAll called without userId, skipping API call');
        return;
      }

      // Early return if router is not ready
      if (!router.isReady) {
        console.warn('Router not ready, skipping API call');
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Validate all required parameters
        if (!userId || !type) {
          console.error('Missing required parameters:', { userId, type });
          return;
        }

        // For "my-workflow", we need to get all workflow types with assignee filter applied
        // Since the backend my-workflow type may not be fully implemented, we'll use a broader approach
        let apiType = type;
        if (type === 'my-workflow') {
          // Use new-court-notice as base type but ensure filters are applied
          apiType = 'new-court-notice';
          console.log(
            '🚀 Using new-court-notice type for My Workflows with assignee filter:',
            filters
          );
        }

        // Check if there's a template filter that should override the API type
        // Template filter always takes priority over URL type parameter
        const templateFilter = filters?.find(
          filter =>
            (filter.fieldName === 'templates' || filter.fieldName === 'work_flow_runner') &&
            filter.value &&
            filter.value.length > 0
        );

        if (templateFilter) {
          const originalType = apiType;
          // Map template filter values to API types
          if (templateFilter.value.includes('New Court Notice')) {
            apiType = 'new-court-notice';
          } else if (templateFilter.value.includes('Court Notice Follow Up')) {
            apiType = 'court-notice-follow-up';
          }
          console.log(
            `🚀 Template filter override: ${originalType} → ${apiType}`,
            templateFilter.value
          );
        }

        // Remove template filter from the filters array since it's only used for type determination
        const filtersWithoutTemplate =
          filters?.filter(
            filter => !(filter.fieldName === 'templates' || filter.fieldName === 'work_flow_runner')
          ) || [];

        console.log('🚀 API Call Payload:', {
          page: pagination.page,
          limit: pagination.limit,
          userId,
          view: 'View_1',
          type: apiType,
          sort_by: sortBy,
          sort_order: sortOrder,
          filters: filtersWithoutTemplate,
        });

        didRequest = true;
        const viewRes = await workflowService.workFlowData(
          pagination.page,
          pagination.limit,
          userId,
          'View_1',
          apiType,
          sortBy,
          sortOrder,
          filtersWithoutTemplate
        );
        console.log('🚀 ~ fetchAll ~ viewRes:', viewRes);

        // Handle different possible response structures - API returns nested data
        const responseData =
          (
            viewRes as {
              data?: { data?: unknown; fields?: unknown[]; column?: unknown[]; total?: number };
            }
          )?.data?.data ||
          (viewRes as { data?: unknown })?.data ||
          viewRes;
        const fields = (responseData as { fields?: unknown[] })?.fields || [];
        const columns = (responseData as { column?: unknown[] })?.column || [];
        const total = (responseData as { total?: number })?.total || 0;

        // Map API column format to component format
        const mappedColumns = (
          columns as {
            fieldName: string;
            isShowing: boolean;
            isSorting: boolean;
            isFilter: boolean;
          }[]
        ).map(col => ({
          FieldName: col.fieldName,
          visible: col.isShowing,
          sortable: col.isSorting,
          searchable: col.isFilter,
        }));

        const visibleColumns = mappedColumns.filter((f: TableViewField) => f.visible);
        console.log(
          '🚀 ~ visible columns:',
          visibleColumns.map((c: TableViewField) => c.FieldName)
        );
        setViewFields(visibleColumns);

        // Also set visibleColumnNames for TableActions component to show correct count
        // Map API field names to TableActions display names
        const apiToDisplayNameMap: Record<string, string> = {
          template_name: 'Name',
          assigned_users: 'Assignee',
          start_date: 'Started',
          end_date: 'Due On',
          completed_tasks: 'Task Completed',
          status: 'Status',
          last_activity: 'Activity',
          matter: 'Matter',
          attorney: 'Attorney',
          view: 'View',
          notes: 'Notes',
          note: 'Notes',
          description: 'Notes',
          auto_generated_execution: 'Notes',
          // Add common variations and exact matches
          Notes: 'Notes',
          Activity: 'Activity',
          'Due On': 'Due On',
          'DUE ON': 'Due On',
          due_on: 'Due On',
          due: 'Due On',
          Due: 'Due On',
          // Handle other possible field names
          'Task Completed': 'Task Completed',
          'TASK COMPLETED': 'Task Completed',
          task_completed: 'Task Completed',
          Started: 'Started',
          STARTED: 'Started',
          Name: 'Name',
          NAME: 'Name',
          Status: 'Status',
          STATUS: 'Status',
          Assignee: 'Assignee',
          ASSIGNEE: 'Assignee',
          // Add any other mappings as needed
        };

        const columnNames = visibleColumns.map((c: TableViewField) => {
          // Map API field name to display name, fallback to original if no mapping exists
          let mapped = apiToDisplayNameMap[c.FieldName] || c.FieldName;

          // Special handling for due-related fields
          const fieldLower = c.FieldName.toLowerCase();
          if (
            fieldLower.includes('due') ||
            fieldLower.includes('end_date') ||
            fieldLower === 'due' ||
            fieldLower === 'end'
          ) {
            mapped = 'Due On';
            console.log(`🚀 DUE FIELD SPECIAL MAPPING: API field "${c.FieldName}" -> "${mapped}"`);
          }

          if (c.FieldName.toLowerCase().includes('due') || mapped.toLowerCase().includes('due')) {
            console.log(`🚀 DUE FIELD MAPPING: API field "${c.FieldName}" -> "${mapped}"`);
          }
          return mapped;
        });
        console.log('🚀 Final columnNames for TableActions:', columnNames);

        // Check if user has explicit column preferences in localStorage
        const apiStoredColumns = getStoredColumnVisibility();
        if (apiStoredColumns && apiStoredColumns.length > 0) {
          console.log('🚀 Using stored column preferences:', apiStoredColumns);
          // Use stored preferences, but map "Workflow Name" to "Name" internally
          const mappedStoredColumns = apiStoredColumns.map(col => {
            if (col === 'Workflow Name') return 'Name';
            return col;
          });
          setVisibleColumnNames(apiStoredColumns); // Keep original names for TableActions

          // Also update viewFields to match stored preferences
          const allPossibleColumns = [
            { FieldName: 'Name', visible: true, sortable: true, searchable: true },
            { FieldName: 'Started', visible: true, sortable: true, searchable: true },
            { FieldName: 'Due On', visible: true, sortable: true, searchable: true },
            { FieldName: 'Task Completed', visible: true, sortable: true, searchable: true },
            { FieldName: 'Status', visible: true, sortable: true, searchable: true },
            { FieldName: 'Activity', visible: true, sortable: true, searchable: true },
            { FieldName: 'Assignee', visible: true, sortable: true, searchable: true },
            { FieldName: 'Matter', visible: true, sortable: true, searchable: true },
            { FieldName: 'Attorney', visible: true, sortable: true, searchable: true },
            { FieldName: 'View', visible: true, sortable: true, searchable: true },
            { FieldName: 'Notes', visible: true, sortable: true, searchable: true },
          ];
          const storedViewFields = allPossibleColumns.filter(col =>
            mappedStoredColumns.includes(col.FieldName)
          );
          setViewFields(storedViewFields);
          console.log('🚀 Applied stored column preferences to viewFields:', storedViewFields);
        } else {
          // No stored preferences, use API response
          console.log('🚀 No stored preferences, using API column response');
          setVisibleColumnNames(columnNames);
        }

        const transformedData: CourtNoticeItem[] = (
          fields as {
            id: string;
            assigned_users: string;
            work_flow_runner: string;
            start_date: string;
            end_date: string;
            status: string;
            last_activity: string;
            notes?: string;
            attorney?: string;
            matter?: string;
            template_name?: string;
            client_name?: string;
            completed_tasks?: string;
            last_task_id?: string;
          }[]
        ).map(field => ({
          id: field.id,
          _id: field.id,
          assigned_users: field.assigned_users,
          workflowRun: field.work_flow_runner,
          started: field.start_date,
          due: field.end_date,
          status: (() => {
            if (field.status === 'ON_TRACK') return 'On Track';
            if (field.status === 'DUE_SOON') return 'DUE SOON';
            if (field.status === 'OVERDUE') return 'OVERDUE';
            return 'Completed';
          })(),
          activity: field.last_activity,
          assignee: field.assigned_users,
          notes: field.notes,
          work_flow_runner: field.work_flow_runner,
          start_date: field.start_date,
          attorney: field.attorney,
          matter: field.matter,
          templates: field.template_name,
          contact: field.client_name,
          task_completed: field.completed_tasks,
          view: [],
          last_activity: field.last_activity,
          work_flow_name: field.template_name,
          end_date: field.end_date,
          last_task_id: field.last_task_id,
        }));
        // Get user's stored column preferences
        const finalStoredColumns = getStoredColumnVisibility();

        // Check if current filters include Matter or Attorney (requirement #3)
        const hasFilterOnMatterOrAttorney =
          filters &&
          filters.some(filter => {
            const fieldName = filter.fieldName?.toLowerCase();
            return fieldName === 'matter' || fieldName === 'attorney';
          });

        let finalViewFields: TableViewField[];

        if (finalStoredColumns && finalStoredColumns.length > 0) {
          // User has explicit column selections - merge with API data but respect user choices
          console.log('🚀 Using stored column preferences:', finalStoredColumns);

          // Create a map of all available columns from API
          const columnMap = new Map(mappedColumns.map(col => [col.FieldName, col]));

          // Build final columns based on user selection, maintaining API metadata
          const columnsToShow = [...finalStoredColumns];

          // Add Matter/Attorney if they're being filtered on (requirement #3)
          if (hasFilterOnMatterOrAttorney) {
            if (
              filters?.some(f => f.fieldName?.toLowerCase() === 'matter') &&
              !columnsToShow.some(c => c.toLowerCase() === 'matter')
            ) {
              columnsToShow.push('Matter');
            }
            if (
              filters?.some(f => f.fieldName?.toLowerCase() === 'attorney') &&
              !columnsToShow.some(c => c.toLowerCase() === 'attorney')
            ) {
              columnsToShow.push('Attorney');
            }
            console.log('🚀 Added filtered columns:', columnsToShow);
          }

          finalViewFields = columnsToShow
            .map(columnName => {
              const apiColumn = columnMap.get(columnName);
              if (apiColumn) {
                return apiColumn;
              }
              // For columns not in API response (like Matter, Attorney), create default
              return {
                FieldName: columnName,
                visible: true,
                sortable: true,
                searchable: true,
              };
            })
            .filter(Boolean) as TableViewField[];

          console.log(
            '🚀 Final columns with user preferences:',
            finalViewFields.map(c => c.FieldName)
          );
        } else {
          // No user preferences - use API defaults but ensure Matter/Attorney are hidden by default
          const apiDefaults = mappedColumns.filter((f: TableViewField) => f.visible);
          finalViewFields = apiDefaults.filter(col => {
            // Hide Matter and Attorney by default (requirement #1)
            const normalizedFieldName = col.FieldName.toLowerCase();
            return !['matter', 'attorney'].includes(normalizedFieldName);
          });

          // However, if there are filters on Matter/Attorney, show those columns (requirement #3)
          if (hasFilterOnMatterOrAttorney) {
            const allAvailableColumns = mappedColumns;
            const columnMap = new Map(
              allAvailableColumns.map(col => [col.FieldName.toLowerCase(), col])
            );

            if (filters?.some(f => f.fieldName?.toLowerCase() === 'matter')) {
              const matterColumn = columnMap.get('matter') || {
                FieldName: 'Matter',
                visible: true,
                sortable: true,
                searchable: true,
              };
              if (!finalViewFields.some(c => c.FieldName.toLowerCase() === 'matter')) {
                finalViewFields.push(matterColumn);
              }
            }

            if (filters?.some(f => f.fieldName?.toLowerCase() === 'attorney')) {
              const attorneyColumn = columnMap.get('attorney') || {
                FieldName: 'Attorney',
                visible: true,
                sortable: true,
                searchable: true,
              };
              if (!finalViewFields.some(c => c.FieldName.toLowerCase() === 'attorney')) {
                finalViewFields.push(attorneyColumn);
              }
            }

            console.log(
              '🚀 Added filtered columns to defaults:',
              finalViewFields.map(c => c.FieldName)
            );
          }

          console.log(
            '🚀 Using API defaults (with filter logic):',
            finalViewFields.map(c => c.FieldName)
          );
        }

        setViewFields(finalViewFields);

        console.log('🚀 ~ transformed data:', transformedData.length, 'items');
        setCourtNotices(transformedData);
        setPagination(prev => ({ ...prev, total }));
      } catch (e) {
        setError(e instanceof Error ? e.message : 'Failed to load workflows');
      } finally {
        if (didRequest) {
          setHasFetched(true);
        }
        setLoading(false);
      }
    },
    [
      userId,
      pagination.page,
      pagination.limit,
      router.isReady,
      router.query.type,
      sortBy,
      sortOrder,
    ]
  );

  /**
   * Effect to automatically set default filter for "My Workflows" page
   * When user navigates to My Workflows, automatically filters by logged-in user's assignee
   * Filter: Assignee > Contains > [Logged in user's name from API]
   */
  useEffect(() => {
    const initializeMyWorkflow = async () => {
      console.log(
        '🚀 setDefaultAssigneeFilter called - router ready:',
        router.isReady,
        'type:',
        router.query.type,
        'userId:',
        userId,
        'defaultFilterSet:',
        defaultFilterSet
      );

      if (router.isReady && userId) {
        if (router.query.type === 'my-workflow') {
          console.log('🚀 Processing My Workflows default filter...');
          if (!defaultFilterSet) {
            try {
              const loggedInUserName = await getLoggedInUserNameFromAPI();
              if (loggedInUserName) {
                const defaultFilter = {
                  fieldName: 'assigned_users', // Maps to "Assignee" field in UI
                  filter: 'contains', // "Contains" operator
                  value: [loggedInUserName], // Logged-in user's display name from API
                };

                console.log('🚀 Setting default assignee filter for My Workflows:', defaultFilter);
                console.log('🚀 Before setting - currentFilters:', currentFilters);
                console.log('🚀 Before setting - appliedFilters:', appliedFilters);

                // Set filters and make API call in one go
                setCurrentFilters([defaultFilter]);
                setAppliedFilters([defaultFilter]);
                setDefaultFilterSet(true);

                // Make the single API call with the filter
                await fetchAll([defaultFilter]);
              } else {
                console.warn('Could not get logged-in user name from API');
              }
            } catch (error) {
              console.error('Error initializing My Workflows:', error);
            }
          }
        } else {
          // Clear filters and reset default filter flag when not on My Workflows page
          const type = router.query.type as string;
          if (
            defaultFilterSet &&
            type !== 'new-court-notice' &&
            type !== 'court-notice-follow-up' &&
            type !== 'completed'
          ) {
            console.log('🚀 Clearing filters when leaving My Workflows to non-default-filter page');
            setCurrentFilters([]);
            setAppliedFilters([]);
            setDefaultFilterSet(false);
          }
        }
      }
    };

    initializeMyWorkflow();
  }, [
    router.isReady,
    router.query.type,
    userId,
    defaultFilterSet,
    fetchAll,
    getLoggedInUserNameFromAPI,
  ]);

  // Separate effect for other workflow types and pagination/sorting changes
  useEffect(() => {
    if (userId && router.isReady && router.query.type !== 'my-workflow') {
      // Check if this is a court notice page that needs default template filter
      const type = router.query.type as string;

      if ((type === 'new-court-notice' || type === 'court-notice-follow-up') && !defaultFilterSet) {
        // Apply default template filter based on URL type
        const templateName =
          type === 'new-court-notice' ? 'New Court Notice' : 'Court Notice Follow Up';
        const defaultTemplateFilter = {
          fieldName: 'templates', // Maps to "Templates" field in UI
          filter: 'contains', // "Contains" operator
          value: [templateName], // Template name based on URL type
        };

        console.log(`🚀 Setting default template filter for ${type}:`, defaultTemplateFilter);

        // Set filters and make API call
        setCurrentFilters([defaultTemplateFilter]);
        setAppliedFilters([defaultTemplateFilter]);
        setDefaultFilterSet(true);

        // Make the API call with the template filter
        fetchAll([defaultTemplateFilter]);
      } else if (type === 'completed' && !defaultFilterSet) {
        // Apply default status filter for completed page
        const defaultStatusFilter = {
          fieldName: 'status', // Maps to "Status" field in UI
          filter: 'contains', // "Contains" operator
          value: ['Completed'], // Status value for completed workflows
        };

        console.log(`🚀 Setting default status filter for ${type}:`, defaultStatusFilter);

        // Set filters and make API call
        setCurrentFilters([defaultStatusFilter]);
        setAppliedFilters([defaultStatusFilter]);
        setDefaultFilterSet(true);

        // Make the API call with the status filter
        fetchAll([defaultStatusFilter]);
      } else if (
        type !== 'new-court-notice' &&
        type !== 'court-notice-follow-up' &&
        type !== 'completed'
      ) {
        // For other workflow types, clear any default filters and make normal API call
        if (defaultFilterSet) {
          console.log('🚀 Clearing default filters for non-court-notice page');
          setCurrentFilters([]);
          setAppliedFilters([]);
          setDefaultFilterSet(false);
        }
        fetchAll(appliedFilters.length > 0 ? appliedFilters : undefined);
      } else if (defaultFilterSet) {
        // For court notice pages after default filter is set, use applied filters
        fetchAll(appliedFilters.length > 0 ? appliedFilters : undefined);
      }
    } else if (
      userId &&
      router.isReady &&
      router.query.type === 'my-workflow' &&
      defaultFilterSet
    ) {
      // For myworkflow, only make additional calls if filters change after initial load
      // Skip if this is the initial load (already handled above)
      if (appliedFilters.length > 0) {
        fetchAll(appliedFilters);
      }
    } else {
      console.log(' Waiting for userId or router to be ready...', {
        userId,
        routerReady: router.isReady,
        type: router.query.type,
        defaultFilterSet,
      });
    }
  }, [
    pagination.page,
    pagination.limit,
    userId,
    router.isReady,
    router.query.type,
    appliedFilters,
    sortBy,
    sortOrder,
    fetchAll,
    defaultFilterSet,
  ]);

  const columns: Column[] = useMemo(() => {
    // If no columns are selected (empty array), return empty columns array
    if (visibleColumnNames && visibleColumnNames.length === 0) {
      console.log('🚀 No columns selected, returning empty array');
      return [];
    }

    const toId = (label: string) => label.toLowerCase().replace(/\s+/g, '_');

    const normalize = (label: string) => {
      const l = label.trim().toLowerCase().replace(/[_#]/g, ' ').replace(/\s+/g, ' ');
      if (l === 'workflow name' || l === 'name') return 'name';
      if (l === 'due' || l === 'due on') return 'due on';
      if (l === 'task completed' || l === 'tasks completed') return 'task completed';
      if (l === 'attorney' || l === 'attorney') return 'attorney';
      if (l === 'matter') return 'matter';
      return l;
    };

    const mapCell = (fieldName: string): Column['cell'] => {
      const lower = normalize(fieldName);
      const CellComponent = (row: CourtNoticeItem) => {
        if (lower === 'name' || lower === 'workflow name') {
          // Parse comma-separated client names and apply intelligent formatting
          const clientNames = row.work_flow_runner
            ? row.work_flow_runner
                .split(',')
                .map(name => name.trim())
                .filter(Boolean)
            : [];
          const displayName = formatWorkflowName(clientNames, 60);

          return (
            <span
              className="text-[14px] text-[#2A2E34] cursor-pointer"
              onClick={() => handleRowClick(row)}
              title={row.work_flow_runner} // Show full text on hover
            >
              {displayName}
            </span>
          );
        }
        if (lower === 'view') {
          // Parse comma-separated client names and apply intelligent formatting
          const clientNames = row.work_flow_runner
            ? row.work_flow_runner
                .split(',')
                .map(name => name.trim())
                .filter(Boolean)
            : [];
          const displayName = formatWorkflowName(clientNames, 60);

          return (
            <span
              className="text-[14px] cursor-pointer"
              onClick={() => handleRowClick(row)}
              title={row.work_flow_runner} // Show full text on hover
            >
              {displayName}
            </span>
          );
        }
        if (lower === 'started')
          return (
            <span className="text-[14px] cursor-pointer" onClick={() => handleRowClick(row)}>
              {row.started}
            </span>
          );
        if (lower === 'due on' || lower === 'due')
          return (
            <span className="text-[14px] cursor-pointer" onClick={() => handleRowClick(row)}>
              {row.due}
            </span>
          );
        if (
          lower === 'task completed' ||
          lower === '#tasks completed' ||
          lower === 'task_completed'
        )
          return (
            <span className="text-[14px] cursor-pointer" onClick={() => handleRowClick(row)}>
              {row.task_completed}
            </span>
          );
        if (lower === 'status')
          return (
            <div onClick={() => handleRowClick(row)} className="cursor-pointer">
              <StatusBadge status={mapStatusVariant(row.status)} variant="fill" />
            </div>
          );
        if (lower === 'activity')
          return (
            <span className="text-[14px] cursor-pointer" onClick={() => handleRowClick(row)}>
              {row.activity}
            </span>
          );
        if (lower === 'assignee') {
          const assignedUsers =
            row?.assignee && typeof row.assignee === 'string' && row.assignee.trim() !== ''
              ? row?.assignee
                  ?.split(',')
                  .map((s: string) => s.trim())
                  .filter((s: string) => s.length > 0)
              : [];
          const userCount = assignedUsers.length || 0;
          const firstUser = assignedUsers[0];

          // Show AI robot icon if no assignee
          if (userCount === 0 || !firstUser || firstUser.trim() === '') {
            return (
              <div
                className="w-[24px] h-[24px] cursor-pointer flex items-center justify-center"
                onClick={() => handleRowClick(row)}
              >
                <img src="/assets/ai-robot.svg" alt="AI Robot" className="w-[24px] h-[24px]" />
              </div>
            );
          }

          return (
            <div
              className="relative w-[24px] h-[24px] cursor-pointer"
              onClick={() => handleRowClick(row)}
            >
              <div className="w-[24px] h-[24px] rounded-full bg-[#5F6F84] text-white flex items-center justify-center font-semibold text-[10px]">
                {getInitials(firstUser)}
              </div>
              <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                {userCount}
              </span>
            </div>
          );
        }

        if (lower === 'notes') return <span className="text-[14px]">{row.notes}</span>;
        if (lower === 'matter') return <span className="text-[14px]">{row.matter}</span>;
        if (lower === 'attorney') return <span className="text-[14px]">{row.attorney}</span>;
        return <span className="text-[14px]"></span>;
      };
      CellComponent.displayName = `CellComponent_${fieldName}`;
      return CellComponent;
    };

    const built = viewFields.map(f => {
      const lower = f.FieldName.toLowerCase();
      const isTaskCompletedHeader =
        lower === 'task completed' || lower === '#tasks completed' || lower === 'task_completed';

      // Map frontend field names to backend field names for sorting
      const getSortField = (fieldName: string) => {
        const normalized = fieldName.toLowerCase().trim();
        switch (normalized) {
          case 'name':
          case 'workflow name':
            return 'work_flow_runner';
          case 'view':
            return 'work_flow_runner';
          case 'assignee':
            return 'assigned_users';
          case 'status':
            return 'status';
          case 'activity':
            return 'last_activity';
          case 'started':
            return 'start_date';
          case 'due on':
          case 'due':
            return 'end_date';
          case 'task completed':
          case '#tasks completed':
          case 'task_completed':
            return 'completed_tasks';
          case 'matter':
            return 'matter';
          case 'attorney':
            return 'attorney';
          default:
            return toId(fieldName);
        }
      };

      // Map the field name to display name for header
      const getDisplayHeader = (fieldName: string) => {
        const headerMap: Record<string, string> = {
          template_name: 'Name',
          'template name': 'Name',
          'Template Name': 'Name',
          'TEMPLATE NAME': 'Name',
          assigned_users: 'Assignee',
          start_date: 'Started',
          end_date: 'Due On',
          completed_tasks: 'Task Completed',
          status: 'Status',
          last_activity: 'Activity',
          matter: 'Matter',
          attorney: 'Attorney',
          view: 'View',
          notes: 'Notes',
          // Fallback mappings
          'workflow name': 'Name',
          'Workflow Name': 'Name',
          'WORKFLOW NAME': 'Name',
        };

        return headerMap[fieldName] || headerMap[fieldName.toLowerCase()] || fieldName.trim();
      };

      return {
        id: toId(f.FieldName),
        header: getDisplayHeader(f.FieldName),
        sortable: f.sortable,
        cell: mapCell(f.FieldName),
        className: isTaskCompletedHeader ? 'whitespace-nowrap min-w-[200px]' : undefined,
        sortField: getSortField(f.FieldName),
      };
    });

    console.log(
      '🚀 ~ built columns:',
      built.map(c => c.header)
    );

    if (visibleColumnNames && visibleColumnNames.length > 0) {
      console.log('🚀 ~ visibleColumnNames received:', visibleColumnNames);
      const normalizedSelected = new Set(visibleColumnNames.map(normalize));
      console.log('🚀 ~ normalizedSelected:', Array.from(normalizedSelected));
      console.log(
        '🚀 ~ built columns headers:',
        built.map(c => c.header)
      );
      console.log(
        '🚀 ~ built columns normalized:',
        built.map(c => normalize(String(c.header)))
      );

      const filtered = built.filter(
        col =>
          typeof col.header === 'string' && normalizedSelected.has(normalize(String(col.header)))
      );
      console.log(
        '🚀 ~ filtered columns:',
        filtered.map(c => c.header)
      );
      return filtered;
    }
    console.log(
      '🚀 ~ returning all built columns:',
      built.map(c => c.header)
    );
    return built;
  }, [viewFields, visibleColumnNames]);

  // Get dynamic page info based on query type
  const pageInfo = getPageInfo(router.query.type);

  return (
    <Layout>
      <Head>
        <title>{pageInfo.title}</title>
        <meta
          name="description"
          content={`Manage ${pageInfo.header.toLowerCase()} in FirmProfit`}
        />
      </Head>
      <div className={`flex ml-0 h-full overflow-hidden ${poppins.className}`}>
        <WorkflowSidebar className="flex-shrink-0" />
        <div className="flex-1 overflow-auto">
          <div className="px-[30px] py-[32px]">
            <PageHeader
              title={pageInfo.header}
              searchValue={searchTerm}
              onSearchChange={setSearchTerm}
              onFilter={handleFilter}
              onColumns={handleColumns}
              onSaveView={handleSaveView}
              disableSaveView={visibleColumnNames?.length === 0}
              savedFilters={currentFilters}
              onFiltersChange={handleFilters}
              onFilteredDataReceived={handleFilteredDataReceived}
              visibleColumns={
                visibleColumnNames?.map(col => (col === 'Name' ? 'Workflow Name' : col)) || []
              }
            />

            {!loading && !error && visibleColumnNames && visibleColumnNames.length === 0 ? (
              // Show message when no columns are selected
              <div className="mt-4 border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-white py-12 text-center">
                  <div className="text-gray-500 text-lg mb-2">No columns selected</div>
                  <div className="text-gray-400 text-sm">
                    Please select at least one column to view the data
                  </div>
                </div>
              </div>
            ) : !loading && !error && hasFetched && courtNotices.length === 0 ? (
              // Show message when no data is found but columns are selected
              <div className="mt-4 border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-50 border-b border-gray-200">
                  <div className="flex items-center px-6 py-3">
                    <input type="checkbox" disabled className="mr-4 opacity-50" />
                    {columns.map((column, index) => (
                      <div key={index} className="flex-1 text-sm font-medium text-gray-700 px-3">
                        {column.header}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="bg-white py-12 text-center">
                  <div className="text-gray-500 text-lg">No record found</div>
                </div>
              </div>
            ) : (
              <DataTable
                columns={columns}
                data={courtNotices}
                selectedIds={selectedNotices}
                onSelectRow={handleSelectNotice}
                onSelectAll={handleSelectAll}
                isAllSelected={
                  selectedNotices.length === courtNotices.length && courtNotices.length > 0
                }
                isLoading={loading}
                currentSortBy={activeColumn || sortBy}
                currentSortOrder={sortOrder}
                showHeader={true}
                onSort={handleSort}
                idField="id"
                className="mt-4"
              />
            )}
            {!loading &&
              courtNotices.length > 0 &&
              !(visibleColumnNames && visibleColumnNames.length === 0) && (
                <Pagination
                  currentPage={pagination.page}
                  totalPages={totalPages}
                  totalItems={pagination.total}
                  itemsPerPage={pagination.limit}
                  onPageChange={handlePageChange}
                  onItemsPerPageChange={handleItemsPerPageChange}
                  className="mt-6"
                />
              )}
          </div>
        </div>
      </div>

      <SaveViewModal
        isOpen={showSaveViewModal}
        onClose={() => setShowSaveViewModal(false)}
        onSave={handleSaveViewSubmit}
        isLoading={savingView}
      />
    </Layout>
  );
};

export default CourtNoticePage;
