import React from 'react';
import Head from 'next/head';
import { DataTable, PageHeader, Column } from '@/components/common';
import Layout from '@/components/layout/Layout';
import ContactsSidebar from '@/components/layout/ContactsSidebar';
// import { ContactStatus } from '@/types/contact';
import { useContacts } from '@/hooks/useContacts';
import { Poppins } from 'next/font/google';
// import { useRouter } from 'next/router';

// Font setup
const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
});

const ContactsPage: React.FC = () => {
  // const router = useRouter();

  // Use the custom contacts hook
  const {
    filteredContacts,
    selectedContacts,
    loading,
    error,
    searchTerm,
    setSearchTerm,
    selectContact,
    selectAllContacts,
    isAllSelected,
  } = useContacts({
    initialLimit: 50,
    autoLoad: true,
  });

  // Status badge mapping for contact statuses
  // const getStatusBadgeProps = (status: ContactStatus | string) => {
  //   return status;
  // };

  // Enhanced sync status badge component
  // const SyncStatusBadge = ({ syncStatus }: { syncStatus: string }) => {
  //   const getSyncStatusStyle = (status: string) => {
  //     const normalizedStatus = status?.toLowerCase();

  //     switch (normalizedStatus) {
  //       case 'synced':
  //         return {
  //           backgroundColor: '#8CF1BD', // Light green background
  //           color: '#2A2E34', // Dark green text
  //         };
  //       case 'manual':
  //         return {
  //           backgroundColor: '#97C7FF', // Light blue background
  //           color: '#2A2E34', // Dark blue text
  //         };
  //       case 'pending':
  //         return {
  //           backgroundColor: '#FEF3C7', // Light yellow background
  //           color: '#92400E', // Dark yellow/orange text
  //         };
  //       case 'error':
  //       case 'failed':
  //         return {
  //           backgroundColor: '#FEE2E2', // Light red background
  //           color: '#991B1B', // Dark red text
  //         };
  //       default:
  //         return {
  //           backgroundColor: '#F3F4F6', // Light gray background
  //           color: '#374151', // Dark gray text
  //         };
  //     }
  //   };

  //   const style = getSyncStatusStyle(syncStatus);

  //   return (
  //     <span
  //       className="inline-flex items-center justify-center w-[100px] px-3 py-1 rounded-full text-[12px] text-center"
  //       style={{
  //         backgroundColor: style.backgroundColor,
  //         color: style.color,
  //       }}
  //     >
  //       {syncStatus || 'Unknown'}
  //     </span>
  //   );
  // };

  // Handle contact name click
  // const handleContactClick = (contactId: string) => {
  //   router.push(`/contacts/${contactId}`);
  // };

  // Define table columns
  const columns: Column[] = [];
  
  // Event handlers
  const handleFilter = () => {
    // Implement filter functionality
    console.log('Filter clicked');
  };

  const handleColumns = () => {
    // Implement column selection functionality
    console.log('Columns clicked');
  };

  const handleSaveView = () => {
    // Implement save view functionality
    console.log('Save view clicked');
  };

  return (
    <>
      <Head>
        <title>Contacts - FirmProfit</title>
        <meta name="description" content="Manage and view contacts in FirmProfit" />
      </Head>
      <Layout>
        <div className={`flex ml-0 h-full overflow-hidden ${poppins.className}`}>
          <ContactsSidebar />
          <div className="flex-1 overflow-auto">
            <div className="p-6">
              {/* Page Header */}
              <PageHeader
                title="Contact List"
                searchValue={searchTerm}
                onSearchChange={setSearchTerm}
                onFilter={handleFilter}
                onColumns={handleColumns}
                onSaveView={handleSaveView}
                showFilter={false}
                showColumns={false}
                showSaveView={false}
              />

              {/* Error Display */}
              {error && (
                <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-[12px] border border-red-200">
                  {error}
                </div>
              )}

              {/* Data Table */}
              <DataTable
                columns={columns}
                showCheckbox={false}
                data={filteredContacts}
                selectedIds={selectedContacts}
                onSelectRow={selectContact}
                onSelectAll={selectAllContacts}
                isAllSelected={isAllSelected}
                className="mt-4"
                isLoading={loading}
                idField="id"
                rowClassName="hover:bg-gray-50 transition-colors"
              />

              {/* Results Summary */}
              {!loading && (
                <div className="mt-4 text-sm text-[#5F6F84]">
                  {/* Showing {filteredContacts.length} of {totalContacts} contacts */}
                  {selectedContacts.length > 0 && (
                    <span className="ml-2">
                      • {selectedContacts.length} selected
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default ContactsPage;